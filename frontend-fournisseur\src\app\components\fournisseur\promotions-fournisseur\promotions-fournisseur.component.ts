import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  PromotionGestionService,
  PromotionGestionDto,
  PromotionFilterDto,
  PromotionCreateDto,
  PromotionUpdateDto,
  PromotionStatsDto,
  TypePromotionGestion
} from '../../../services/promotion-gestion.service';

@Component({
  selector: 'app-promotions-fournisseur',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './promotions-fournisseur.component.html',
  styleUrls: ['./promotions-fournisseur.component.scss']
})
export class PromotionsFournisseurComponent implements OnInit {
  promotions: PromotionGestionDto[] = [];
  stats: PromotionStatsDto | null = null;
  loading = false;
  error: string | null = null;

  // Filtres
  filter: PromotionFilterDto = {
    page: 1,
    pageSize: 10,
    sortBy: 'dateCreation',
    sortDesc: true
  };

  // Énumérations pour le template
  TypePromotionGestion = TypePromotionGestion;

  // Modales
  showCreateModal = false;
  showEditModal = false;
  selectedPromotion: PromotionGestionDto | null = null;

  // Formulaires
  createForm: FormGroup;
  editForm: FormGroup;

  constructor(
    private promotionService: PromotionGestionService,
    private fb: FormBuilder
  ) {
    this.createForm = this.createPromotionForm();
    this.editForm = this.createPromotionForm();
  }

  ngOnInit(): void {
    this.loadPromotions();
    this.loadStats();
  }

  private createPromotionForm(): FormGroup {
    return this.fb.group({
      code: ['', [Validators.required, Validators.maxLength(100)]],
      nom: ['', [Validators.required, Validators.maxLength(200)]],
      description: ['', [Validators.maxLength(1000)]],
      type: [TypePromotion.Pourcentage, [Validators.required]],
      valeur: [0, [Validators.required, Validators.min(0), Validators.max(100)]],
      dateDebut: ['', [Validators.required]],
      dateFin: ['', [Validators.required]],
      utilisationsMax: [null],
      montantMinimum: [null, [Validators.min(0)]],
      produitId: [null]
    });
  }

  loadPromotions(): void {
    this.loading = true;
    this.error = null;

    this.promotionService.getPromotionsFournisseur(this.filter).subscribe({
      next: (data) => {
        this.promotions = data;
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des promotions:', error);
        this.error = 'Erreur lors du chargement des promotions';
        this.loading = false;
      }
    });
  }

  loadStats(): void {
    this.promotionService.getPromotionStatsFournisseur().subscribe({
      next: (stats) => {
        this.stats = stats;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des statistiques:', error);
      }
    });
  }

  onFilterChange(): void {
    this.filter.page = 1;
    this.loadPromotions();
  }

  onSortChange(sortBy: string): void {
    if (this.filter.sortBy === sortBy) {
      this.filter.sortDesc = !this.filter.sortDesc;
    } else {
      this.filter.sortBy = sortBy;
      this.filter.sortDesc = true;
    }
    this.loadPromotions();
  }

  // Gestion des modales
  openCreateModal(): void {
    this.showCreateModal = true;
    this.createForm.reset();
    this.createForm.patchValue({
      type: TypePromotionGestion.Pourcentage,
      valeur: 0
    });
  }

  closeCreateModal(): void {
    this.showCreateModal = false;
    this.createForm.reset();
  }

  openEditModal(promotion: PromotionGestionDto): void {
    this.selectedPromotion = promotion;
    this.showEditModal = true;
    
    this.editForm.patchValue({
      nom: promotion.nom,
      description: promotion.description,
      valeur: promotion.valeur,
      dateDebut: this.formatDateForInput(promotion.dateDebut),
      dateFin: this.formatDateForInput(promotion.dateFin),
      estActive: promotion.estActive,
      utilisationsMax: promotion.utilisationsMax,
      montantMinimum: promotion.montantMinimum
    });
  }

  closeEditModal(): void {
    this.showEditModal = false;
    this.selectedPromotion = null;
    this.editForm.reset();
  }

  // Actions CRUD
  createPromotion(): void {
    if (this.createForm.valid) {
      const dto: PromotionCreateDto = {
        ...this.createForm.value,
        dateDebut: new Date(this.createForm.value.dateDebut),
        dateFin: new Date(this.createForm.value.dateFin)
      };

      this.promotionService.createPromotion(dto).subscribe({
        next: (promotion) => {
          this.promotions.unshift(promotion);
          this.closeCreateModal();
          this.loadStats();
        },
        error: (error) => {
          console.error('Erreur lors de la création:', error);
          this.error = 'Erreur lors de la création de la promotion';
        }
      });
    }
  }

  updatePromotion(): void {
    if (this.editForm.valid && this.selectedPromotion) {
      const dto: PromotionUpdateDto = {
        ...this.editForm.value,
        dateDebut: new Date(this.editForm.value.dateDebut),
        dateFin: new Date(this.editForm.value.dateFin)
      };

      this.promotionService.updatePromotion(this.selectedPromotion.id, dto).subscribe({
        next: (promotion) => {
          const index = this.promotions.findIndex(p => p.id === promotion.id);
          if (index !== -1) {
            this.promotions[index] = promotion;
          }
          this.closeEditModal();
          this.loadStats();
        },
        error: (error) => {
          console.error('Erreur lors de la mise à jour:', error);
          this.error = 'Erreur lors de la mise à jour de la promotion';
        }
      });
    }
  }

  deletePromotion(promotion: PromotionGestionDto): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la promotion "${promotion.nom}" ?`)) {
      this.promotionService.deletePromotion(promotion.id).subscribe({
        next: () => {
          this.promotions = this.promotions.filter(p => p.id !== promotion.id);
          this.loadStats();
        },
        error: (error) => {
          console.error('Erreur lors de la suppression:', error);
          this.error = 'Erreur lors de la suppression de la promotion';
        }
      });
    }
  }

  togglePromotion(promotion: PromotionGestionDto): void {
    this.promotionService.togglePromotion(promotion.id).subscribe({
      next: (updatedPromotion) => {
        const index = this.promotions.findIndex(p => p.id === updatedPromotion.id);
        if (index !== -1) {
          this.promotions[index] = updatedPromotion;
        }
        this.loadStats();
      },
      error: (error) => {
        console.error('Erreur lors du changement d\'état:', error);
        this.error = 'Erreur lors du changement d\'état de la promotion';
      }
    });
  }

  // Validation du code
  checkCodeUniqueness(): void {
    const code = this.createForm.get('code')?.value;
    if (code && code.length >= 3) {
      this.promotionService.checkCodeExists(code).subscribe({
        next: (result) => {
          if (result.exists) {
            this.createForm.get('code')?.setErrors({ 'codeExists': true });
          }
        },
        error: (error) => {
          console.error('Erreur lors de la vérification du code:', error);
        }
      });
    }
  }

  // Méthodes utilitaires
  getTypeLibelle(type: TypePromotion): string {
    return this.promotionService.getTypeLibelle(type);
  }

  getTypeIcon(type: TypePromotion): string {
    return this.promotionService.getTypeIcon(type);
  }

  getTypeColor(type: TypePromotion): string {
    return this.promotionService.getTypeColor(type);
  }

  getStatutColor(promotion: PromotionGestionDto): string {
    return this.promotionService.getStatutColor(promotion.estActive, promotion.dateDebut, promotion.dateFin);
  }

  getStatutLibelle(promotion: PromotionGestionDto): string {
    return this.promotionService.getStatutLibelle(promotion.estActive, promotion.dateDebut, promotion.dateFin);
  }

  formatValeur(type: TypePromotion, valeur: number): string {
    return this.promotionService.formatValeur(type, valeur);
  }

  private formatDateForInput(date: Date): string {
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  }

  resetFilters(): void {
    this.filter = {
      page: 1,
      pageSize: 10,
      sortBy: 'dateCreation',
      sortDesc: true
    };
    this.loadPromotions();
  }

  // Pagination
  previousPage(): void {
    if (this.filter.page && this.filter.page > 1) {
      this.filter.page--;
      this.loadPromotions();
    }
  }

  nextPage(): void {
    if (this.filter.page) {
      this.filter.page++;
      this.loadPromotions();
    }
  }

  // Méthodes pour les statistiques
  getStatsArray(): { type: TypePromotion, count: number, libelle: string }[] {
    if (!this.stats) return [];
    
    return Object.entries(this.stats.promotionsParType).map(([type, count]) => ({
      type: parseInt(type) as TypePromotion,
      count: count,
      libelle: this.getTypeLibelle(parseInt(type) as TypePromotion)
    }));
  }

  getTauxUtilisation(promotion: PromotionGestionDto): number {
    if (!promotion.utilisationsMax) return 0;
    return (promotion.utilisationsActuelles / promotion.utilisationsMax) * 100;
  }
}
