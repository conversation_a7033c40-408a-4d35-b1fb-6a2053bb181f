<div class="promotions-fournisseur-container">
  <div class="header-section">
    <h2><i class="bi bi-tag"></i> Mes Promotions</h2>
    <p class="text-muted">C<PERSON>ez et gérez vos promotions pour attirer plus de clients</p>
    <button class="btn btn-primary" (click)="openCreateModal()">
      <i class="bi bi-plus"></i> Nouvelle Promotion
    </button>
  </div>

  <!-- Statistiques -->
  <div *ngIf="stats" class="stats-section mb-4">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="bi bi-tag"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.totalPromotions }}</h3>
            <p>Total promotions</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon text-success">
            <i class="bi bi-check-circle"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.promotionsActives }}</h3>
            <p>Actives</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon text-primary">
            <i class="bi bi-graph-up"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.totalUtilisations }}</h3>
            <p>Utilisations</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon text-warning">
            <i class="bi bi-currency-euro"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.montantTotalEconomise | number:'1.2-2' }} DT</h3>
            <p>Économies clients</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Répartition par type -->
    <div class="type-distribution mt-4" *ngIf="getStatsArray().length > 0">
      <div class="card">
        <div class="card-header">
          <h5><i class="bi bi-pie-chart"></i> Répartition par type</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4" *ngFor="let item of getStatsArray()">
              <div class="type-stat">
                <div class="type-icon">
                  <i class="bi" [class]="'bi-' + getTypeIcon(item.type)"></i>
                </div>
                <div class="type-info">
                  <h6>{{ item.libelle }}</h6>
                  <span class="badge" [class]="'bg-' + getTypeColor(item.type)">{{ item.count }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filtres -->
  <div class="filters-section card">
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-3">
          <label class="form-label">Type</label>
          <select class="form-select" [(ngModel)]="filter.type" (change)="onFilterChange()">
            <option [value]="undefined">Tous les types</option>
            <option [value]="TypePromotionGestion.Pourcentage">Pourcentage</option>
            <option [value]="TypePromotionGestion.MontantFixe">Montant fixe</option>
            <option [value]="TypePromotionGestion.Outlet">Outlet</option>
          </select>
        </div>
        
        <div class="col-md-3">
          <label class="form-label">Statut</label>
          <select class="form-select" [(ngModel)]="filter.estActive" (change)="onFilterChange()">
            <option [value]="undefined">Tous les statuts</option>
            <option [value]="true">Actives</option>
            <option [value]="false">Inactives</option>
          </select>
        </div>
        
        <div class="col-md-3">
          <label class="form-label">Recherche</label>
          <input type="text" class="form-control" placeholder="Code, nom..." 
                 [(ngModel)]="filter.recherche" (input)="onFilterChange()">
        </div>
        
        <div class="col-md-3">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" (click)="resetFilters()">
              <i class="bi bi-arrow-clockwise"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger">
    <i class="bi bi-exclamation-triangle"></i> {{ error }}
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Chargement...</span>
    </div>
  </div>

  <!-- Liste des promotions -->
  <div *ngIf="!loading" class="promotions-list">
    <div class="card">
      <div class="card-header">
        <div class="row align-items-center">
          <div class="col">
            <h5 class="mb-0">Mes promotions ({{ promotions.length }})</h5>
          </div>
          <div class="col-auto">
            <div class="btn-group btn-group-sm">
              <button class="btn btn-outline-secondary" 
                      [class.active]="filter.sortBy === 'dateCreation'"
                      (click)="onSortChange('dateCreation')">
                Date création
                <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'dateCreation' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'dateCreation' && filter.sortDesc"></i>
              </button>
              <button class="btn btn-outline-secondary"
                      [class.active]="filter.sortBy === 'nom'"
                      (click)="onSortChange('nom')">
                Nom
                <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'nom' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'nom' && filter.sortDesc"></i>
              </button>
              <button class="btn btn-outline-secondary"
                      [class.active]="filter.sortBy === 'valeur'"
                      (click)="onSortChange('valeur')">
                Valeur
                <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'valeur' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'valeur' && filter.sortDesc"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="card-body p-0">
        <div *ngIf="promotions.length === 0" class="text-center py-5 text-muted">
          <i class="bi bi-tag fs-1"></i>
          <p class="mt-2">Aucune promotion trouvée</p>
          <button class="btn btn-primary" (click)="openCreateModal()">
            <i class="bi bi-plus"></i> Créer ma première promotion
          </button>
        </div>
        
        <div *ngFor="let promotion of promotions" class="promotion-item border-bottom">
          <div class="row g-3 p-3">
            <!-- Informations promotion -->
            <div class="col-md-6">
              <div class="promotion-info">
                <div class="d-flex align-items-center mb-2">
                  <span class="badge me-2" [class]="'bg-' + getTypeColor(promotion.type)">
                    <i class="bi" [class]="'bi-' + getTypeIcon(promotion.type)"></i>
                    {{ getTypeLibelle(promotion.type) }}
                  </span>
                  <span class="badge" [class]="'bg-' + getStatutColor(promotion)">
                    {{ getStatutLibelle(promotion) }}
                  </span>
                </div>
                <h6 class="mb-1">{{ promotion.nom }}</h6>
                <p class="text-muted mb-1">Code: <strong>{{ promotion.code }}</strong></p>
                <p class="mb-2" *ngIf="promotion.description">{{ promotion.description }}</p>
                <div class="promotion-details">
                  <small class="text-muted d-block">
                    <i class="bi bi-calendar"></i> 
                    {{ promotion.dateDebut | date:'dd/MM/yyyy' }} - {{ promotion.dateFin | date:'dd/MM/yyyy' }}
                  </small>
                  <small class="text-muted d-block" *ngIf="promotion.montantMinimum">
                    <i class="bi bi-cash"></i> Minimum: {{ promotion.montantMinimum }}€
                  </small>
                </div>
              </div>
            </div>
            
            <!-- Valeur et utilisation -->
            <div class="col-md-3">
              <div class="promotion-value text-center">
                <div class="value-display">
                  <span class="value-number">{{ formatValeur(promotion.type, promotion.valeur) }}</span>
                </div>
                <div class="usage-info mt-2" *ngIf="promotion.utilisationsMax">
                  <small class="text-muted">Utilisations</small>
                  <div class="progress mt-1">
                    <div class="progress-bar" 
                         [style.width.%]="getTauxUtilisation(promotion)"
                         [class]="'bg-' + (getTauxUtilisation(promotion) > 80 ? 'warning' : 'primary')">
                    </div>
                  </div>
                  <small class="text-muted">{{ promotion.utilisationsActuelles }} / {{ promotion.utilisationsMax }}</small>
                </div>
                <div class="usage-info mt-2" *ngIf="!promotion.utilisationsMax">
                  <small class="text-muted">Utilisations</small>
                  <div class="mt-1">
                    <strong>{{ promotion.utilisationsActuelles }}</strong>
                  </div>
                  <small class="text-muted">Illimitées</small>
                </div>
              </div>
            </div>
            
            <!-- Actions -->
            <div class="col-md-3 text-end">
              <div class="promotion-stats mb-2">
                <small class="text-muted d-block">Économies: {{ promotion.montantTotalEconomise | number:'1.2-2' }} DT</small>
                <small class="text-muted d-block">Commandes: {{ promotion.nombreCommandesImpactees }}</small>
              </div>
              
              <div class="btn-group-vertical btn-group-sm w-100">
                <button class="btn btn-outline-primary" (click)="openEditModal(promotion)">
                  <i class="bi bi-pencil"></i> Modifier
                </button>
                <button class="btn" 
                        [class]="promotion.estActive ? 'btn-outline-warning' : 'btn-outline-success'"
                        (click)="togglePromotion(promotion)">
                  <i class="bi" [class]="promotion.estActive ? 'bi-pause' : 'bi-play'"></i>
                  {{ promotion.estActive ? 'Désactiver' : 'Activer' }}
                </button>
                <button class="btn btn-outline-danger" (click)="deletePromotion(promotion)">
                  <i class="bi bi-trash"></i> Supprimer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="promotions.length > 0" class="pagination-section mt-3">
    <nav>
      <ul class="pagination justify-content-center">
        <li class="page-item" [class.disabled]="filter.page === 1">
          <button class="page-link" (click)="previousPage()">Précédent</button>
        </li>
        <li class="page-item active">
          <span class="page-link">{{ filter.page }}</span>
        </li>
        <li class="page-item">
          <button class="page-link" (click)="nextPage()">Suivant</button>
        </li>
      </ul>
    </nav>
  </div>
</div>

<!-- Modal de création -->
<div *ngIf="showCreateModal" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-plus"></i> Nouvelle Promotion
        </h5>
        <button type="button" class="btn-close" (click)="closeCreateModal()"></button>
      </div>

      <form [formGroup]="createForm" (ngSubmit)="createPromotion()">
        <div class="modal-body">
          <div class="row g-3">
            <div class="col-md-6">
              <label class="form-label">Code promotion *</label>
              <input type="text" class="form-control" formControlName="code"
                     placeholder="Ex: PROMO2024" (blur)="checkCodeUniqueness()">
              <div class="form-text">Code unique pour identifier la promotion</div>
              <div *ngIf="createForm.get('code')?.errors?.['codeExists']" class="text-danger">
                Ce code existe déjà
              </div>
            </div>

            <div class="col-md-6">
              <label class="form-label">Type de promotion *</label>
              <select class="form-select" formControlName="type">
                <option [value]="TypePromotionGestion.Pourcentage">Pourcentage</option>
                <option [value]="TypePromotionGestion.MontantFixe">Montant fixe</option>
                <option [value]="TypePromotionGestion.Outlet">Outlet</option>
              </select>
            </div>

            <div class="col-12">
              <label class="form-label">Nom de la promotion *</label>
              <input type="text" class="form-control" formControlName="nom"
                     placeholder="Ex: Soldes d'été 2024">
            </div>

            <div class="col-12">
              <label class="form-label">Description</label>
              <textarea class="form-control" rows="3" formControlName="description"
                        placeholder="Description de la promotion..."></textarea>
            </div>

            <div class="col-md-6">
              <label class="form-label">Valeur *</label>
              <div class="input-group">
                <input type="number" class="form-control" formControlName="valeur"
                       min="0" max="100" step="0.01">
                <span class="input-group-text">
                  {{ createForm.get('type')?.value === TypePromotionGestion.MontantFixe ? '€' : '%' }}
                </span>
              </div>
            </div>

            <div class="col-md-6">
              <label class="form-label">Montant minimum</label>
              <div class="input-group">
                <input type="number" class="form-control" formControlName="montantMinimum"
                       min="0" step="0.01">
                <span class="input-group-text">€</span>
              </div>
              <div class="form-text">Montant minimum de commande requis</div>
            </div>

            <div class="col-md-6">
              <label class="form-label">Date de début *</label>
              <input type="date" class="form-control" formControlName="dateDebut">
            </div>

            <div class="col-md-6">
              <label class="form-label">Date de fin *</label>
              <input type="date" class="form-control" formControlName="dateFin">
            </div>

            <div class="col-md-6">
              <label class="form-label">Nombre d'utilisations max</label>
              <input type="number" class="form-control" formControlName="utilisationsMax"
                     min="1" placeholder="Illimité si vide">
              <div class="form-text">Laissez vide pour un usage illimité</div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
            Annuler
          </button>
          <button type="submit" class="btn btn-primary" [disabled]="!createForm.valid">
            <i class="bi bi-check"></i> Créer la promotion
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal d'édition -->
<div *ngIf="showEditModal" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-pencil"></i> Modifier la promotion
        </h5>
        <button type="button" class="btn-close" (click)="closeEditModal()"></button>
      </div>

      <form [formGroup]="editForm" (ngSubmit)="updatePromotion()">
        <div class="modal-body">
          <div class="row g-3">
            <div class="col-12">
              <label class="form-label">Nom de la promotion *</label>
              <input type="text" class="form-control" formControlName="nom">
            </div>

            <div class="col-12">
              <label class="form-label">Description</label>
              <textarea class="form-control" rows="3" formControlName="description"></textarea>
            </div>

            <div class="col-md-6">
              <label class="form-label">Valeur *</label>
              <div class="input-group">
                <input type="number" class="form-control" formControlName="valeur"
                       min="0" max="100" step="0.01">
                <span class="input-group-text">
                  {{ selectedPromotion?.type === TypePromotionGestion.MontantFixe ? '€' : '%' }}
                </span>
              </div>
            </div>

            <div class="col-md-6">
              <label class="form-label">Montant minimum</label>
              <div class="input-group">
                <input type="number" class="form-control" formControlName="montantMinimum"
                       min="0" step="0.01">
                <span class="input-group-text">€</span>
              </div>
            </div>

            <div class="col-md-6">
              <label class="form-label">Date de début *</label>
              <input type="date" class="form-control" formControlName="dateDebut">
            </div>

            <div class="col-md-6">
              <label class="form-label">Date de fin *</label>
              <input type="date" class="form-control" formControlName="dateFin">
            </div>

            <div class="col-md-6">
              <label class="form-label">Nombre d'utilisations max</label>
              <input type="number" class="form-control" formControlName="utilisationsMax"
                     min="1" placeholder="Illimité si vide">
            </div>

            <div class="col-md-6">
              <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" formControlName="estActive">
                <label class="form-check-label">
                  Promotion active
                </label>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
            Annuler
          </button>
          <button type="submit" class="btn btn-primary" [disabled]="!editForm.valid">
            <i class="bi bi-check"></i> Mettre à jour
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
