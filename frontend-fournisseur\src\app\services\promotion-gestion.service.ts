import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface PromotionGestionDto {
  id: number;
  code: string;
  nom: string;
  description?: string;
  type: TypePromotionGestion;
  valeur: number;
  dateDebut: Date;
  dateFin: Date;
  estActive: boolean;
  utilisationsMax?: number;
  utilisationsActuelles: number;
  montantMinimum?: number;
  dateCreation: Date;
  
  // Informations fournisseur
  fournisseurId?: number;
  fournisseurNom?: string;
  fournisseurRaisonSociale?: string;
  
  // Informations produit
  produitId?: number;
  produitNom?: string;
  
  // Statistiques
  montantTotalEconomise: number;
  nombreCommandesImpactees: number;
}

export interface PromotionCreateDto {
  code: string;
  nom: string;
  description?: string;
  type: TypePromotionGestion;
  valeur: number;
  dateDebut: Date;
  dateFin: Date;
  utilisationsMax?: number;
  montantMinimum?: number;
  produitId?: number;
  fournisseurId?: number;
}

export interface PromotionUpdateDto {
  nom: string;
  description?: string;
  valeur: number;
  dateDebut: Date;
  dateFin: Date;
  estActive: boolean;
  utilisationsMax?: number;
  montantMinimum?: number;
}

export interface PromotionFilterDto {
  type?: TypePromotionGestion;
  estActive?: boolean;
  fournisseurId?: number;
  dateDebut?: Date;
  dateFin?: Date;
  recherche?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDesc?: boolean;
}

export interface PromotionStatsDto {
  totalPromotions: number;
  promotionsActives: number;
  promotionsExpirees: number;
  promotionsEnAttente: number;
  montantTotalEconomise: number;
  totalUtilisations: number;
  tauxUtilisationMoyen: number;
  promotionsParType: { [key: number]: number };
  promotionsPopulaires: PromotionGestionDto[];
  promotionsRecentes: PromotionGestionDto[];
}

export interface ValidatePromotionDto {
  code: string;
  montantCommande: number;
  produitId?: number;
  clientId?: number;
}

export interface PromotionValidationResultDto {
  estValide: boolean;
  messageErreur?: string;
  montantReduction: number;
  promotion?: PromotionGestionDto;
}

export enum TypePromotionGestion {
  Pourcentage = 1,
  MontantFixe = 2,
  Outlet = 3
}

// Alias pour compatibilité
export const TypePromotion = TypePromotionGestion;

@Injectable({
  providedIn: 'root'
})
export class PromotionGestionService {
  private apiUrl = `${environment.apiUrl}/promotion-gestion`;

  constructor(private http: HttpClient) { }

  // Gestion CRUD des promotions
  getPromotions(filter: PromotionFilterDto = {}): Observable<PromotionGestionDto[]> {
    let params = new HttpParams();
    
    if (filter.type !== undefined) params = params.set('type', filter.type.toString());
    if (filter.estActive !== undefined) params = params.set('estActive', filter.estActive.toString());
    if (filter.fournisseurId) params = params.set('fournisseurId', filter.fournisseurId.toString());
    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.recherche) params = params.set('recherche', filter.recherche);
    if (filter.page) params = params.set('page', filter.page.toString());
    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);
    if (filter.sortDesc !== undefined) params = params.set('sortDesc', filter.sortDesc.toString());

    return this.http.get<PromotionGestionDto[]>(this.apiUrl, { params });
  }

  getPromotion(id: number): Observable<PromotionGestionDto> {
    return this.http.get<PromotionGestionDto>(`${this.apiUrl}/${id}`);
  }

  createPromotion(dto: PromotionCreateDto): Observable<PromotionGestionDto> {
    return this.http.post<PromotionGestionDto>(this.apiUrl, dto);
  }

  updatePromotion(id: number, dto: PromotionUpdateDto): Observable<PromotionGestionDto> {
    return this.http.put<PromotionGestionDto>(`${this.apiUrl}/${id}`, dto);
  }

  deletePromotion(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  togglePromotion(id: number): Observable<PromotionGestionDto> {
    return this.http.patch<PromotionGestionDto>(`${this.apiUrl}/${id}/toggle`, {});
  }

  // Validation
  validatePromotion(dto: ValidatePromotionDto): Observable<PromotionValidationResultDto> {
    return this.http.post<PromotionValidationResultDto>(`${this.apiUrl}/validate`, dto);
  }

  getApplicablePromotions(produitId?: number, montantCommande?: number): Observable<PromotionGestionDto[]> {
    let params = new HttpParams();
    if (produitId) params = params.set('produitId', produitId.toString());
    if (montantCommande) params = params.set('montantCommande', montantCommande.toString());

    return this.http.get<PromotionGestionDto[]>(`${this.apiUrl}/applicable`, { params });
  }

  // Statistiques
  getPromotionStats(): Observable<PromotionStatsDto> {
    return this.http.get<PromotionStatsDto>(`${this.apiUrl}/statistiques`);
  }

  // Méthodes pour les fournisseurs
  getPromotionsFournisseur(filter: PromotionFilterDto = {}): Observable<PromotionGestionDto[]> {
    let params = new HttpParams();
    
    if (filter.type !== undefined) params = params.set('type', filter.type.toString());
    if (filter.estActive !== undefined) params = params.set('estActive', filter.estActive.toString());
    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.recherche) params = params.set('recherche', filter.recherche);
    if (filter.page) params = params.set('page', filter.page.toString());
    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);
    if (filter.sortDesc !== undefined) params = params.set('sortDesc', filter.sortDesc.toString());

    return this.http.get<PromotionGestionDto[]>(`${this.apiUrl}/fournisseur`, { params });
  }

  getPromotionStatsFournisseur(): Observable<PromotionStatsDto> {
    return this.http.get<PromotionStatsDto>(`${this.apiUrl}/statistiques/fournisseur`);
  }

  // Méthodes pour l'admin
  getPromotionsExpirants(jours: number = 7): Observable<PromotionGestionDto[]> {
    return this.http.get<PromotionGestionDto[]>(`${this.apiUrl}/expirants?jours=${jours}`);
  }

  getPromotionsPopulaires(limit: number = 10): Observable<PromotionGestionDto[]> {
    return this.http.get<PromotionGestionDto[]>(`${this.apiUrl}/populaires?limit=${limit}`);
  }

  // Utilitaires
  checkCodeExists(code: string, excludeId?: number): Observable<{exists: boolean}> {
    let params = new HttpParams();
    if (excludeId) params = params.set('excludeId', excludeId.toString());
    
    return this.http.get<{exists: boolean}>(`${this.apiUrl}/check-code/${code}`, { params });
  }

  // Méthodes utilitaires
  getTypeLibelle(type: TypePromotionGestion): string {
    switch (type) {
      case TypePromotionGestion.Pourcentage: return 'Pourcentage';
      case TypePromotionGestion.MontantFixe: return 'Montant fixe';
      case TypePromotionGestion.Outlet: return 'Outlet';
      default: return 'Non défini';
    }
  }

  getTypeIcon(type: TypePromotionGestion): string {
    switch (type) {
      case TypePromotionGestion.Pourcentage: return 'percent';
      case TypePromotionGestion.MontantFixe: return 'currency-euro';
      case TypePromotionGestion.Outlet: return 'tag';
      default: return 'question-circle';
    }
  }

  getTypeColor(type: TypePromotionGestion): string {
    switch (type) {
      case TypePromotionGestion.Pourcentage: return 'primary';
      case TypePromotionGestion.MontantFixe: return 'success';
      case TypePromotionGestion.Outlet: return 'warning';
      default: return 'secondary';
    }
  }

  getStatutColor(estActive: boolean, dateDebut: Date, dateFin: Date): string {
    const now = new Date();
    if (!estActive) return 'secondary';
    if (now < dateDebut) return 'info';
    if (now > dateFin) return 'danger';
    return 'success';
  }

  getStatutLibelle(estActive: boolean, dateDebut: Date, dateFin: Date): string {
    const now = new Date();
    if (!estActive) return 'Inactive';
    if (now < dateDebut) return 'En attente';
    if (now > dateFin) return 'Expirée';
    return 'Active';
  }

  formatValeur(type: TypePromotionGestion, valeur: number): string {
    switch (type) {
      case TypePromotionGestion.Pourcentage: return `${valeur}%`;
      case TypePromotionGestion.MontantFixe: return `${valeur}€`;
      case TypePromotionGestion.Outlet: return `${valeur}%`;
      default: return valeur.toString();
    }
  }
}
