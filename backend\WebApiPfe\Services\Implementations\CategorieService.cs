﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApiPfe.Models;
using WebApiPfe.Models.Entity;
using WebApiPfe.Services.Interfaces;
using WebApiPfe.DTOs.ReadDTOs;
namespace WebApiPfe.Services.Implementations
{
    public class CategorieService : ICategorieService
    {
        private readonly AppDbContext _context;
        public CategorieService(AppDbContext context)
        {
            _context = context;
        }
        public async Task<Categorie> CreateAsync(Categorie categorie)
        {
            if (await _context.Categories.AnyAsync(c => c.Nom == categorie.Nom))
                throw new ArgumentException("Une catégorie avec ce nom existe déjà");

            await _context.Categories.AddAsync(categorie);
            await _context.SaveChangesAsync();
            return categorie;
        }
        public async Task DeleteAsync(int id)
        {
            var categorie = await _context.Categories
                .Include(c => c.SousCategories)
                .FirstOrDefaultAsync(c => c.Id == id);
            if (categorie == null)
                throw new KeyNotFoundException("Catégorie non trouvée");
            if (categorie.SousCategories.Any())
                throw new InvalidOperationException("Impossible de supprimer une catégorie avec des sous-catégories");
            _context.Categories.Remove(categorie);
            await _context.SaveChangesAsync();
        }
        public async Task<bool> ExistsAsync(int id) =>
            await _context.Categories.AnyAsync(c => c.Id == id);
        public async Task<IEnumerable<Categorie>> GetAllAsync() =>
            await _context.Categories
                .Where(c => c.EstValidee) // Seulement les catégories validées
                .Include(c => c.SousCategories.Where(sc => sc.EstValidee)) // Seulement les sous-catégories validées
                .AsNoTracking()
                .OrderBy(c => c.Nom)
                .ToListAsync();

        // Méthode pour les admins pour voir toutes les catégories
        public async Task<IEnumerable<Categorie>> GetAllForAdminAsync() =>
            await _context.Categories
                .Include(c => c.SousCategories)
                .AsNoTracking()
                .OrderBy(c => c.Nom)
                .ToListAsync();

        // Méthode pour les fournisseurs pour voir les catégories validées + leurs propres demandes en attente
        public async Task<IEnumerable<Categorie>> GetAllForFournisseurAsync(int fournisseurId)
        {
            // Récupérer les catégories validées
            var categoriesValidees = await _context.Categories
                .Where(c => c.EstValidee)
                .Include(c => c.SousCategories.Where(sc => sc.EstValidee))
                .AsNoTracking()
                .ToListAsync();

            // Récupérer les demandes de catégories en attente du fournisseur
            var demandesEnAttente = await _context.DemandesCategories
                .Where(d => d.FournisseurId == fournisseurId && d.Statut == Models.Enum.StatutDemande.EnAttente)
                .ToListAsync();

            // Récupérer les catégories en attente créées par ce fournisseur
            var categoriesEnAttente = await _context.Categories
                .Where(c => !c.EstValidee && demandesEnAttente.Any(d => d.Nom == c.Nom))
                .Include(c => c.SousCategories)
                .AsNoTracking()
                .ToListAsync();

            // Combiner les deux listes
            var toutesCategories = categoriesValidees.Concat(categoriesEnAttente)
                .OrderBy(c => c.Nom)
                .ToList();

            return toutesCategories;
        }
        public async Task<CategorieDto> GetByIdAsync(int id)
        {
            var result = await _context.Categories
                .Where(c => c.Id == id)
                .Select(c => new CategorieDto
                {
                    Id = c.Id,
                    Nom = c.Nom,
                    SousCategoriesCount = c.SousCategories.Count
                })
                .FirstOrDefaultAsync();

            return result ?? throw new KeyNotFoundException("Catégorie introuvable");
        }
        public async Task<IEnumerable<SousCategorie>> GetSousCategoriesAsync(int categorieId) =>
            await _context.SousCategories
                //.Where(sc => sc.CategorieId == categorieId)
                .ToListAsync();
        public async Task<int> GetProduitsCountAsync(int categorieId) =>
            await _context.Produits
                .Where(p => p.SousCategorie.CategorieId == categorieId)
                .CountAsync();
        public async Task<Dictionary<int, string>> GetCategoriesForDropdownAsync() =>
            await _context.Categories
                .OrderBy(c => c.Nom)
                .ToDictionaryAsync(c => c.Id, c => c.Nom);
        public async Task ToggleVisibilityAsync(int id)
        {
            var categorie = await GetByIdAsync(id);
            // Implémentez votre logique de visibilité ici
            await _context.SaveChangesAsync();
        }
        public async Task UpdateAsync(Categorie categorie)
        {
            if (await _context.Categories.AnyAsync(c => c.Nom == categorie.Nom && c.Id != categorie.Id))
                throw new ArgumentException("Une catégorie avec ce nom existe déjà");
            _context.Categories.Update(categorie);
            await _context.SaveChangesAsync();
        }
    }
}
