﻿using WebApiPfe.DTOs.Admin;
using WebApiPfe.DTOs.ReadDTOs;

namespace WebApiPfe.Services.Interfaces
{
    public interface IAdminService
    {
        // 1. Utilisateurs
        Task<IEnumerable<UtilisateurGestionDto>> GetAllUtilisateursAsync();
        Task<UtilisateursPageDto> GetUtilisateursPageAsync(int page = 1, int pageSize = 10, string? search = null, string? role = null, bool? estActif = null);
        Task<bool> ToggleActivationUtilisateurAsync(int id);
        Task<bool> SupprimerUtilisateurAsync(int id);

        // 2. Catégories / Sous-catégories
        Task<bool> ValiderCategorieAsync(int categorieId);
        Task<bool> RefuserCategorieAsync(int categorieId);
        Task<bool> AjouterCategorieAsync(CategorieGestionDto dto);
        Task<bool> ModifierCategorieAsync(int id, CategorieGestionDto dto);
        Task<bool> SupprimerCategorieAsync(int id);

        Task<bool> ValidersousCategorieAsync(int categorieId);
        Task<bool> RefusersousCategorieAsync(int categorieId);
        Task<bool> AjouterSousCategorieAsync(SousCategorieGestionDto dto);
        Task<bool> ModifierSousCategorieAsync(int id, SousCategorieGestionDto dto);
        Task<bool> SupprimerSousCategorieAsync(int id);

        // 3. Commandes / Transactions
        Task<bool> AnnulerCommandeAsync(int commandeId);
        Task<IEnumerable<CommandeGestionDto>> GetCommandesAsync();

        // 4. Gestion des produits
        Task<ProduitGestionPageDto> GetProduitsPageAsync(int page = 1, int pageSize = 10, string? search = null, string? statut = null, bool? stockCritique = null, string? fournisseur = null);
        Task<bool> ValiderProduitAsync(int produitId);
        Task<bool> RefuserProduitAsync(int produitId, string? raison = null);
        Task<bool> MettreEnAvantProduitAsync(int produitId);
        Task<bool> RetirerMiseEnAvantProduitAsync(int produitId);
        Task<bool> ModererContenuProduitAsync(int produitId, string? nouveauNom = null, string? nouvelleDescription = null);
        Task<bool> SupprimerProduitAsync(int produitId);
        Task<IEnumerable<ProduitGestionDto>> GetProduitsStockCritiqueAsync(int seuilCritique = 10);
        Task<bool> UpdateStockProduitAsync(int produitId, int nouveauStock);

        // 5. Promotions / Visibilité
        Task<IEnumerable<ProduitGestionDto>> GetProduitsEnAvantAsync();

        // 6. Statistiques
        Task<StatistiqueDto> ObtenirStatistiquesGeneralesAsync();
    }
}
