<div class="avis-fournisseur-container">
  <div class="header-section">
    <h2><i class="bi bi-star"></i> Mes Avis Clients</h2>
    <p class="text-muted">Consultez et répondez aux avis de vos clients</p>
  </div>

  <!-- Statistiques -->
  <div *ngIf="stats" class="stats-section mb-4">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="bi bi-chat-square-text"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.totalAvis }}</h3>
            <p>Total des avis</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon text-warning">
            <i class="bi bi-clock"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.avisPublies }}</h3>
            <p>Publiés</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon text-success">
            <i class="bi bi-check-circle"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.avisCommentaireSupprime }}</h3>
            <p>Commentaires supprimés</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon text-primary">
            <i class="bi bi-star-fill"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.noteMoyenneGlobale | number:'1.1-1' }}</h3>
            <p>Note moyenne</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Répartition des notes -->
    <div class="rating-distribution mt-4">
      <div class="card">
        <div class="card-header">
          <h5><i class="bi bi-bar-chart"></i> Répartition des notes</h5>
        </div>
        <div class="card-body">
          <div *ngFor="let item of getStatsArray()" class="rating-row">
            <div class="rating-label">
              <span>{{ item.note }}</span>
              <i class="bi bi-star-fill text-warning"></i>
            </div>
            <div class="rating-bar">
              <div class="progress">
                <div class="progress-bar" 
                     [style.width]="getProgressBarWidth(item.percentage)"
                     [class]="'bg-' + (item.note >= 4 ? 'success' : item.note >= 3 ? 'warning' : 'danger')">
                </div>
              </div>
            </div>
            <div class="rating-count">
              {{ item.count }} ({{ item.percentage | number:'1.0-0' }}%)
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filtres -->
  <div class="filters-section card">
    <div class="card-body">
      <div class="row g-3">
        <div class="col-md-3">
          <label class="form-label">Statut</label>
          <select class="form-select" [(ngModel)]="filter.statut" (change)="onFilterChange()">
            <option [value]="undefined">Tous les statuts</option>
            <option [value]="StatutAvis.Publie">Publié</option>
            <option [value]="StatutAvis.CommentaireSupprime">Commentaire supprimé</option>
            <option [value]="StatutAvis.Signale">Signalé</option>
          </select>
        </div>
        
        <div class="col-md-3">
          <label class="form-label">Recherche</label>
          <input type="text" class="form-control" placeholder="Produit, client..." 
                 [(ngModel)]="filter.recherche" (input)="onFilterChange()">
        </div>
        
        <div class="col-md-2">
          <label class="form-label">Date début</label>
          <input type="date" class="form-control" [(ngModel)]="filter.dateDebut" (change)="onFilterChange()">
        </div>
        
        <div class="col-md-2">
          <label class="form-label">Date fin</label>
          <input type="date" class="form-control" [(ngModel)]="filter.dateFin" (change)="onFilterChange()">
        </div>
        
        <div class="col-md-2">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" (click)="resetFilters()">
              <i class="bi bi-arrow-clockwise"></i>
            </button>
            <button class="btn btn-outline-primary" (click)="exportAvis()">
              <i class="bi bi-download"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger">
    <i class="bi bi-exclamation-triangle"></i> {{ error }}
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Chargement...</span>
    </div>
  </div>

  <!-- Liste des avis -->
  <div *ngIf="!loading" class="avis-list">
    <div class="card">
      <div class="card-header">
        <div class="row align-items-center">
          <div class="col">
            <h5 class="mb-0">Avis clients ({{ avis.length }})</h5>
          </div>
          <div class="col-auto">
            <div class="btn-group btn-group-sm">
              <button class="btn btn-outline-secondary" 
                      [class.active]="filter.sortBy === 'datePublication'"
                      (click)="onSortChange('datePublication')">
                Date
                <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'datePublication' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'datePublication' && filter.sortDesc"></i>
              </button>
              <button class="btn btn-outline-secondary"
                      [class.active]="filter.sortBy === 'note'"
                      (click)="onSortChange('note')">
                Note
                <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'note' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'note' && filter.sortDesc"></i>
              </button>
              <button class="btn btn-outline-secondary"
                      [class.active]="filter.sortBy === 'statut'"
                      (click)="onSortChange('statut')">
                Statut
                <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'statut' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'statut' && filter.sortDesc"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="card-body p-0">
        <div *ngIf="avis.length === 0" class="text-center py-5 text-muted">
          <i class="bi bi-star fs-1"></i>
          <p class="mt-2">Aucun avis trouvé</p>
        </div>
        
        <div *ngFor="let avisItem of avis" class="avis-item border-bottom">
          <div class="row g-3 p-3">
            <!-- Informations produit et client -->
            <div class="col-md-4">
              <div class="product-info">
                <h6 class="mb-1">{{ avisItem.produitNom }}</h6>
                <small class="text-muted">Réf: {{ avisItem.produitReference }}</small>
              </div>
              <div class="client-info mt-2">
                <small class="text-muted">
                  <i class="bi bi-person"></i> {{ avisItem.clientPrenom }} {{ avisItem.clientNom }}
                </small>
              </div>
            </div>
            
            <!-- Avis -->
            <div class="col-md-5">
              <div class="rating mb-2">
                <span *ngFor="let star of getStars(avisItem.note)" 
                      class="bi" [class]="'bi-' + star" style="color: #ffc107;"></span>
                <span class="ms-2 text-muted">{{ avisItem.note }}/5</span>
              </div>
              <p class="mb-2" [title]="avisItem.commentaire">
                <span *ngIf="avisItem.commentaireSupprime" class="text-danger fst-italic">
                  Commentaire supprimé
                </span>
                <span *ngIf="!avisItem.commentaireSupprime && avisItem.commentaire && avisItem.commentaire.trim()">
                  {{ avisItem.commentaire.length > 100 ?
                     avisItem.commentaire.substring(0, 100) + '...' :
                     avisItem.commentaire }}
                </span>
                <span *ngIf="!avisItem.commentaireSupprime && (!avisItem.commentaire || !avisItem.commentaire.trim())" class="text-muted fst-italic">
                  Note sans commentaire
                </span>
              </p>
              <small class="text-muted">
                <i class="bi bi-calendar"></i> {{ avisItem.datePublication | date:'dd/MM/yyyy HH:mm' }}
              </small>
              
              <!-- Réponse existante -->
              <div *ngIf="avisItem.commentaireModeration" class="mt-2 p-2 bg-light rounded">
                <small class="text-muted d-block">Votre réponse :</small>
                <small>{{ avisItem.commentaireModeration }}</small>
              </div>
            </div>
            
            <!-- Statut et actions -->
            <div class="col-md-3 text-end">
              <div class="mb-2">
                <span class="badge" [class]="'bg-' + getStatutColor(avisItem.statut)">
                  <i class="bi" [class]="'bi-' + getStatutIcon(avisItem.statut)"></i>
                  {{ getStatutLibelle(avisItem.statut) }}
                </span>
              </div>
              
              <div class="d-flex flex-column gap-2">
                <div *ngIf="avisItem.statut === StatutAvis.Publie">
                  <button class="btn btn-sm btn-outline-primary w-100" (click)="openReponseModal(avisItem)">
                    <i class="bi bi-reply"></i>
                    {{ avisItem.commentaireModeration ? 'Modifier réponse' : 'Répondre' }}
                  </button>
                </div>

                <div *ngIf="avisItem.statut !== StatutAvis.Signale">
                  <button class="btn btn-sm btn-outline-warning w-100"
                          (click)="openSignalementModal(avisItem)"
                          title="Signaler cet avis à l'administrateur">
                    <i class="bi bi-flag"></i>
                    Signaler
                  </button>
                </div>

                <div *ngIf="avisItem.statut === StatutAvis.Signale">
                  <small class="text-warning">
                    <i class="bi bi-flag-fill"></i> Avis signalé
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="avis.length > 0" class="pagination-section mt-3">
    <nav>
      <ul class="pagination justify-content-center">
        <li class="page-item" [class.disabled]="filter.page === 1">
          <button class="page-link" (click)="previousPage()">Précédent</button>
        </li>
        <li class="page-item active">
          <span class="page-link">{{ filter.page }}</span>
        </li>
        <li class="page-item">
          <button class="page-link" (click)="nextPage()">Suivant</button>
        </li>
      </ul>
    </nav>
  </div>
</div>

<!-- Modal de réponse -->
<div *ngIf="selectedAvis" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-reply"></i> Répondre à l'avis
        </h5>
        <button type="button" class="btn-close" (click)="closeReponseModal()"></button>
      </div>

      <div class="modal-body">
        <!-- Détails de l'avis -->
        <div class="avis-details mb-4">
          <div class="row">
            <div class="col-md-6">
              <h6>Produit</h6>
              <p>{{ selectedAvis.produitNom }}</p>
              <small class="text-muted">Réf: {{ selectedAvis.produitReference }}</small>
            </div>
            <div class="col-md-6">
              <h6>Client</h6>
              <p>{{ selectedAvis.clientPrenom }} {{ selectedAvis.clientNom }}</p>
            </div>
          </div>

          <div class="mt-3">
            <h6>Avis client</h6>
            <div class="rating mb-2">
              <span *ngFor="let star of getStars(selectedAvis.note)"
                    class="bi" [class]="'bi-' + star" style="color: #ffc107;"></span>
              <span class="ms-2">{{ selectedAvis.note }}/5</span>
            </div>
            <div class="border p-3 rounded bg-light">
              <span *ngIf="selectedAvis.commentaireSupprime" class="text-danger fst-italic">
                Commentaire supprimé
              </span>
              <span *ngIf="!selectedAvis.commentaireSupprime && selectedAvis.commentaire && selectedAvis.commentaire.trim()">
                {{ selectedAvis.commentaire }}
              </span>
              <span *ngIf="!selectedAvis.commentaireSupprime && (!selectedAvis.commentaire || !selectedAvis.commentaire.trim())" class="text-muted fst-italic">
                Note sans commentaire
              </span>
            </div>
          </div>
        </div>

        <!-- Formulaire de réponse -->
        <form (ngSubmit)="repondreAvis()">
          <div class="mb-3">
            <label class="form-label">Votre réponse</label>
            <textarea class="form-control" rows="4"
                      [(ngModel)]="reponseForm.reponse"
                      name="reponse"
                      placeholder="Rédigez votre réponse au client..."
                      required></textarea>
            <div class="form-text">
              Cette réponse sera visible par le client et les autres visiteurs.
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeReponseModal()">
          Annuler
        </button>
        <button type="button" class="btn btn-primary"
                (click)="repondreAvis()"
                [disabled]="!reponseForm.reponse.trim()">
          <i class="bi bi-send"></i> Envoyer la réponse
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modale de signalement d'avis -->
<div *ngIf="showSignalementModal" class="modal-overlay" (click)="closeSignalementModal()">
  <div class="modal-content signalement-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h5 class="modal-title">
        <i class="bi bi-flag text-warning me-2"></i>
        Signaler un avis
      </h5>
      <button type="button" class="btn-close" (click)="closeSignalementModal()"></button>
    </div>

    <div class="modal-body">
      <div class="alert alert-info">
        <i class="bi bi-info-circle me-2"></i>
        <strong>Avis concerné :</strong><br>
        <small class="text-muted">{{ selectedAvisForSignalement?.clientNom }} {{ selectedAvisForSignalement?.clientPrenom }}</small><br>
        <small>{{ selectedAvisForSignalement?.commentaire }}</small>
      </div>

      <form (ngSubmit)="signalerAvis()">
        <div class="mb-3">
          <label class="form-label">
            <i class="bi bi-chat-square-text me-2"></i>
            Raison du signalement <span class="text-danger">*</span>
          </label>
          <textarea class="form-control" rows="4"
                    [(ngModel)]="signalementForm.raisonSignalement"
                    name="raisonSignalement"
                    placeholder="Expliquez pourquoi vous signalez cet avis (contenu inapproprié, faux avis, etc.)..."
                    required></textarea>
          <div class="form-text">
            <i class="bi bi-shield-check me-1"></i>
            Ce signalement sera transmis à l'administrateur pour examen.
          </div>
        </div>

        <div class="d-flex justify-content-end gap-2">
          <button type="button" class="btn btn-secondary" (click)="closeSignalementModal()">
            <i class="bi bi-x-circle me-2"></i>
            Annuler
          </button>
          <button type="submit"
                  class="btn btn-warning"
                  [disabled]="!signalementForm.raisonSignalement.trim() || loading">
            <i class="bi bi-flag me-2"></i>
            <span *ngIf="loading">Signalement en cours...</span>
            <span *ngIf="!loading">Signaler l'avis</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
