<div class="promotions-admin-container">
  <div class="header-section">
    <h2><i class="bi bi-tags"></i> Gestion des Promotions</h2>
    <p class="text-muted">Supervisez et gérez toutes les promotions de la plateforme</p>
    <button class="btn btn-primary" (click)="openCreateModal()">
      <i class="bi bi-plus"></i> Nouvelle Promotion
    </button>
  </div>

  <!-- Statistiques globales -->
  <div *ngIf="stats" class="stats-section mb-4">
    <div class="row g-3">
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="bi bi-tags"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.totalPromotions }}</h3>
            <p>Total promotions</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon text-success">
            <i class="bi bi-check-circle"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.promotionsActives }}</h3>
            <p>Actives</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon text-warning">
            <i class="bi bi-clock"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.promotionsEnAttente }}</h3>
            <p>En attente</p>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="stat-card">
          <div class="stat-icon text-danger">
            <i class="bi bi-x-circle"></i>
          </div>
          <div class="stat-content">
            <h3>{{ stats.promotionsExpirees }}</h3>
            <p>Expirées</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Statistiques d'utilisation -->
    <div class="usage-stats mt-4">
      <div class="row g-3">
        <div class="col-md-4">
          <div class="usage-card">
            <h5><i class="bi bi-graph-up"></i> Utilisations</h5>
            <div class="usage-number">{{ stats.totalUtilisations }}</div>
            <small class="text-muted">Total des utilisations</small>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="usage-card">
            <h5><i class="bi bi-currency-euro"></i> Économies</h5>
            <div class="usage-number">{{ stats.montantTotalEconomise | number:'1.2-2' }} DT</div>
            <small class="text-muted">Économies clients</small>
          </div>
        </div>
        
        <div class="col-md-4">
          <div class="usage-card">
            <h5><i class="bi bi-percent"></i> Taux moyen</h5>
            <div class="usage-number">{{ stats.tauxUtilisationMoyen | number:'1.1-1' }}</div>
            <small class="text-muted">Utilisations par promotion</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Alertes promotions expirants -->
  <div *ngIf="promotionsExpirants.length > 0" class="alert alert-warning">
    <h6><i class="bi bi-exclamation-triangle"></i> Promotions expirant bientôt</h6>
    <div class="row">
      <div class="col-md-6" *ngFor="let promo of promotionsExpirants.slice(0, 4)">
        <small>
          <strong>{{ promo.nom }}</strong> - expire dans {{ getJoursRestants(promo.dateFin) }} jour(s)
        </small>
      </div>
    </div>
  </div>

  <!-- Onglets -->
  <div class="tabs-section">
    <ul class="nav nav-tabs">
      <li class="nav-item">
        <button class="nav-link" [class.active]="activeTab === 'all'" (click)="onTabChange('all')">
          <i class="bi bi-list"></i> Toutes ({{ promotions.length }})
        </button>
      </li>
      <li class="nav-item">
        <button class="nav-link" [class.active]="activeTab === 'active'" (click)="onTabChange('active')">
          <i class="bi bi-check-circle"></i> Actives
        </button>
      </li>
      <li class="nav-item">
        <button class="nav-link" [class.active]="activeTab === 'expired'" (click)="onTabChange('expired')">
          <i class="bi bi-x-circle"></i> Expirées
        </button>
      </li>
      <li class="nav-item">
        <button class="nav-link" [class.active]="activeTab === 'stats'" (click)="onTabChange('stats')">
          <i class="bi bi-bar-chart"></i> Statistiques
        </button>
      </li>
    </ul>
  </div>

  <!-- Contenu des onglets -->
  <div class="tab-content">
    <!-- Onglet liste des promotions -->
    <div *ngIf="activeTab !== 'stats'" class="tab-pane">
      <!-- Filtres -->
      <div class="filters-section card">
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-2">
              <label class="form-label">Type</label>
              <select class="form-select" [(ngModel)]="filter.type" (change)="onFilterChange()">
                <option [value]="undefined">Tous</option>
                <option [value]="TypePromotionGestion.Pourcentage">Pourcentage</option>
                <option [value]="TypePromotionGestion.MontantFixe">Montant fixe</option>
                <option [value]="TypePromotionGestion.Outlet">Outlet</option>
              </select>
            </div>
            
            <div class="col-md-2">
              <label class="form-label">Fournisseur</label>
              <input type="number" class="form-control" placeholder="ID Fournisseur" 
                     [(ngModel)]="filter.fournisseurId" (input)="onFilterChange()">
            </div>
            
            <div class="col-md-3">
              <label class="form-label">Recherche</label>
              <input type="text" class="form-control" placeholder="Code, nom, fournisseur..." 
                     [(ngModel)]="filter.recherche" (input)="onFilterChange()">
            </div>
            
            <div class="col-md-2">
              <label class="form-label">Date début</label>
              <input type="date" class="form-control" [(ngModel)]="filter.dateDebut" (change)="onFilterChange()">
            </div>
            
            <div class="col-md-2">
              <label class="form-label">Date fin</label>
              <input type="date" class="form-control" [(ngModel)]="filter.dateFin" (change)="onFilterChange()">
            </div>
            
            <div class="col-md-1">
              <label class="form-label">&nbsp;</label>
              <button class="btn btn-outline-secondary w-100" (click)="resetFilters()">
                <i class="bi bi-arrow-clockwise"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Message d'erreur -->
      <div *ngIf="error" class="alert alert-danger">
        <i class="bi bi-exclamation-triangle"></i> {{ error }}
      </div>

      <!-- Loading -->
      <div *ngIf="loading" class="text-center py-4">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
      </div>

      <!-- Liste des promotions -->
      <div *ngIf="!loading" class="promotions-list">
        <div class="card">
          <div class="card-header">
            <div class="row align-items-center">
              <div class="col">
                <h5 class="mb-0">Promotions ({{ promotions.length }})</h5>
              </div>
              <div class="col-auto">
                <div class="btn-group btn-group-sm">
                  <button class="btn btn-outline-secondary" 
                          [class.active]="filter.sortBy === 'dateCreation'"
                          (click)="onSortChange('dateCreation')">
                    Date création
                    <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'dateCreation' && !filter.sortDesc"
                       [class.bi-arrow-down]="filter.sortBy === 'dateCreation' && filter.sortDesc"></i>
                  </button>
                  <button class="btn btn-outline-secondary"
                          [class.active]="filter.sortBy === 'nom'"
                          (click)="onSortChange('nom')">
                    Nom
                    <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'nom' && !filter.sortDesc"
                       [class.bi-arrow-down]="filter.sortBy === 'nom' && filter.sortDesc"></i>
                  </button>
                  <button class="btn btn-outline-secondary"
                          [class.active]="filter.sortBy === 'utilisationsActuelles'"
                          (click)="onSortChange('utilisationsActuelles')">
                    Popularité
                    <i class="bi" [class.bi-arrow-up]="filter.sortBy === 'utilisationsActuelles' && !filter.sortDesc"
                       [class.bi-arrow-down]="filter.sortBy === 'utilisationsActuelles' && filter.sortDesc"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <div class="card-body p-0">
            <div *ngIf="promotions.length === 0" class="text-center py-5 text-muted">
              <i class="bi bi-tags fs-1"></i>
              <p class="mt-2">Aucune promotion trouvée</p>
            </div>
            
            <div *ngFor="let promotion of promotions" class="promotion-item border-bottom">
              <div class="row g-3 p-3">
                <!-- Informations promotion -->
                <div class="col-md-5">
                  <div class="promotion-info">
                    <div class="d-flex align-items-center mb-2">
                      <span class="badge me-2" [class]="'bg-' + getTypeColor(promotion.type)">
                        <i class="bi" [class]="'bi-' + getTypeIcon(promotion.type)"></i>
                        {{ getTypeLibelle(promotion.type) }}
                      </span>
                      <span class="badge" [class]="'bg-' + getStatutColor(promotion)">
                        {{ getStatutLibelle(promotion) }}
                      </span>
                    </div>
                    <h6 class="mb-1">{{ promotion.nom }}</h6>
                    <p class="text-muted mb-1">Code: <strong>{{ promotion.code }}</strong></p>
                    <p class="mb-2" *ngIf="promotion.description">{{ promotion.description }}</p>
                    <div class="promotion-details">
                      <small class="text-muted d-block">
                        <i class="bi bi-calendar"></i> 
                        {{ promotion.dateDebut | date:'dd/MM/yyyy' }} - {{ promotion.dateFin | date:'dd/MM/yyyy' }}
                      </small>
                      <small class="text-muted d-block" *ngIf="promotion.fournisseurRaisonSociale">
                        <i class="bi bi-building"></i> {{ promotion.fournisseurRaisonSociale }}
                      </small>
                    </div>
                  </div>
                </div>
                
                <!-- Valeur et utilisation -->
                <div class="col-md-3">
                  <div class="promotion-value text-center">
                    <div class="value-display">
                      <span class="value-number">{{ formatValeur(promotion.type, promotion.valeur) }}</span>
                    </div>
                    <div class="usage-info mt-2">
                      <small class="text-muted">Utilisations</small>
                      <div class="mt-1">
                        <strong>{{ promotion.utilisationsActuelles }}</strong>
                        <span *ngIf="promotion.utilisationsMax"> / {{ promotion.utilisationsMax }}</span>
                        <span *ngIf="!promotion.utilisationsMax"> (illimitées)</span>
                      </div>
                      <div *ngIf="promotion.utilisationsMax" class="progress mt-1">
                        <div class="progress-bar" 
                             [style.width.%]="getTauxUtilisation(promotion)"
                             [class]="'bg-' + (getTauxUtilisation(promotion) > 80 ? 'warning' : 'primary')">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Statistiques -->
                <div class="col-md-2">
                  <div class="promotion-stats">
                    <small class="text-muted d-block">Économies</small>
                    <strong class="d-block">{{ promotion.montantTotalEconomise | number:'1.2-2' }}€</strong>
                    <small class="text-muted d-block mt-1">Commandes</small>
                    <strong class="d-block">{{ promotion.nombreCommandesImpactees }}</strong>
                  </div>
                </div>
                
                <!-- Actions -->
                <div class="col-md-2 text-end">
                  <div class="btn-group-vertical btn-group-sm w-100">
                    <button class="btn btn-outline-info" (click)="openDetailsModal(promotion)">
                      <i class="bi bi-eye"></i> Détails
                    </button>
                    <button class="btn btn-outline-primary" (click)="openEditModal(promotion)">
                      <i class="bi bi-pencil"></i> Modifier
                    </button>
                    <button class="btn" 
                            [class]="promotion.estActive ? 'btn-outline-warning' : 'btn-outline-success'"
                            (click)="togglePromotion(promotion)">
                      <i class="bi" [class]="promotion.estActive ? 'bi-pause' : 'bi-play'"></i>
                      {{ promotion.estActive ? 'Désactiver' : 'Activer' }}
                    </button>
                    <button class="btn btn-outline-danger" (click)="deletePromotion(promotion)">
                      <i class="bi bi-trash"></i> Supprimer
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div *ngIf="promotions.length > 0" class="pagination-section mt-3">
        <nav>
          <ul class="pagination justify-content-center">
            <li class="page-item" [class.disabled]="filter.page === 1">
              <button class="page-link" (click)="previousPage()">Précédent</button>
            </li>
            <li class="page-item active">
              <span class="page-link">{{ filter.page }}</span>
            </li>
            <li class="page-item">
              <button class="page-link" (click)="nextPage()">Suivant</button>
            </li>
          </ul>
        </nav>
      </div>
    </div>

    <!-- Onglet statistiques -->
    <div *ngIf="activeTab === 'stats'" class="tab-pane">
      <div class="row g-4">
        <!-- Répartition par type -->
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5><i class="bi bi-pie-chart"></i> Répartition par type</h5>
            </div>
            <div class="card-body">
              <div *ngFor="let item of getStatsArray()" class="type-stat">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <div class="d-flex align-items-center">
                    <i class="bi me-2" [class]="'bi-' + getTypeIcon(item.type)"></i>
                    <span>{{ item.libelle }}</span>
                  </div>
                  <span class="badge" [class]="'bg-' + getTypeColor(item.type)">{{ item.count }}</span>
                </div>
                <div class="progress mb-3">
                  <div class="progress-bar"
                       [class]="'bg-' + getTypeColor(item.type)"
                       [style.width.%]="stats ? (item.count / stats.totalPromotions) * 100 : 0">
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Promotions populaires -->
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h5><i class="bi bi-trophy"></i> Promotions populaires</h5>
            </div>
            <div class="card-body">
              <div *ngIf="promotionsPopulaires.length === 0" class="text-center text-muted py-3">
                <i class="bi bi-trophy"></i>
                <p class="mt-2">Aucune promotion populaire</p>
              </div>
              <div *ngFor="let promo of promotionsPopulaires; let i = index" class="popular-item">
                <div class="d-flex justify-content-between align-items-center">
                  <div>
                    <div class="d-flex align-items-center">
                      <span class="rank-badge">{{ i + 1 }}</span>
                      <div class="ms-2">
                        <h6 class="mb-0">{{ promo.nom }}</h6>
                        <small class="text-muted">{{ promo.code }}</small>
                      </div>
                    </div>
                  </div>
                  <div class="text-end">
                    <strong>{{ promo.utilisationsActuelles }}</strong>
                    <small class="text-muted d-block">utilisations</small>
                  </div>
                </div>
                <hr *ngIf="i < promotionsPopulaires.length - 1">
              </div>
            </div>
          </div>
        </div>

        <!-- Promotions expirant bientôt -->
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5><i class="bi bi-clock"></i> Promotions expirant dans les 7 prochains jours</h5>
            </div>
            <div class="card-body">
              <div *ngIf="promotionsExpirants.length === 0" class="text-center text-muted py-3">
                <i class="bi bi-check-circle text-success"></i>
                <p class="mt-2">Aucune promotion n'expire bientôt</p>
              </div>
              <div class="table-responsive" *ngIf="promotionsExpirants.length > 0">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>Promotion</th>
                      <th>Code</th>
                      <th>Fournisseur</th>
                      <th>Date fin</th>
                      <th>Jours restants</th>
                      <th>Utilisations</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let promo of promotionsExpirants">
                      <td>
                        <div>
                          <strong>{{ promo.nom }}</strong>
                          <span class="badge ms-2" [class]="'bg-' + getTypeColor(promo.type)">
                            {{ getTypeLibelle(promo.type) }}
                          </span>
                        </div>
                      </td>
                      <td><code>{{ promo.code }}</code></td>
                      <td>{{ promo.fournisseurRaisonSociale || 'Admin' }}</td>
                      <td>{{ promo.dateFin | date:'dd/MM/yyyy' }}</td>
                      <td>
                        <span class="badge"
                              [class]="getJoursRestants(promo.dateFin) <= 2 ? 'bg-danger' : 'bg-warning'">
                          {{ getJoursRestants(promo.dateFin) }} jour(s)
                        </span>
                      </td>
                      <td>{{ promo.utilisationsActuelles }}</td>
                      <td>
                        <button class="btn btn-sm btn-outline-primary" (click)="openEditModal(promo)">
                          <i class="bi bi-pencil"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Inclure les modales -->
<ng-container *ngTemplateOutlet="modalsTemplate"></ng-container>

<ng-template #modalsTemplate>
  <!-- Modal de création -->
  <div *ngIf="showCreateModal" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-plus"></i> Nouvelle Promotion
          </h5>
          <button type="button" class="btn-close" (click)="closeCreateModal()"></button>
        </div>

        <form [formGroup]="createForm" (ngSubmit)="createPromotion()">
          <div class="modal-body">
            <div class="row g-3">
              <div class="col-md-6">
                <label class="form-label">Code promotion *</label>
                <input type="text" class="form-control" formControlName="code"
                       placeholder="Ex: ADMIN2024" (blur)="checkCodeUniqueness()">
                <div class="form-text">Code unique pour identifier la promotion</div>
                <div *ngIf="createForm.get('code')?.errors?.['codeExists']" class="text-danger">
                  Ce code existe déjà
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label">Type de promotion *</label>
                <select class="form-select" formControlName="type">
                  <option [value]="TypePromotionGestion.Pourcentage">Pourcentage</option>
                  <option [value]="TypePromotionGestion.MontantFixe">Montant fixe</option>
                  <option [value]="TypePromotionGestion.Outlet">Outlet</option>
                </select>
              </div>

              <div class="col-12">
                <label class="form-label">Nom de la promotion *</label>
                <input type="text" class="form-control" formControlName="nom"
                       placeholder="Ex: Promotion spéciale admin">
              </div>

              <div class="col-12">
                <label class="form-label">Description</label>
                <textarea class="form-control" rows="3" formControlName="description"
                          placeholder="Description de la promotion..."></textarea>
              </div>

              <div class="col-md-6">
                <label class="form-label">Valeur *</label>
                <div class="input-group">
                  <input type="number" class="form-control" formControlName="valeur"
                         min="0" max="100" step="0.01">
                  <span class="input-group-text">
                    {{ createForm.get('type')?.value === TypePromotionGestion.MontantFixe ? '€' : '%' }}
                  </span>
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label">Montant minimum</label>
                <div class="input-group">
                  <input type="number" class="form-control" formControlName="montantMinimum"
                         min="0" step="0.01">
                  <span class="input-group-text">€</span>
                </div>
                <div class="form-text">Montant minimum de commande requis</div>
              </div>

              <div class="col-md-6">
                <label class="form-label">Date de début *</label>
                <input type="date" class="form-control" formControlName="dateDebut">
              </div>

              <div class="col-md-6">
                <label class="form-label">Date de fin *</label>
                <input type="date" class="form-control" formControlName="dateFin">
              </div>

              <div class="col-md-6">
                <label class="form-label">Nombre d'utilisations max</label>
                <input type="number" class="form-control" formControlName="utilisationsMax"
                       min="1" placeholder="Illimité si vide">
                <div class="form-text">Laissez vide pour un usage illimité</div>
              </div>

              <div class="col-md-6">
                <label class="form-label">ID Fournisseur</label>
                <input type="number" class="form-control" formControlName="fournisseurId"
                       placeholder="Laissez vide pour promotion globale">
                <div class="form-text">Promotion spécifique à un fournisseur</div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
              Annuler
            </button>
            <button type="submit" class="btn btn-primary" [disabled]="!createForm.valid">
              <i class="bi bi-check"></i> Créer la promotion
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Modal d'édition -->
  <div *ngIf="showEditModal" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-pencil"></i> Modifier la promotion
          </h5>
          <button type="button" class="btn-close" (click)="closeEditModal()"></button>
        </div>

        <form [formGroup]="editForm" (ngSubmit)="updatePromotion()">
          <div class="modal-body">
            <div class="row g-3">
              <div class="col-12">
                <label class="form-label">Nom de la promotion *</label>
                <input type="text" class="form-control" formControlName="nom">
              </div>

              <div class="col-12">
                <label class="form-label">Description</label>
                <textarea class="form-control" rows="3" formControlName="description"></textarea>
              </div>

              <div class="col-md-6">
                <label class="form-label">Valeur *</label>
                <div class="input-group">
                  <input type="number" class="form-control" formControlName="valeur"
                         min="0" max="100" step="0.01">
                  <span class="input-group-text">
                    {{ selectedPromotion?.type === TypePromotionGestion.MontantFixe ? '€' : '%' }}
                  </span>
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label">Montant minimum</label>
                <div class="input-group">
                  <input type="number" class="form-control" formControlName="montantMinimum"
                         min="0" step="0.01">
                  <span class="input-group-text">€</span>
                </div>
              </div>

              <div class="col-md-6">
                <label class="form-label">Date de début *</label>
                <input type="date" class="form-control" formControlName="dateDebut">
              </div>

              <div class="col-md-6">
                <label class="form-label">Date de fin *</label>
                <input type="date" class="form-control" formControlName="dateFin">
              </div>

              <div class="col-md-6">
                <label class="form-label">Nombre d'utilisations max</label>
                <input type="number" class="form-control" formControlName="utilisationsMax"
                       min="1" placeholder="Illimité si vide">
              </div>

              <div class="col-md-6">
                <div class="form-check mt-4">
                  <input class="form-check-input" type="checkbox" formControlName="estActive">
                  <label class="form-check-label">
                    Promotion active
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
              Annuler
            </button>
            <button type="submit" class="btn btn-primary" [disabled]="!editForm.valid">
              <i class="bi bi-check"></i> Mettre à jour
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Modal de détails -->
  <div *ngIf="showDetailsModal && selectedPromotion" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-eye"></i> Détails de la promotion
          </h5>
          <button type="button" class="btn-close" (click)="closeDetailsModal()"></button>
        </div>

        <div class="modal-body">
          <div class="row g-4">
            <!-- Informations générales -->
            <div class="col-md-6">
              <h6>Informations générales</h6>
              <table class="table table-sm">
                <tr>
                  <td><strong>Nom:</strong></td>
                  <td>{{ selectedPromotion.nom }}</td>
                </tr>
                <tr>
                  <td><strong>Code:</strong></td>
                  <td><code>{{ selectedPromotion.code }}</code></td>
                </tr>
                <tr>
                  <td><strong>Type:</strong></td>
                  <td>
                    <span class="badge" [class]="'bg-' + getTypeColor(selectedPromotion.type)">
                      {{ getTypeLibelle(selectedPromotion.type) }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td><strong>Valeur:</strong></td>
                  <td>{{ formatValeur(selectedPromotion.type, selectedPromotion.valeur) }}</td>
                </tr>
                <tr>
                  <td><strong>Statut:</strong></td>
                  <td>
                    <span class="badge" [class]="'bg-' + getStatutColor(selectedPromotion)">
                      {{ getStatutLibelle(selectedPromotion) }}
                    </span>
                  </td>
                </tr>
              </table>
            </div>

            <!-- Conditions et limites -->
            <div class="col-md-6">
              <h6>Conditions et limites</h6>
              <table class="table table-sm">
                <tr>
                  <td><strong>Date début:</strong></td>
                  <td>{{ selectedPromotion.dateDebut | date:'dd/MM/yyyy HH:mm' }}</td>
                </tr>
                <tr>
                  <td><strong>Date fin:</strong></td>
                  <td>{{ selectedPromotion.dateFin | date:'dd/MM/yyyy HH:mm' }}</td>
                </tr>
                <tr>
                  <td><strong>Montant minimum:</strong></td>
                  <td>{{ selectedPromotion.montantMinimum ? (selectedPromotion.montantMinimum + '€') : 'Aucun' }}</td>
                </tr>
                <tr>
                  <td><strong>Utilisations max:</strong></td>
                  <td>{{ selectedPromotion.utilisationsMax || 'Illimitées' }}</td>
                </tr>
                <tr>
                  <td><strong>Utilisations actuelles:</strong></td>
                  <td>{{ selectedPromotion.utilisationsActuelles }}</td>
                </tr>
              </table>
            </div>

            <!-- Description -->
            <div class="col-12" *ngIf="selectedPromotion.description">
              <h6>Description</h6>
              <p class="border p-3 rounded bg-light">{{ selectedPromotion.description }}</p>
            </div>

            <!-- Statistiques -->
            <div class="col-12">
              <h6>Statistiques d'utilisation</h6>
              <div class="row g-3">
                <div class="col-md-4">
                  <div class="stat-box">
                    <div class="stat-value">{{ selectedPromotion.montantTotalEconomise | number:'1.2-2' }}€</div>
                    <div class="stat-label">Économies totales</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="stat-box">
                    <div class="stat-value">{{ selectedPromotion.nombreCommandesImpactees }}</div>
                    <div class="stat-label">Commandes impactées</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="stat-box">
                    <div class="stat-value">{{ getTauxUtilisation(selectedPromotion) | number:'1.1-1' }}%</div>
                    <div class="stat-label">Taux d'utilisation</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" (click)="closeDetailsModal()">
            Fermer
          </button>
          <button type="button" class="btn btn-primary" (click)="openEditModal(selectedPromotion); closeDetailsModal()">
            <i class="bi bi-pencil"></i> Modifier
          </button>
        </div>
      </div>
    </div>
  </div>
</ng-template>
