using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Services.Implementations
{
    public class CommandeService : ICommandeService
    {
        private readonly AppDbContext _context;
        private readonly IPromotionService _promotionService;
        private readonly IPaiementService _paiementService;

        public CommandeService(AppDbContext context,
                             IPromotionService promotionService,
                             IPaiementService paiementService)
        {
            _context = context;
            _promotionService = promotionService;
            _paiementService = paiementService;
        }

        public async Task<Commande> CreerCommandePanier(Panier panier, int userId, string? codePromo = null)
        {
            Console.WriteLine("⚠️ ATTENTION: Utilisation de l'ancienne méthode CreerCommandePanier");
            Console.WriteLine($"⚠️ Stack trace: {Environment.StackTrace}");

            // Vérifications de sécurité
            if (panier == null)
            {
                Console.WriteLine("❌ Panier est null!");
                throw new ArgumentNullException(nameof(panier), "Le panier ne peut pas être null");
            }

            if (panier.Items == null)
            {
                throw new ArgumentException("La collection Items du panier ne peut pas être null", nameof(panier));
            }

            if (!panier.Items.Any())
            {
                throw new ArgumentException("Le panier ne peut pas être vide", nameof(panier));
            }

            Console.WriteLine($"🛒 Conversion panier vers détails commande - {panier.Items.Count} items");

            // Conversion sécurisée vers DetailsCommandeDto
            var detailsCommandes = new List<DetailsCommandeDto>();

            foreach (var item in panier.Items)
            {
                if (item == null)
                {
                    Console.WriteLine("⚠️ Item null trouvé dans le panier, ignoré");
                    continue;
                }

                Console.WriteLine($"🛒 Traitement item: ProduitId={item.ProduitId}, Quantite={item.Quantite}");

                detailsCommandes.Add(new DetailsCommandeDto
                {
                    ProduitId = item.ProduitId,
                    Quantite = item.Quantite
                });
            }

            if (!detailsCommandes.Any())
            {
                throw new ArgumentException("Aucun item valide trouvé dans le panier");
            }

            Console.WriteLine($"🛒 Redirection vers CreerCommandeDirecte avec {detailsCommandes.Count} items");
            return await CreerCommandeDirecte(userId, detailsCommandes, codePromo);
        }

        // Nouvelle méthode pour créer une commande directement depuis les détails
        public async Task<Commande> CreerCommandeDirecte(int clientId, List<DetailsCommandeDto> detailsCommandes, string? codePromo = null)
        {
            Console.WriteLine($"🛒 Création commande directe pour client {clientId} avec {detailsCommandes.Count} items");

            // Récupérer les détails des produits depuis la base de données
            var produitsIds = detailsCommandes.Select(d => d.ProduitId).ToList();
            Console.WriteLine($"🛒 Produits demandés: {string.Join(", ", produitsIds)}");

            var produits = await _context.Produits
                .Include(p => p.TauxTVA)
                .Where(p => produitsIds.Contains(p.Id))
                .ToDictionaryAsync(p => p.Id, p => p);

            Console.WriteLine($"🛒 Produits trouvés: {produits.Count}/{produitsIds.Count}");

            // Vérifier que tous les produits existent
            var produitsManquants = produitsIds.Where(id => !produits.ContainsKey(id)).ToList();
            if (produitsManquants.Any())
            {
                Console.WriteLine($"❌ Produits manquants: {string.Join(", ", produitsManquants)}");
                throw new ArgumentException($"Produits introuvables: {string.Join(", ", produitsManquants)}");
            }

            var commande = new Commande
            {
                ClientId = clientId,
                DateCreation = DateTime.UtcNow,
                Statut = StatutCommande.Brouillon,
                FraisLivraison = 0, // Initialiser à 0, sera mis à jour après division
                DetailsCommandes = detailsCommandes.Select(detail =>
                {
                    var produit = produits[detail.ProduitId];
                    Console.WriteLine($"🛒 Traitement produit {produit.Nom}: Prix={produit.PrixVenteHT}, TVA={produit.TauxTVA?.Taux ?? 0}%");

                    return new DetailsCommande
                    {
                        ProduitId = detail.ProduitId,
                        Quantite = detail.Quantite,
                        PrixUnitaireHT = produit.PrixVenteHT,
                        TauxTVAValue = produit.TauxTVA?.Taux ?? 0m
                        // Ne pas définir Produit pour éviter les problèmes de tracking
                    };
                }).ToList()
            };

            // TODO: Appliquer promotions (temporairement désactivé)
            // await AppliquerPromotions(commande, codePromo);
            Console.WriteLine($"⚠️ Application des promotions temporairement désactivée pour diagnostic");

            commande.MontantTotal = commande.DetailsCommandes
                .Sum(d => (d.PrixUnitaireHT * (1 + d.TauxTVAValue / 100)) * d.Quantite);

            Console.WriteLine($"🛒 Montant total calculé: {commande.MontantTotal:C}");

            try
            {
                _context.Commandes.Add(commande);
                Console.WriteLine($"🛒 Commande ajoutée au contexte, sauvegarde en cours...");
                await _context.SaveChangesAsync();
                Console.WriteLine($"✅ Commande {commande.Id} créée avec succès");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de la sauvegarde de la commande: {ex.Message}");
                Console.WriteLine($"❌ Inner exception: {ex.InnerException?.Message}");
                throw;
            }

            // Diviser automatiquement la commande par fournisseur
            Console.WriteLine($"🏭 Début de la division automatique de la commande {commande.Id}");
            Console.WriteLine($"📊 Commande créée avec {commande.DetailsCommandes.Count} détails");
            await DiviserCommandeParFournisseur(commande.Id);
            Console.WriteLine($"✅ Division automatique terminée pour la commande {commande.Id}");

            return commande;
        }

        // Méthode pour diviser automatiquement une commande par fournisseur
        private async Task DiviserCommandeParFournisseur(int commandeId)
        {
            try
            {
                Console.WriteLine($"🏭 Début de la division de la commande {commandeId} par fournisseur");

            // Récupérer la commande avec ses détails et les produits (avec chargement explicite)
            var commande = await _context.Commandes
                .Include(c => c.DetailsCommandes)
                    .ThenInclude(d => d.Produit)
                        .ThenInclude(p => p.Fournisseur)
                .FirstOrDefaultAsync(c => c.Id == commandeId);

            // Vérification supplémentaire : recharger les produits si nécessaire
            if (commande != null)
            {
                foreach (var detail in commande.DetailsCommandes)
                {
                    if (detail.Produit == null)
                    {
                        detail.Produit = await _context.Produits
                            .Include(p => p.Fournisseur)
                            .FirstOrDefaultAsync(p => p.Id == detail.ProduitId);
                    }
                }
            }

            Console.WriteLine($"🔍 Commande trouvée: {commande != null}");

            if (commande == null)
            {
                Console.WriteLine($"❌ Commande {commandeId} introuvable");
                return;
            }

            Console.WriteLine($"🔍 Détails commande: {commande.DetailsCommandes.Count} items");
            Console.WriteLine($"🔍 Statut commande: {commande.Statut}");

            // Debug: Vérifier les détails de la commande avec plus de détails
            Console.WriteLine($"🔍 === DIAGNOSTIC DÉTAILLÉ ===");
            foreach (var detail in commande.DetailsCommandes)
            {
                Console.WriteLine($"🔍 Détail {detail.Id}:");
                Console.WriteLine($"   - ProduitId: {detail.ProduitId}");
                Console.WriteLine($"   - Produit: {detail.Produit?.Nom ?? "NULL"}");
                Console.WriteLine($"   - FournisseurId: {detail.Produit?.FournisseurId ?? 0}");
                Console.WriteLine($"   - Fournisseur: {detail.Produit?.Fournisseur?.RaisonSociale ?? "NULL"}");
                Console.WriteLine($"   - Quantité: {detail.Quantite}");
                Console.WriteLine($"   - Prix HT: {detail.PrixUnitaireHT:C}");
            }
            Console.WriteLine($"🔍 === FIN DIAGNOSTIC ===");

            // Vérifier et assigner automatiquement les fournisseurs manquants
            var produitsSansFournisseur = commande.DetailsCommandes
                .Where(d => d.Produit == null || d.Produit.FournisseurId <= 0)
                .ToList();

            if (produitsSansFournisseur.Any())
            {
                Console.WriteLine($"⚠️ {produitsSansFournisseur.Count} produits sans fournisseur, assignation automatique...");

                // Récupérer le premier fournisseur disponible
                var fournisseurParDefaut = await _context.Fournisseurs.FirstOrDefaultAsync();
                if (fournisseurParDefaut != null)
                {
                    foreach (var detail in produitsSansFournisseur)
                    {
                        if (detail.Produit != null)
                        {
                            detail.Produit.FournisseurId = fournisseurParDefaut.Id;
                            Console.WriteLine($"📦 Produit {detail.Produit.Nom} assigné au fournisseur {fournisseurParDefaut.RaisonSociale}");
                        }
                    }
                    await _context.SaveChangesAsync();
                    Console.WriteLine($"✅ Assignation automatique terminée");
                }
                else
                {
                    Console.WriteLine($"❌ Aucun fournisseur disponible pour l'assignation automatique");
                }
            }

            // Grouper les détails par fournisseur
            var detailsParFournisseur = commande.DetailsCommandes
                .Where(d => d.Produit != null && d.Produit.FournisseurId > 0)
                .GroupBy(d => d.Produit.FournisseurId)
                .ToList();

            Console.WriteLine($"🏭 {detailsParFournisseur.Count} fournisseurs différents trouvés après vérification");

            if (detailsParFournisseur.Count == 0)
            {
                Console.WriteLine($"❌ Aucun produit avec fournisseur valide trouvé même après assignation");
                return;
            }

            foreach (var groupe in detailsParFournisseur)
            {
                var fournisseurId = groupe.Key;
                var detailsFournisseur = groupe.ToList();

                Console.WriteLine($"🏭 Création commande fournisseur pour fournisseur {fournisseurId} avec {detailsFournisseur.Count} produits");

                // Afficher les détails des produits pour ce fournisseur
                foreach (var detail in detailsFournisseur)
                {
                    Console.WriteLine($"   📦 Produit {detail.ProduitId}: {detail.Produit?.Nom} - Qté: {detail.Quantite} - Prix: {detail.PrixUnitaireHT:C}");
                }

                // Récupérer les frais de livraison du fournisseur
                var fraisLivraison = await CalculerFraisLivraison(fournisseurId);
                Console.WriteLine($"🚚 Frais de livraison fournisseur {fournisseurId}: {fraisLivraison:C}");

                // Créer une commande fournisseur
                var commandeFournisseur = new CommandeFournisseur
                {
                    CommandeClientId = commandeId,
                    FournisseurId = fournisseurId,
                    Reference = $"F{fournisseurId}-{DateTime.Now:MMddHHmm}",
                    DateCreation = DateTime.UtcNow,
                    Statut = StatutCommandeFournisseur.EnAttente,
                    FraisLivraison = fraisLivraison
                };

                // Ajouter la commande fournisseur d'abord pour obtenir l'ID
                _context.CommandesFournisseurs.Add(commandeFournisseur);
                await _context.SaveChangesAsync(); // Sauvegarder pour obtenir l'ID

                Console.WriteLine($"🏭 Commande fournisseur créée avec ID: {commandeFournisseur.Id}");

                // Maintenant créer les lignes de commande avec le bon CommandeId
                var lignesCommande = detailsFournisseur.Select(detail => new LigneCommandeFournisseur
                {
                    CommandeId = commandeFournisseur.Id, // Utiliser l'ID de la commande fournisseur
                    ProduitId = detail.ProduitId,
                    Quantite = detail.Quantite,
                    PrixUnitaire = detail.PrixUnitaireHT,
                    MontantLigne = detail.PrixUnitaireHT * detail.Quantite
                }).ToList();

                // Ajouter les lignes de commande
                _context.LignesCommandeFournisseur.AddRange(lignesCommande);
                commandeFournisseur.LignesCommande = lignesCommande;

                // Calculer le montant total de la commande fournisseur (produits + frais de livraison)
                var montantProduits = lignesCommande.Sum(l => l.MontantLigne);
                commandeFournisseur.MontantTotal = montantProduits + fraisLivraison;

                Console.WriteLine($"🏭 Montant commande fournisseur {fournisseurId}: {commandeFournisseur.MontantTotal:C}");
                Console.WriteLine($"🏭 Référence: {commandeFournisseur.Reference}");

                // Sauvegarder les lignes de commande
                await _context.SaveChangesAsync();
                Console.WriteLine($"✅ Lignes de commande créées pour fournisseur {fournisseurId}");
            }

            Console.WriteLine($"✅ Division de la commande {commandeId} terminée - {detailsParFournisseur.Count} commandes fournisseurs créées automatiquement");

            // Vérifier que les commandes ont bien été sauvegardées
            var commandesSauvegardees = await _context.CommandesFournisseurs
                .Where(cf => cf.CommandeClientId == commandeId)
                .CountAsync();
            Console.WriteLine($"🔍 Vérification: {commandesSauvegardees} commandes fournisseurs trouvées en base pour la commande {commandeId}");

            // Mettre à jour les frais de livraison totaux de la commande principale
            await MettreAJourFraisLivraisonCommande(commandeId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de la division de la commande {commandeId}: {ex.Message}");
                Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                throw; // Relancer l'exception pour que l'appelant puisse la gérer
            }
        }

        public async Task<Commande> GetCommandeByIdAsync(int id)
        {
            var commande = await _context.Commandes
                .Include(c => c.DetailsCommandes)
                    .ThenInclude(d => d.Produit)
                .Include(c => c.PromotionsUtilisees)
                .Include(c => c.CommandesFournisseurs)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (commande == null)
            {
                throw new KeyNotFoundException($"Commande avec Id {id} non trouvée.");
            }

            return commande;
        }
        private async Task AppliquerPromotions(Commande commande, string? codePromo)
        {
            // Récupérer tous les produits avec leurs promotions en une seule requête
            var produitsIds = commande.DetailsCommandes.Select(d => d.ProduitId).ToList();
            var produits = await _context.Produits
                .Include(p => p.TauxTVA)
                .Include(p => p.PromotionsApplicables)
                .Where(p => produitsIds.Contains(p.Id))
                .ToDictionaryAsync(p => p.Id, p => p);

            foreach (var detail in commande.DetailsCommandes)
            {
                if (!produits.TryGetValue(detail.ProduitId, out var produit))
                    continue;

                try
                {
                    decimal prixFinal = await _promotionService.GetPrixFinalAsync(
                        produit.Id,
                        codePromo: codePromo);

                    detail.PrixUnitaireHT = prixFinal / (1 + (produit.TauxTVA?.Taux ?? 0) / 100);
                }
                catch (Exception ex)
                {
                    // Log l'erreur mais continue le traitement
                    Console.WriteLine($"Erreur lors de l'application des promotions pour le produit {produit.Id}: {ex.Message}");
                }
            }
        }
        //public async Task<decimal> AppliquerCodePromoAsync(string code, decimal montantTotal)
        //{
        //    var promo = await _context.Promotions
        //        .FirstOrDefaultAsync(p => p.TypePromotion == TypePromotion.CodePromo
        //            && p.Code == code && p.EstValide());

        //    if (promo == null)
        //        throw new ArgumentException("Code promo invalide");

        //    var montantApresRemise = montantTotal * (1 - promo.PourcentageRemise / 100m);
        //    return Math.Round(montantApresRemise, 2);
        //}

        public async Task SplitCommandeParFournisseur(int commandeId)
        {
            // Cette méthode est maintenant un alias pour DiviserCommandeParFournisseur
            // pour maintenir la compatibilité avec l'interface
            Console.WriteLine($"⚠️ Utilisation de l'ancienne méthode SplitCommandeParFournisseur - redirection vers DiviserCommandeParFournisseur");
            await DiviserCommandeParFournisseur(commandeId);
        }
        private async Task MettreAJourFraisLivraisonCommande(int commandeId)
        {
            try
            {
                Console.WriteLine($"🚚 Mise à jour des frais de livraison pour la commande {commandeId}");

            // Calculer le total des frais de livraison de toutes les commandes fournisseurs
            var totalFraisLivraison = await _context.CommandesFournisseurs
                .Where(cf => cf.CommandeClientId == commandeId)
                .SumAsync(cf => cf.FraisLivraison);

            Console.WriteLine($"🚚 Total frais de livraison calculé: {totalFraisLivraison:C}");

            // Mettre à jour la commande principale
            var commande = await _context.Commandes.FindAsync(commandeId);
            if (commande != null)
            {
                commande.FraisLivraison = totalFraisLivraison;
                commande.MontantTotal += totalFraisLivraison; // Ajouter les frais au montant total

                Console.WriteLine($"🚚 Nouveau montant total commande {commandeId}: {commande.MontantTotal:C}");
                await _context.SaveChangesAsync();
            }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de la mise à jour des frais de livraison pour la commande {commandeId}: {ex.Message}");
                // Ne pas relancer l'exception pour ne pas bloquer la création de commande
            }
        }
        public async Task<bool> FinaliserCommande(int commandeId, string tokenPaiement)
        {
            var commande = await _context.Commandes
                .Include(c => c.PromotionsUtilisees)
                .FirstOrDefaultAsync(c => c.Id == commandeId);

            if (commande == null) return false;
            var paiement = await _paiementService.ProcesserPaiementAsync(commandeId, tokenPaiement);
            commande.Statut = paiement.Statut == StatutPaiement.Reussi
                ? StatutCommande.Validee
                : StatutCommande.Annulee;
            if (commande.Statut == StatutCommande.Validee)
            {
                await SplitCommandeParFournisseur(commandeId);
            }
            await _context.SaveChangesAsync();
            return commande.Statut == StatutCommande.Validee;
        }

        public async Task ForcerDivisionCommande(int commandeId)
        {
            Console.WriteLine($"🔧 SERVICE - Forçage de la division pour la commande {commandeId}");
            Console.WriteLine($"🔧 SERVICE - Début de ForcerDivisionCommande");

            // Supprimer les anciennes commandes fournisseurs si elles existent
            var anciennesCommandesFournisseurs = await _context.CommandesFournisseurs
                .Where(cf => cf.CommandeClientId == commandeId)
                .ToListAsync();

            if (anciennesCommandesFournisseurs.Any())
            {
                Console.WriteLine($"🗑️ Suppression de {anciennesCommandesFournisseurs.Count} anciennes commandes fournisseurs");
                _context.CommandesFournisseurs.RemoveRange(anciennesCommandesFournisseurs);
                await _context.SaveChangesAsync();
            }

            // Relancer la division
            await DiviserCommandeParFournisseur(commandeId);
            Console.WriteLine($"✅ Division forcée terminée pour la commande {commandeId}");
        }

        public async Task<decimal> CalculerFraisLivraison(int fournisseurId)
        {
            try
            {
                // Récupérer les frais de livraison du fournisseur
                var fournisseur = await _context.Fournisseurs
                    .FirstOrDefaultAsync(f => f.Id == fournisseurId);

                if (fournisseur != null)
                {
                    return fournisseur.FraisLivraisonBase;
                }

                // Frais par défaut si fournisseur non trouvé
                return 5.0m;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Erreur lors du calcul des frais de livraison pour le fournisseur {fournisseurId}: {ex.Message}");
                return 5.0m; // Frais par défaut
            }
        }

        public async Task<bool> SupprimerCommande(int commandeId)
        {
            try
            {
                Console.WriteLine($"🗑️ Début suppression de la commande {commandeId}");

                // Récupérer la commande avec toutes ses relations
                var commande = await _context.Commandes
                    .Include(c => c.DetailsCommandes)
                    .Include(c => c.CommandesFournisseurs)
                        .ThenInclude(cf => cf.LignesCommande)
                    .Include(c => c.PromotionsUtilisees)
                    .FirstOrDefaultAsync(c => c.Id == commandeId);

                if (commande == null)
                {
                    Console.WriteLine($"❌ Commande {commandeId} introuvable");
                    return false;
                }

                // Vérifier si la commande peut être supprimée
                if (commande.Statut == StatutCommande.Expediee ||
                    commande.Statut == StatutCommande.Livree)
                {
                    Console.WriteLine($"❌ Impossible de supprimer la commande {commandeId} - Statut: {commande.Statut}");
                    return false;
                }

                Console.WriteLine($"🔍 Commande trouvée - Statut: {commande.Statut}");
                Console.WriteLine($"🔍 {commande.DetailsCommandes.Count} détails de commande");
                Console.WriteLine($"🔍 {commande.CommandesFournisseurs.Count} commandes fournisseurs");

                // Supprimer les lignes de commandes fournisseurs
                foreach (var commandeFournisseur in commande.CommandesFournisseurs)
                {
                    if (commandeFournisseur.LignesCommande.Any())
                    {
                        Console.WriteLine($"🗑️ Suppression de {commandeFournisseur.LignesCommande.Count} lignes pour la commande fournisseur {commandeFournisseur.Id}");
                        _context.LignesCommandeFournisseur.RemoveRange(commandeFournisseur.LignesCommande);
                    }
                }

                // Supprimer les commandes fournisseurs
                if (commande.CommandesFournisseurs.Any())
                {
                    Console.WriteLine($"🗑️ Suppression de {commande.CommandesFournisseurs.Count} commandes fournisseurs");
                    _context.CommandesFournisseurs.RemoveRange(commande.CommandesFournisseurs);
                }

                // Supprimer les promotions utilisées
                if (commande.PromotionsUtilisees.Any())
                {
                    Console.WriteLine($"🗑️ Suppression de {commande.PromotionsUtilisees.Count} promotions utilisées");
                    _context.PromotionsUtilisees.RemoveRange(commande.PromotionsUtilisees);
                }

                // Supprimer les détails de commande
                if (commande.DetailsCommandes.Any())
                {
                    Console.WriteLine($"🗑️ Suppression de {commande.DetailsCommandes.Count} détails de commande");
                    _context.DetailsCommandes.RemoveRange(commande.DetailsCommandes);
                }

                // Supprimer la commande principale
                Console.WriteLine($"🗑️ Suppression de la commande principale {commandeId}");
                _context.Commandes.Remove(commande);

                // Sauvegarder les changements
                await _context.SaveChangesAsync();

                Console.WriteLine($"✅ Commande {commandeId} supprimée avec succès");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de la suppression de la commande {commandeId}: {ex.Message}");
                Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                return false;
            }
        }
    }
}