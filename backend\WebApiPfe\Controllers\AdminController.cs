﻿using Microsoft.AspNetCore.Mvc;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AdminController : ControllerBase
    {
        private readonly IAdminService _adminService;

        public AdminController(IAdminService adminService)
        {
            _adminService = adminService;
        }

        // 1. Gestion des utilisateurs
        [HttpGet("utilisateurs")]
        public async Task<IActionResult> GetUtilisateurs(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? search = null,
            [FromQuery] string? role = null,
            [FromQuery] bool? estActif = null)
        {
            try
            {
                Console.WriteLine($"🔍 AdminController: GetUtilisateurs - Page: {page}, PageSize: {pageSize}, Search: {search}, Role: {role}, EstActif: {estActif}");

                var result = await _adminService.GetUtilisateursPageAsync(page, pageSize, search, role, estActif);

                Console.WriteLine($"✅ AdminController: Retour de {result.Utilisateurs.Count} utilisateurs sur {result.TotalCount} total");

                return Ok(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminController: Erreur dans GetUtilisateurs: {ex.Message}");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPatch("utilisateurs/{id}/toggle")]
        public async Task<IActionResult> ToggleActivationUtilisateur(int id)
        {
            var result = await _adminService.ToggleActivationUtilisateurAsync(id);
            if (!result) return NotFound();
            return NoContent();
        }

        [HttpDelete("utilisateurs/{id}")]
        public async Task<IActionResult> SupprimerUtilisateur(int id)
        {
            var result = await _adminService.SupprimerUtilisateurAsync(id);
            if (!result) return NotFound();
            return NoContent();
        }

        // 2. Gestion des catégories
        [HttpPost("categories")]
        public async Task<IActionResult> AjouterCategorie(CategorieGestionDto dto)
        {
            var result = await _adminService.AjouterCategorieAsync(dto);
            return result ? Ok() : BadRequest();
        }

        [HttpPut("categories/{id}")]
        public async Task<IActionResult> ModifierCategorie(int id, CategorieGestionDto dto)
        {
            var result = await _adminService.ModifierCategorieAsync(id, dto);
            return result ? Ok() : NotFound();
        }

        [HttpDelete("categories/{id}")]
        public async Task<IActionResult> SupprimerCategorie(int id)
        {
            var result = await _adminService.SupprimerCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("categories/{id}/valider")]
        public async Task<IActionResult> ValiderCategorie(int id)
        {
            try
            {
                Console.WriteLine($"🔍 Tentative de validation de la catégorie ID: {id}");
                var result = await _adminService.ValiderCategorieAsync(id);
                Console.WriteLine($"✅ Résultat de la validation: {result}");
                return result ? Ok() : NotFound();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur lors de la validation de la catégorie {id}: {ex.Message}");
                Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPatch("categories/{id}/refuser")]
        public async Task<IActionResult> RefuserCategorie(int id)
        {
            var result = await _adminService.RefuserCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        // 3. Gestion des sous-catégories
        [HttpPost("souscategories")]
        public async Task<IActionResult> AjouterSousCategorie(SousCategorieGestionDto dto)
        {
            var result = await _adminService.AjouterSousCategorieAsync(dto);
            return result ? Ok() : BadRequest();
        }

        [HttpPut("souscategories/{id}")]
        public async Task<IActionResult> ModifierSousCategorie(int id, SousCategorieGestionDto dto)
        {
            var result = await _adminService.ModifierSousCategorieAsync(id, dto);
            return result ? Ok() : NotFound();
        }

        [HttpDelete("souscategories/{id}")]
        public async Task<IActionResult> SupprimerSousCategorie(int id)
        {
            var result = await _adminService.SupprimerSousCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("souscategories/{id}/valider")]
        public async Task<IActionResult> ValiderSousCategorie(int id)
        {
            var result = await _adminService.ValidersousCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        [HttpPatch("souscategories/{id}/refuser")]
        public async Task<IActionResult> RefuserSousCategorie(int id)
        {
            var result = await _adminService.RefusersousCategorieAsync(id);
            return result ? Ok() : NotFound();
        }

        // 4. Commandes / Transactions
        [HttpGet("commandes")]
        public async Task<IActionResult> GetCommandes()
        {
            var commandes = await _adminService.GetCommandesAsync();
            return Ok(commandes);
        }

        [HttpPatch("commandes/{id}/annuler")]
        public async Task<IActionResult> AnnulerCommande(int id)
        {
            var result = await _adminService.AnnulerCommandeAsync(id);
            return result ? Ok() : NotFound();
        }

        // 5. Gestion des produits
        [HttpGet("produits")]
        public async Task<IActionResult> GetProduits(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? search = null,
            [FromQuery] string? statut = null,
            [FromQuery] bool? stockCritique = null,
            [FromQuery] string? fournisseur = null)
        {
            try
            {
                Console.WriteLine($"🔍 AdminController: GetProduits - Page: {page}, PageSize: {pageSize}");

                var result = await _adminService.GetProduitsPageAsync(page, pageSize, search, statut, stockCritique, fournisseur);

                Console.WriteLine($"✅ AdminController: Retour de {result.Produits.Count} produits sur {result.TotalCount} total");

                return Ok(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminController: Erreur dans GetProduits: {ex.Message}");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPatch("produits/{produitId}/valider")]
        public async Task<IActionResult> ValiderProduit(int produitId)
        {
            try
            {
                Console.WriteLine($"🔍 AdminController: Validation produit ID: {produitId}");
                var result = await _adminService.ValiderProduitAsync(produitId);
                Console.WriteLine($"✅ AdminController: Résultat validation: {result}");
                return result ? Ok(new { message = "Produit validé avec succès" }) : NotFound(new { error = "Produit non trouvé" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminController: Erreur validation produit: {ex.Message}");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPatch("produits/{produitId}/refuser")]
        public async Task<IActionResult> RefuserProduit(int produitId, [FromBody] string? raison = null)
        {
            try
            {
                Console.WriteLine($"🔍 AdminController: Refus produit ID: {produitId}");
                var result = await _adminService.RefuserProduitAsync(produitId, raison);
                Console.WriteLine($"✅ AdminController: Résultat refus: {result}");
                return result ? Ok(new { message = "Produit refusé" }) : NotFound(new { error = "Produit non trouvé" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminController: Erreur refus produit: {ex.Message}");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPatch("produits/{produitId}/enavant")]
        public async Task<IActionResult> MettreEnAvantProduit(int produitId)
        {
            try
            {
                Console.WriteLine($"🔍 AdminController: Mise en avant produit ID: {produitId}");
                var result = await _adminService.MettreEnAvantProduitAsync(produitId);
                Console.WriteLine($"✅ AdminController: Résultat mise en avant: {result}");
                return result ? Ok(new { message = "Produit mis en avant" }) : NotFound(new { error = "Produit non trouvé" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminController: Erreur mise en avant: {ex.Message}");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPatch("produits/{produitId}/retirer-avant")]
        public async Task<IActionResult> RetirerMiseEnAvantProduit(int produitId)
        {
            try
            {
                var result = await _adminService.RetirerMiseEnAvantProduitAsync(produitId);
                return result ? Ok(new { message = "Mise en avant retirée" }) : NotFound(new { error = "Produit non trouvé" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPut("produits/{produitId}/moderer")]
        public async Task<IActionResult> ModererContenuProduit(int produitId, [FromBody] ProduitModerationDto dto)
        {
            try
            {
                var result = await _adminService.ModererContenuProduitAsync(produitId, dto.NouveauNom, dto.NouvelleDescription);
                return result ? Ok(new { message = "Contenu modéré" }) : NotFound(new { error = "Produit non trouvé" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpDelete("produits/{produitId}")]
        public async Task<IActionResult> SupprimerProduit(int produitId)
        {
            try
            {
                var result = await _adminService.SupprimerProduitAsync(produitId);
                return result ? Ok(new { message = "Produit supprimé" }) : NotFound(new { error = "Produit non trouvé" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("produits/stock-critique")]
        public async Task<IActionResult> GetProduitsStockCritique([FromQuery] int seuil = 10)
        {
            try
            {
                var produits = await _adminService.GetProduitsStockCritiqueAsync(seuil);
                return Ok(produits);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPatch("produits/{produitId}/stock")]
        public async Task<IActionResult> UpdateStockProduit(int produitId, [FromBody] int nouveauStock)
        {
            try
            {
                var result = await _adminService.UpdateStockProduitAsync(produitId, nouveauStock);
                return result ? Ok(new { message = "Stock mis à jour" }) : NotFound(new { error = "Produit non trouvé" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("produits/en-avant")]
        public async Task<IActionResult> GetProduitsEnAvant()
        {
            try
            {
                var produits = await _adminService.GetProduitsEnAvantAsync();
                return Ok(produits);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // 6. Statistiques
        [HttpGet("statistiques")]
        public async Task<IActionResult> ObtenirStatistiques()
        {
            var stats = await _adminService.ObtenirStatistiquesGeneralesAsync();
            return Ok(stats);
        }

        [HttpGet("utilisateurs/statistiques")]
        public async Task<IActionResult> ObtenirStatistiquesUtilisateurs()
        {
            try
            {
                var stats = await _adminService.ObtenirStatistiquesGeneralesAsync();

                // Adapter les statistiques pour le format attendu par le frontend
                var userStats = new
                {
                    totalUsers = stats.NombreUtilisateurs,
                    clientCount = stats.NombreClients,
                    supplierCount = stats.NombreFournisseurs,
                    suspendedCount = 0 // À implémenter si nécessaire
                };

                return Ok(userStats);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminController: Erreur statistiques utilisateurs: {ex.Message}");
                return StatusCode(500, new { error = ex.Message });
            }
        }
    }
}