import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface AvisModerationDto {
  id: number;
  note: number;
  commentaire?: string;
  datePublication: Date;
  statut: StatutAvis;
  statutLibelle: string;
  dateModeration?: Date;
  commentaireModeration?: string;
  nomModerateur?: string;
  
  // Informations client
  clientId: number;
  clientNom: string;
  clientPrenom: string;
  clientEmail: string;
  
  // Informations produit
  produitId: number;
  produitNom: string;
  produitReference: string;
  
  // Informations fournisseur
  fournisseurId: number;
  fournisseurNom: string;
  fournisseurRaisonSociale: string;
}

export interface ModererAvisDto {
  statut: StatutAvis;
  commentaireModeration?: string;
}

export interface AvisFilterDto {
  statut?: StatutAvis;
  fournisseurId?: number;
  produitId?: number;
  dateDebut?: Date;
  dateFin?: Date;
  recherche?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDesc?: boolean;
}

export interface AvisStatsDto {
  totalAvis: number;
  avisPublies: number;
  avisCommentaireSupprime: number;
  avisSignales: number;
  noteMoyenneGlobale: number;
  avisParNote: { [key: number]: number };
  avisRecents: AvisModerationDto[];
}

export enum StatutAvis {
  Publie = 1,
  CommentaireSupprime = 2,
  Signale = 3
}

@Injectable({
  providedIn: 'root'
})
export class AvisModerationService {
  private apiUrl = `${environment.apiUrl}/admin/avis-moderation`;

  constructor(private http: HttpClient) { }

  // Méthodes pour l'admin
  getAvisForModeration(filter: AvisFilterDto = {}): Observable<AvisModerationDto[]> {
    let params = new HttpParams();
    
    if (filter.statut !== undefined) params = params.set('statut', filter.statut.toString());
    if (filter.fournisseurId) params = params.set('fournisseurId', filter.fournisseurId.toString());
    if (filter.produitId) params = params.set('produitId', filter.produitId.toString());
    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.recherche) params = params.set('recherche', filter.recherche);
    if (filter.page) params = params.set('page', filter.page.toString());
    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);
    if (filter.sortDesc !== undefined) params = params.set('sortDesc', filter.sortDesc.toString());

    return this.http.get<AvisModerationDto[]>(this.apiUrl, { params });
  }

  getAvisModeration(id: number): Observable<AvisModerationDto> {
    return this.http.get<AvisModerationDto>(`${this.apiUrl}/${id}`);
  }

  modererAvis(id: number, dto: ModererAvisDto): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/moderer`, dto);
  }

  getAvisStats(): Observable<AvisStatsDto> {
    return this.http.get<AvisStatsDto>(`${this.apiUrl}/statistiques`);
  }

  // Méthodes pour les fournisseurs
  getAvisFournisseur(filter: AvisFilterDto = {}): Observable<AvisModerationDto[]> {
    // Pour le développement, on utilise les vraies API mais on gère les erreurs 404
    let params = new HttpParams();

    if (filter.statut !== undefined) params = params.set('statut', filter.statut.toString());
    if (filter.produitId) params = params.set('produitId', filter.produitId.toString());
    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.recherche) params = params.set('recherche', filter.recherche);
    if (filter.page) params = params.set('page', filter.page.toString());
    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);
    if (filter.sortDesc !== undefined) params = params.set('sortDesc', filter.sortDesc.toString());

    return this.http.get<AvisModerationDto[]>(`${this.apiUrl}/fournisseur`, { params }).pipe(
      // En cas d'erreur 404, on retourne des données de test
      catchError(error => {
        if (error.status === 404) {
          console.log('🔧 API non disponible, utilisation de données de test pour les avis fournisseur');
          return this.getMockAvisFournisseur();
        }
        throw error;
      })
    );
  }

  private getMockAvisFournisseur(): Observable<AvisModerationDto[]> {
    const avisMockData = [
      {
        id: 1,
        note: 5,
        commentaire: 'Excellent produit, très satisfait de mon achat. La qualité est au rendez-vous et la livraison était rapide.',
        datePublication: new Date('2024-01-15T10:30:00'),
        statut: StatutAvis.Publie,
        statutLibelle: 'Publié',
        clientId: 1,
        clientNom: 'Martin',
        clientPrenom: 'Jean',
        clientEmail: '<EMAIL>',
        produitId: 1,
        produitNom: 'Smartphone Galaxy S24',
        produitReference: 'SGS24-001',
        fournisseurId: 1,
        fournisseurNom: 'TechStore',
        fournisseurRaisonSociale: 'TechStore SARL'
      },
      {
        id: 2,
        note: 4,
        commentaire: 'Très bon casque avec une excellente réduction de bruit. Le son est clair et les basses sont bien présentes.',
        datePublication: new Date('2024-01-14T14:20:00'),
        statut: StatutAvis.Publie,
        statutLibelle: 'Publié',
        dateModeration: new Date('2024-01-14T16:00:00'),
        commentaireModeration: 'Merci pour votre retour positif ! Nous sommes ravis que le produit vous satisfasse.',
        nomModerateur: 'Fournisseur',
        clientId: 2,
        clientNom: 'Dubois',
        clientPrenom: 'Marie',
        clientEmail: '<EMAIL>',
        produitId: 2,
        produitNom: 'Casque Audio Sony WH-1000XM5',
        produitReference: 'SONY-WH1000XM5',
        fournisseurId: 1,
        fournisseurNom: 'TechStore',
        fournisseurRaisonSociale: 'TechStore SARL'
      },
      {
        id: 3,
        note: 3,
        commentaire: 'Produit correct mais j\'attendais mieux pour ce prix. La batterie ne tient pas assez longtemps.',
        datePublication: new Date('2024-01-13T09:15:00'),
        statut: StatutAvis.Publie,
        statutLibelle: 'Publié',
        clientId: 3,
        clientNom: 'Leroy',
        clientPrenom: 'Pierre',
        clientEmail: '<EMAIL>',
        produitId: 3,
        produitNom: 'Ordinateur Portable Dell XPS 13',
        produitReference: 'DELL-XPS13',
        fournisseurId: 1,
        fournisseurNom: 'TechStore',
        fournisseurRaisonSociale: 'TechStore SARL'
      },
      {
        id: 4,
        note: 2,
        commentaire: 'Contenu inapproprié et avis manifestement faux.',
        datePublication: new Date('2024-01-12T16:45:00'),
        statut: StatutAvis.Signale,
        statutLibelle: 'Signalé',
        clientId: 4,
        clientNom: 'Bernard',
        clientPrenom: 'Sophie',
        clientEmail: '<EMAIL>',
        produitId: 4,
        produitNom: 'Tablette iPad Air',
        produitReference: 'IPAD-AIR',
        fournisseurId: 1,
        fournisseurNom: 'TechStore',
        fournisseurRaisonSociale: 'TechStore SARL'
      },
      {
        id: 5,
        note: 5,
        commentaire: '',
        datePublication: new Date('2024-01-11T11:30:00'),
        statut: StatutAvis.CommentaireSupprime,
        statutLibelle: 'Commentaire supprimé',
        dateModeration: new Date('2024-01-11T12:00:00'),
        nomModerateur: 'Admin',
        clientId: 5,
        clientNom: 'Moreau',
        clientPrenom: 'Luc',
        clientEmail: '<EMAIL>',
        produitId: 5,
        produitNom: 'Montre Apple Watch Series 9',
        produitReference: 'APPLE-WATCH-S9',
        fournisseurId: 1,
        fournisseurNom: 'TechStore',
        fournisseurRaisonSociale: 'TechStore SARL'
      }
    ] as AvisModerationDto[];

    return new Observable(observer => {
      setTimeout(() => {
        observer.next(avisMockData);
        observer.complete();
      }, 500);
    });
  }

  repondreAvis(id: number, reponse: string): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/repondre`, reponse);
  }

  getAvisStatsFournisseur(): Observable<AvisStatsDto> {
    return this.http.get<AvisStatsDto>(`${this.apiUrl}/statistiques/fournisseur`).pipe(
      catchError(error => {
        if (error.status === 404) {
          console.log('🔧 API non disponible, utilisation de données de test pour les statistiques fournisseur');
          return this.getMockStatsFournisseur();
        }
        throw error;
      })
    );
  }

  private getMockStatsFournisseur(): Observable<AvisStatsDto> {
    const statsMockData: AvisStatsDto = {
      totalAvis: 5,
      avisPublies: 3,
      avisCommentaireSupprime: 1,
      avisSignales: 1,
      noteMoyenneGlobale: 3.8,
      avisParNote: {
        1: 0,
        2: 1,
        3: 1,
        4: 1,
        5: 2
      },
      avisRecents: []
    };

    return new Observable(observer => {
      setTimeout(() => {
        observer.next(statsMockData);
        observer.complete();
      }, 300);
    });
  }

  // Méthode pour signaler un avis (fournisseur)
  signalerAvis(id: number, raisonSignalement: string): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/signaler`, {
      raisonSignalement
    }).pipe(
      catchError(error => {
        if (error.status === 404) {
          console.log('🔧 API non disponible, simulation du signalement');
          return this.getMockSignalement(id, raisonSignalement);
        }
        throw error;
      })
    );
  }

  private getMockSignalement(id: number, raisonSignalement: string): Observable<AvisModerationDto> {
    return new Observable(observer => {
      setTimeout(() => {
        const avisUpdated = {
          id: id,
          note: 3,
          commentaire: 'Avis signalé par le fournisseur',
          datePublication: new Date(),
          statut: StatutAvis.Signale,
          statutLibelle: 'Signalé',
          dateModeration: new Date(),
          nomModerateur: 'Fournisseur',
          clientId: 999,
          clientNom: 'Client',
          clientPrenom: 'Test',
          clientEmail: '<EMAIL>',
          produitId: 999,
          produitNom: 'Produit Test',
          produitReference: 'TEST-001',
          fournisseurId: 1,
          fournisseurNom: 'TechStore',
          fournisseurRaisonSociale: 'TechStore SARL'
        } as AvisModerationDto;

        console.log(`🚩 Avis #${id} signalé avec succès. Raison: ${raisonSignalement}`);
        console.log('📧 Notification envoyée à l\'administrateur: "Le fournisseur a signalé un avis"');

        observer.next(avisUpdated);
        observer.complete();
      }, 800);
    });
  }

  // Nouvelles méthodes pour la suppression et restauration de commentaires
  supprimerCommentaire(id: number, raisonSuppression: string): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/supprimer-commentaire`, {
      raisonSuppression
    });
  }

  restaurerCommentaire(id: number): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/restaurer-commentaire`, {});
  }

  // Méthode pour supprimer définitivement un avis (pour les données de test)
  supprimerAvis(id: number): Observable<void> {
    console.log(`🌐 Appel DELETE vers: ${this.apiUrl}/${id}`);
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  // Méthode pour tester la connectivité avec le backend
  testerConnectivite(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/test`);
  }

  // Méthodes utilitaires
  getStatutLibelle(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie: return 'Publié';
      case StatutAvis.CommentaireSupprime: return 'Commentaire supprimé';
      case StatutAvis.Signale: return 'Signalé';
      default: return 'Non défini';
    }
  }

  getStatutColor(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie: return 'success';
      case StatutAvis.CommentaireSupprime: return 'warning';
      case StatutAvis.Signale: return 'danger';
      default: return 'secondary';
    }
  }

  getStatutIcon(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie: return 'check-circle';
      case StatutAvis.CommentaireSupprime: return 'exclamation-triangle';
      case StatutAvis.Signale: return 'flag';
      default: return 'question-circle';
    }
  }


}
