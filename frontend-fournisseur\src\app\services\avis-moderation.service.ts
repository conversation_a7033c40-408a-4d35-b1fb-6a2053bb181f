import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface AvisModerationDto {
  id: number;
  note: number;
  commentaire?: string;
  datePublication: Date;
  statut: StatutAvis;
  statutLibelle: string;
  commentaireSupprime: boolean;
  dateModeration?: Date;
  commentaireModeration?: string;
  nomModerateur?: string;
  
  // Informations client
  clientId: number;
  clientNom: string;
  clientPrenom: string;
  clientEmail: string;
  
  // Informations produit
  produitId: number;
  produitNom: string;
  produitReference: string;
  
  // Informations fournisseur
  fournisseurId: number;
  fournisseurNom: string;
  fournisseurRaisonSociale: string;
}

export interface ModererAvisDto {
  statut: StatutAvis;
  commentaireModeration?: string;
}

export interface AvisFilterDto {
  statut?: StatutAvis;
  fournisseurId?: number;
  produitId?: number;
  dateDebut?: Date;
  dateFin?: Date;
  recherche?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortDesc?: boolean;
}

export interface AvisStatsDto {
  totalAvis: number;
  avisPublies: number;
  avisCommentaireSupprime: number;
  avisSignales: number;
  noteMoyenneGlobale: number;
  avisParNote: { [key: number]: number };
  avisRecents: AvisModerationDto[];
}

export enum StatutAvis {
  Publie = 1,
  CommentaireSupprime = 2,
  Signale = 3
}

@Injectable({
  providedIn: 'root'
})
export class AvisModerationService {
  private apiUrl = `${environment.apiUrl}/admin/avis-moderation`;

  constructor(private http: HttpClient) { }

  // Méthodes pour l'admin
  getAvisForModeration(filter: AvisFilterDto = {}): Observable<AvisModerationDto[]> {
    let params = new HttpParams();
    
    if (filter.statut !== undefined) params = params.set('statut', filter.statut.toString());
    if (filter.fournisseurId) params = params.set('fournisseurId', filter.fournisseurId.toString());
    if (filter.produitId) params = params.set('produitId', filter.produitId.toString());
    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.recherche) params = params.set('recherche', filter.recherche);
    if (filter.page) params = params.set('page', filter.page.toString());
    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);
    if (filter.sortDesc !== undefined) params = params.set('sortDesc', filter.sortDesc.toString());

    return this.http.get<AvisModerationDto[]>(this.apiUrl, { params });
  }

  getAvisModeration(id: number): Observable<AvisModerationDto> {
    return this.http.get<AvisModerationDto>(`${this.apiUrl}/${id}`);
  }

  modererAvis(id: number, dto: ModererAvisDto): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/moderer`, dto);
  }

  getAvisStats(): Observable<AvisStatsDto> {
    return this.http.get<AvisStatsDto>(`${this.apiUrl}/statistiques`);
  }

  // Méthodes pour les fournisseurs
  getAvisFournisseur(filter: AvisFilterDto = {}): Observable<AvisModerationDto[]> {
    let params = new HttpParams();

    if (filter.statut !== undefined) params = params.set('statut', filter.statut.toString());
    if (filter.produitId) params = params.set('produitId', filter.produitId.toString());
    if (filter.dateDebut) params = params.set('dateDebut', filter.dateDebut.toISOString());
    if (filter.dateFin) params = params.set('dateFin', filter.dateFin.toISOString());
    if (filter.recherche) params = params.set('recherche', filter.recherche);
    if (filter.page) params = params.set('page', filter.page.toString());
    if (filter.pageSize) params = params.set('pageSize', filter.pageSize.toString());
    if (filter.sortBy) params = params.set('sortBy', filter.sortBy);
    if (filter.sortDesc !== undefined) params = params.set('sortDesc', filter.sortDesc.toString());

    // Utiliser l'endpoint correct pour les fournisseurs
    return this.http.get<AvisModerationDto[]>(`${environment.apiUrl}/AvisModeration/fournisseur`, { params });
  }



  repondreAvis(id: number, reponse: string): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/repondre`, reponse);
  }

  getAvisStatsFournisseur(): Observable<AvisStatsDto> {
    // Utiliser l'endpoint correct pour les statistiques fournisseur
    return this.http.get<AvisStatsDto>(`${environment.apiUrl}/AvisModeration/statistiques/fournisseur`);
  }



  // Méthode pour signaler un avis (fournisseur)
  signalerAvis(id: number, raisonSignalement: string): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/signaler`, {
      raisonSignalement
    });
  }



  // Nouvelles méthodes pour la suppression et restauration de commentaires
  supprimerCommentaire(id: number, raisonSuppression: string): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/supprimer-commentaire`, {
      raisonSuppression
    });
  }

  restaurerCommentaire(id: number): Observable<AvisModerationDto> {
    return this.http.put<AvisModerationDto>(`${this.apiUrl}/${id}/restaurer-commentaire`, {});
  }

  // Méthode pour supprimer définitivement un avis (pour les données de test)
  supprimerAvis(id: number): Observable<void> {
    console.log(`🌐 Appel DELETE vers: ${this.apiUrl}/${id}`);
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }

  // Méthode pour tester la connectivité avec le backend
  testerConnectivite(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/test`);
  }

  // Méthodes utilitaires
  getStatutLibelle(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie: return 'Publié';
      case StatutAvis.CommentaireSupprime: return 'Commentaire supprimé';
      case StatutAvis.Signale: return 'Signalé';
      default: return 'Non défini';
    }
  }

  getStatutColor(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie: return 'success';
      case StatutAvis.CommentaireSupprime: return 'warning';
      case StatutAvis.Signale: return 'danger';
      default: return 'secondary';
    }
  }

  getStatutIcon(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie: return 'check-circle';
      case StatutAvis.CommentaireSupprime: return 'exclamation-triangle';
      case StatutAvis.Signale: return 'flag';
      default: return 'question-circle';
    }
  }


}
