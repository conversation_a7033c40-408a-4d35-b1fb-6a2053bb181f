﻿using AutoMapper;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum; 

namespace WebApiPfe.Services.Profile
{
    public class UtilisateurProfile : AutoMapper.Profile
    {
        public UtilisateurProfile()
        {
            ConfigureCreateMappings();
            ConfigureReadMappings();
        }

        private void ConfigureCreateMappings()
        {
            CreateMap<UtilisateurCreateDto, Utilisateur>()
                .Include<UtilisateurCreateDto, Client>()
                .Include<UtilisateurCreateDto, Fournisseur>()
                .ForMember(dest => dest.RoleDiscriminator, opt =>
                {
                    opt.MapFrom((src, dest) =>
                    src is FournisseurCreateDto ? RoleUtilisateur.Fournisseur :
                    src is ClientCreateDto ? RoleUtilisateur.Client :
                    RoleUtilisateur.Client);
                });

            CreateMap<UtilisateurCreateDto, Client>()
                .ForMember(dest => dest.Panier, opt => opt.Ignore());

            CreateMap<FournisseurCreateDto, Fournisseur>() 
                .ForMember(dest => dest.MatriculeFiscale, opt => opt.MapFrom(src => src.MatriculeFiscale))
                .ForMember(dest => dest.RIB, opt => opt.MapFrom(src => src.RIB));
        }

        private void ConfigureReadMappings()
        {
            CreateMap<Utilisateur, UtilisateurReadDto>()
                .ForMember(dest => dest.Nom, opt => opt.MapFrom(src => src.Nom))
                .ForMember(dest => dest.Prenom, opt => opt.MapFrom(src =>  src.Prenom))
                .ForMember(dest => dest.Role, opt => opt.MapFrom(src => src.GetRoleSpecifique()));

            CreateMap<Client, ClientDto>()
                .IncludeBase<Utilisateur, UtilisateurReadDto>()
                .ForMember(dest => dest.NombreCommandes,
                          opt => opt.MapFrom(src => src.Commandes != null ? src.Commandes.Count : 0));

            CreateMap<Fournisseur, FournisseurDto>()
                .IncludeBase<Utilisateur, UtilisateurReadDto>()
                .ForMember(dest => dest.RIBMasque, opt => opt.MapFrom(src => src.RIBMasque))
                .ForMember(dest => dest.LogoFile, opt => opt.MapFrom(src => src.LogoFile));
        }
    }
}
