import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { AdminAuthService } from '../services/admin-auth.service';
import { AdminRole, PermissionAction } from '../models/admin.model';

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate {

  constructor(
    private adminAuthService: AdminAuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.adminAuthService.currentUser$.pipe(
      take(1),
      map(user => {
        if (!user) {
          this.router.navigate(['/admin/login']);
          return false;
        }

        // Vérifier les rôles requis
        const requiredRoles = route.data['roles'] as AdminRole[];
        if (requiredRoles && requiredRoles.length > 0) {
          const hasRole = requiredRoles.includes(user.role);
          if (!hasRole) {
            this.handleAccessDenied('Rôle insuffisant pour accéder à cette page');
            return false;
          }
        }

        // Vérifier les permissions requises
        const requiredPermissions = route.data['permissions'] as { resource: string, action: PermissionAction }[];
        if (requiredPermissions && requiredPermissions.length > 0) {
          const hasAllPermissions = requiredPermissions.every(perm =>
            this.adminAuthService.hasPermission(perm.resource, perm.action)
          );
          if (!hasAllPermissions) {
            this.handleAccessDenied('Permissions insuffisantes pour accéder à cette page');
            return false;
          }
        }

        return true;
      })
    );
  }

  private handleAccessDenied(message: string): void {
    this.router.navigate(['/admin/dashboard'], {
      queryParams: { 
        error: 'access_denied', 
        message: message 
      }
    });
  }
}
