﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Interfaces;
using WebApiPfe.Models.Entity;
namespace WebApiPfe.Services.Implementations
{
    public class CommandeFournisseurService : ICommandeFournisseurService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<CommandeFournisseurService> _logger;

        public CommandeFournisseurService(
            AppDbContext context,
            IMapper mapper,
            ILogger<CommandeFournisseurService> logger)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<CommandeFournisseurDto> GetByIdAsync(int id)
        {
            var commande = await _context.CommandesFournisseurs
                .Include(c => c.Fournis<PERSON>ur)
                .Include(c => c.LignesCommande)
                    .ThenInclude(l => l.Produit)
                .FirstOrDefaultAsync(c => c.Id == id);

            if (commande == null)
                throw new KeyNotFoundException($"Commande fournisseur {id} introuvable");

            var dto = _mapper.Map<CommandeFournisseurDto>(commande);
            return dto;
        }

        public async Task<IEnumerable<CommandeFournisseurDto>> GetAllAsync()
        {
            var commandes = await _context.CommandesFournisseurs
                .Include(c => c.Fournisseur)
                .Include(c => c.LignesCommande)
                    .ThenInclude(l => l.Produit)
                .OrderByDescending(c => c.DateCreation)
                .ToListAsync();

            var dtos = _mapper.Map<IEnumerable<CommandeFournisseurDto>>(commandes);
            return dtos;
        }

        public async Task<CommandeFournisseurDto> CreateAsync(CreateCommandeFournisseurDto dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var commande = new CommandeFournisseur
                {
                    FournisseurId = dto.FournisseurId,
                    FraisLivraison = dto.FraisLivraison,
                    DateCreation = DateTime.UtcNow,
                    Statut = StatutCommandeFournisseur.EnAttente,
                    Reference = $"CMD-F{dto.FournisseurId}-{DateTime.Now:yyyyMMdd-HHmmss}"
                };

                await _context.CommandesFournisseurs.AddAsync(commande);
                await _context.SaveChangesAsync();

                foreach (var ligneDto in dto.Lignes)
                {
                    var ligne = new LigneCommandeFournisseur
                    {
                        CommandeId = commande.Id,
                        ProduitId = ligneDto.ProduitId,
                        Quantite = ligneDto.Quantite,
                        PrixUnitaire = ligneDto.PrixUnitaire
                    };
                    await _context.LignesCommandeFournisseur.AddAsync(ligne);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return await GetByIdAsync(commande.Id);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Erreur lors de la création de la commande fournisseur");
                throw;
            }
        }



        public async Task<IEnumerable<CommandeFournisseurDto>> GetByFournisseurAsync(int fournisseurId)
        {
            var commandes = await _context.CommandesFournisseurs
                .Include(c => c.Fournisseur)
                .Include(c => c.LignesCommande)
                    .ThenInclude(l => l.Produit)
                        .ThenInclude(p => p.Images)
                .Where(c => c.FournisseurId == fournisseurId)
                .OrderByDescending(c => c.DateCreation)
                .ToListAsync();

            // Debug logs
            _logger.LogInformation($"🔍 Trouvé {commandes.Count} commandes pour le fournisseur {fournisseurId}");
            foreach (var commande in commandes)
            {
                _logger.LogInformation($"📦 Commande {commande.Id}: Fournisseur={commande.Fournisseur?.RaisonSociale ?? "NULL"}, Lignes={commande.LignesCommande?.Count ?? 0}");
                if (commande.LignesCommande != null)
                {
                    foreach (var ligne in commande.LignesCommande)
                    {
                        _logger.LogInformation($"  📋 Ligne {ligne.Id}: Produit={ligne.Produit?.Nom ?? "NULL"}, Images={ligne.Produit?.Images?.Count ?? 0}");
                    }
                }
            }

            var dtos = _mapper.Map<IEnumerable<CommandeFournisseurDto>>(commandes);

            // Debug logs après mapping
            _logger.LogInformation($"🔄 Après mapping AutoMapper:");
            foreach (var dto in dtos)
            {
                _logger.LogInformation($"  📦 DTO Commande {dto.Id}: NomFournisseur='{dto.NomFournisseur}', MatriculeFiscale='{dto.MatriculeFiscale}'");
                foreach (var ligne in dto.LignesCommande)
                {
                    _logger.LogInformation($"    📋 DTO Ligne {ligne.Id}: NomProduit='{ligne.NomProduit}', ReferenceProduit='{ligne.ReferenceProduit}', TotalLigne={ligne.TotalLigne}, ImagePrincipale='{ligne.ImagePrincipale}'");
                }
            }

            return dtos;
        }

        public async Task DeleteAsync(int id)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var commande = await _context.CommandesFournisseurs
                    .Include(c => c.LignesCommande)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (commande == null)
                    throw new KeyNotFoundException($"Commande fournisseur {id} introuvable");

                if (!commande.PeutModifier())
                    throw new InvalidOperationException("Impossible de supprimer une commande non brouillon");

                _context.LignesCommandeFournisseur.RemoveRange(commande.LignesCommande);
                _context.CommandesFournisseurs.Remove(commande);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Erreur lors de la suppression de la commande fournisseur {id}");
                throw;
            }
        }
        public async Task<CommandeFournisseurDto> UpdateStatutAsync(int id, UpdateStatutCommandeFournisseurDto dto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();

            try
            {
                var commande = await _context.CommandesFournisseurs
                    .Include(c => c.Fournisseur)
                    .FirstOrDefaultAsync(c => c.Id == id);

                if (commande == null)
                    throw new KeyNotFoundException($"Commande fournisseur {id} introuvable");

                // Validation des transitions de statut
                if (commande.Statut == StatutCommandeFournisseur.Livree && dto.Statut != StatutCommandeFournisseur.Livree)
                    throw new InvalidOperationException("Impossible de modifier le statut d'une commande livrée");

                commande.Statut = dto.Statut;

                if (dto.Statut == StatutCommandeFournisseur.Livree)
                {
                    commande.DateLivraison = DateTime.UtcNow;
                    commande.NumeroBonLivraison = dto.NumeroBonLivraison ?? $"BL-{DateTime.Now:yyyyMMdd-HHmmss}";
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return await GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"Erreur lors de la mise à jour du statut de la commande {id}");
                throw;
            }
        }

    }
}
