[{"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "AjouterCategorie", "RelativePath": "api/Admin/categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.Admin.CategorieGestionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "ModifierCategorie", "RelativePath": "api/Admin/categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.Admin.CategorieGestionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "SupprimerCategorie", "RelativePath": "api/Admin/categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "RefuserCategorie", "RelativePath": "api/Admin/categories/{id}/refuser", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "ValiderCategorie", "RelativePath": "api/Admin/categories/{id}/valider", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "GetCommandes", "RelativePath": "api/Admin/commandes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "AnnulerCommande", "RelativePath": "api/Admin/commandes/{id}/annuler", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "GetProduits", "RelativePath": "api/Admin/produits", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "statut", "Type": "System.String", "IsRequired": false}, {"Name": "stockCritique", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "SupprimerProduit", "RelativePath": "api/Admin/produits/{produitId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "MettreEnAvantProduit", "RelativePath": "api/Admin/produits/{produitId}/enavant", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "ModererContenuProduit", "RelativePath": "api/Admin/produits/{produitId}/moderer", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.Admin.ProduitModerationDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "RefuserProduit", "RelativePath": "api/Admin/produits/{produitId}/refuser", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}, {"Name": "raison", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "RetirerMiseEnAvantProduit", "RelativePath": "api/Admin/produits/{produitId}/retirer-avant", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "UpdateStockProduit", "RelativePath": "api/Admin/produits/{produitId}/stock", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}, {"Name": "nouveauStock", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "ValiderProduit", "RelativePath": "api/Admin/produits/{produitId}/valider", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "GetProduitsEnAvant", "RelativePath": "api/Admin/produits/en-avant", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "GetProduitsStockCritique", "RelativePath": "api/Admin/produits/stock-critique", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "<PERSON>uil", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "AjouterSousCategorie", "RelativePath": "api/Admin/souscategories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.Admin.SousCategorieGestionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "ModifierSousCategorie", "RelativePath": "api/Admin/souscategories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.Admin.SousCategorieGestionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "SupprimerSousCategorie", "RelativePath": "api/Admin/souscategories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "RefuserSousCategorie", "RelativePath": "api/Admin/souscategories/{id}/refuser", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "ValiderSousCategorie", "RelativePath": "api/Admin/souscategories/{id}/valider", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "ObtenirStatistiques", "RelativePath": "api/Admin/statistiques", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "GetUtilisateurs", "RelativePath": "api/Admin/utilisateurs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "role", "Type": "System.String", "IsRequired": false}, {"Name": "estActif", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "SupprimerUtilisateur", "RelativePath": "api/Admin/utilisateurs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdminController", "Method": "ToggleActivationUtilisateur", "RelativePath": "api/Admin/utilisateurs/{id}/toggle", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdressesController", "Method": "Post", "RelativePath": "api/Adresses", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.AdresseCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.AdresseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AdressesController", "Method": "Get", "RelativePath": "api/Adresses/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.AdresseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AdressesController", "Method": "Put", "RelativePath": "api/Adresses/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.AdresseUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdressesController", "Method": "Delete", "RelativePath": "api/Adresses/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdressesController", "Method": "SetPrincipale", "RelativePath": "api/Adresses/{id}/principale/{entityId}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "entityId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AdressesController", "Method": "GetByEntity", "RelativePath": "api/Adresses/entity/{entityId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "entityId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.AdresseDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AuthController", "Method": "InitializeDefaultAdmin", "RelativePath": "api/Auth/init-admin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.AuthDTO.LoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AuthController", "Method": "RegisterAdmin", "RelativePath": "api/Auth/register/admin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.AdminCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AuthController", "Method": "RegisterClient", "RelativePath": "api/Auth/register/client", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.ClientCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AuthController", "Method": "RegisterFournisseur", "RelativePath": "api/Auth/register/fournisseur", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "MatriculeFiscale", "Type": "System.String", "IsRequired": false}, {"Name": "RaisonSociale", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "RIB", "Type": "System.String", "IsRequired": false}, {"Name": "CodeBanque", "Type": "System.String", "IsRequired": false}, {"Name": "Commission", "Type": "System.Decimal", "IsRequired": false}, {"Name": "DelaiPreparationJours", "Type": "System.Int32", "IsRequired": false}, {"Name": "FraisLivraisonBase", "Type": "System.Decimal", "IsRequired": false}, {"Name": "LogoFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Rue", "Type": "System.String", "IsRequired": false}, {"Name": "Ville", "Type": "System.String", "IsRequired": false}, {"Name": "CodePostal", "Type": "System.String", "IsRequired": false}, {"Name": "Pays", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Nom", "Type": "System.String", "IsRequired": false}, {"Name": "Prenom", "Type": "System.String", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "DateNaissance", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "EstActif", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.AvisController", "Method": "<PERSON><PERSON>ter<PERSON>vis", "RelativePath": "api/Avis/{produitId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}, {"Name": "avisDto", "Type": "WebApiPfe.DTOs.CreateDTOs.AvisCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.AvisDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisController", "Method": "GetAvis", "RelativePath": "api/Avis/{produitId}/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}, {"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.AvisDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisController", "Method": "GetMonAvis", "RelativePath": "api/Avis/{produitId}/mon-avis", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.AvisDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Avis/{produitId}/moyenne", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Double", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisController", "Method": "GetAvisByProduit", "RelativePath": "api/Avis/produit/{produitId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.ReadDTOs.AvisDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisModerationController", "Method": "GetAvisForModeration", "RelativePath": "api/AvisModeration", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Statut", "Type": "System.String", "IsRequired": false}, {"Name": "FournisseurId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProduitId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DateDebut", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DateFin", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Recherche", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDesc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.Admin.AvisModerationDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisModerationController", "Method": "GetAvisModeration", "RelativePath": "api/AvisModeration/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.AvisModerationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisModerationController", "Method": "<PERSON>rer<PERSON>vis", "RelativePath": "api/AvisModeration/{id}/moderer", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.Admin.ModererAvisDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.AvisModerationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisModerationController", "Method": "<PERSON><PERSON><PERSON><PERSON>vis", "RelativePath": "api/AvisModeration/{id}/repondre", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "reponse", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.AvisModerationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisModerationController", "Method": "GetAvisFournisseur", "RelativePath": "api/AvisModeration/fournisseur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Statut", "Type": "System.String", "IsRequired": false}, {"Name": "FournisseurId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProduitId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DateDebut", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DateFin", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Recherche", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDesc", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.Admin.AvisModerationDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisModerationController", "Method": "GetAvisStats", "RelativePath": "api/AvisModeration/statistiques", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.AvisStatsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.AvisModerationController", "Method": "GetAvisStatsFournisseur", "RelativePath": "api/AvisModeration/statistiques/fournisseur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.AvisStatsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "GetAll", "RelativePath": "api/Categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.Categorie, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "Create", "RelativePath": "api/Categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateCategorieDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.CategorieDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "GetById", "RelativePath": "api/Categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.CategorieDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "Update", "RelativePath": "api/Categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.UpdateCategorieDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "Delete", "RelativePath": "api/Categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "GetProduitsCount", "RelativePath": "api/Categories/{id}/produits-count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "GetSousCategories", "RelativePath": "api/Categories/{id}/sous-categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.SousCategorie, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "ToggleVisibility", "RelativePath": "api/Categories/{id}/toggle-visibility", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "GetAllForAdmin", "RelativePath": "api/Categories/admin", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.Admin.CategorieAdminDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "GetForDropdown", "RelativePath": "api/Categories/dropdown", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "GetAllEnriched", "RelativePath": "api/Categories/enriched", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.CategorieDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CategoriesController", "Method": "GetAllForFournisseur", "RelativePath": "api/Categories/fournisseur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.CategorieDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ClientsController", "Method": "GetAll", "RelativePath": "api/Clients", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ClientDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ClientsController", "Method": "Create", "RelativePath": "api/Clients", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Nom", "Type": "System.String", "IsRequired": false}, {"Name": "Prenom", "Type": "System.String", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "DateNaissance", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "EstActif", "Type": "System.Boolean", "IsRequired": false}, {"Name": "adress<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "adresseVille", "Type": "System.String", "IsRequired": false}, {"Name": "adresseCodePostal", "Type": "System.String", "IsRequired": false}, {"Name": "adresse<PERSON>ays", "Type": "System.String", "IsRequired": false}, {"Name": "adresseEstPrincipale", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ClientDto", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ClientsController", "Method": "GetById", "RelativePath": "api/Clients/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ClientDto", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ClientsController", "Method": "Update", "RelativePath": "api/Clients/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.ClientUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ClientsController", "Method": "Delete", "RelativePath": "api/Clients/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ClientsController", "Method": "UpdateAdresses", "RelativePath": "api/Clients/{id}/adresses", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "adresses", "Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.ReadDTOs.AdresseDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ClientsController", "Method": "GetCommandes", "RelativePath": "api/Clients/{id}/commandes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.CommandeDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ClientsController", "Method": "UpdateProfile", "RelativePath": "api/Clients/{id}/profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.ClientProfileUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CommandeFournisseurController", "Method": "GetAll", "RelativePath": "api/CommandeFournisseur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CommandeFournisseurController", "Method": "Create", "RelativePath": "api/CommandeFournisseur", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateCommandeFournisseurDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CommandeFournisseurController", "Method": "GetById", "RelativePath": "api/CommandeFournisseur/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CommandeFournisseurController", "Method": "UpdateStatut", "RelativePath": "api/CommandeFournisseur/{id}/statut", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.UpdateStatutCommandeFournisseurDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CommandeFournisseurController", "Method": "GetTestDirect", "RelativePath": "api/CommandeFournisseur/test-direct", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CommandesController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Commandes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateCommandeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.CommandeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CommandesController", "Method": "GetAllCommandes", "RelativePath": "api/Commandes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.CommandeDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CommandesController", "Method": "GetCommande", "RelativePath": "api/Commandes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.CommandeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CommandesController", "Method": "SupprimerCommande", "RelativePath": "api/Commandes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CommandesController", "Method": "GetCommandesFournisseurs", "RelativePath": "api/Commandes/{id}/commandes-fournisseurs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CommandesController", "Method": "Finaliser<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Commandes/{id}/finaliser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "tokenPaiement", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CommandesController", "Method": "ForcerDivision", "RelativePath": "api/Commandes/{id}/force-division", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.CommandesController", "Method": "GetCommandesByClient", "RelativePath": "api/Commandes/by-client/{clientId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.CommandeDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.CommandesController", "Method": "CalculerFraisLiv<PERSON>son", "RelativePath": "api/Commandes/calculer-frais-livraison", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WebApiPfe.Models.DTOs.CalculerFraisLivraisonRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.FraisLivraisonResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesController", "Method": "CreerDemandeCategorie", "RelativePath": "api/Demandes/categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateDemandeCategorieDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesController", "Method": "GetDemandesCategories", "RelativePath": "api/Demandes/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fournisseurId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesController", "Method": "GetDemandeCategorie", "RelativePath": "api/Demandes/categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesController", "Method": "TraiterDemandeCategorie", "RelativePath": "api/Demandes/categories/{id}/traiter", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.TraiterDemandeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesController", "Method": "CreerDemandeSousCategorie", "RelativePath": "api/Demandes/sous-categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateDemandeSousCategorieDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesController", "Method": "GetDemandesSousCategories", "RelativePath": "api/Demandes/sous-categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fournisseurId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesController", "Method": "GetDemandeSousCategorie", "RelativePath": "api/Demandes/sous-categories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesController", "Method": "TraiterDemandeSousCategorie", "RelativePath": "api/Demandes/sous-categories/{id}/traiter", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.TraiterDemandeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesController", "Method": "GetNombreDemandesEnAttente", "RelativePath": "api/Demandes/statistiques/en-attente", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesCategoriesController", "Method": "GetAll<PERSON>eman<PERSON>", "RelativePath": "api/DemandesCategories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.Admin.DemandeCategorieDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesCategoriesController", "Method": "CreateDemande", "RelativePath": "api/DemandesCategories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "WebApiPfe.DTOs.Admin.CreateDemandeCategorieDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.DemandeCategorieDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesCategoriesController", "Method": "GetDemandeById", "RelativePath": "api/DemandesCategories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.DemandeCategorieDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesCategoriesController", "Method": "DeleteDemande", "RelativePath": "api/DemandesCategories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesCategoriesController", "Method": "TraiterDemande", "RelativePath": "api/DemandesCategories/{id}/traiter", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "traiter<PERSON>to", "Type": "WebApiPfe.DTOs.Admin.TraiterDemandeCategorieDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.DemandeCategorieDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesCategoriesController", "Method": "GetMesDemandes", "RelativePath": "api/DemandesCategories/mes-demandes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.Admin.DemandeCategorieDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesCategoriesController", "Method": "GetDemandesByStatut", "RelativePath": "api/DemandesCategories/statut/{statut}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "statut", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.Admin.DemandeCategorieDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesCategoriesController", "Method": "TestEndpoint", "RelativePath": "api/DemandesCategories/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesSousCategoriesController", "Method": "GetAll<PERSON>eman<PERSON>", "RelativePath": "api/DemandesSousCategories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.Admin.DemandeSousCategorieDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesSousCategoriesController", "Method": "CreateDemande", "RelativePath": "api/DemandesSousCategories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createDto", "Type": "WebApiPfe.DTOs.Admin.CreateDemandeSousCategorieDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.DemandeSousCategorieDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesSousCategoriesController", "Method": "GetDemandeById", "RelativePath": "api/DemandesSousCategories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.DemandeSousCategorieDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesSousCategoriesController", "Method": "DeleteDemande", "RelativePath": "api/DemandesSousCategories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DemandesSousCategoriesController", "Method": "TraiterDemande", "RelativePath": "api/DemandesSousCategories/{id}/traiter", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "traiter<PERSON>to", "Type": "WebApiPfe.DTOs.Admin.TraiterDemandeSousCategorieDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.Admin.DemandeSousCategorieDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesSousCategoriesController", "Method": "GetMesDemandes", "RelativePath": "api/DemandesSousCategories/mes-demandes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.Admin.DemandeSousCategorieDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesSousCategoriesController", "Method": "GetDemandesByStatut", "RelativePath": "api/DemandesSousCategories/statut/{statut}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "statut", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.Admin.DemandeSousCategorieDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DemandesSousCategoriesController", "Method": "TestEndpoint", "RelativePath": "api/DemandesSousCategories/test", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DetailsCommandeController", "Method": "Post", "RelativePath": "api/DetailsCommande", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateDetailsCommandeDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DetailsCommandeController", "Method": "Get", "RelativePath": "api/DetailsCommande/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DetailsCommandeController", "Method": "Put", "RelativePath": "api/DetailsCommande/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.UpdateDetailsCommandeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DetailsCommandeController", "Method": "Delete", "RelativePath": "api/DetailsCommande/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DetailsCommandeController", "Method": "RecalculerPrix", "RelativePath": "api/DetailsCommande/{id}/recalculer", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DetailsCommandeController", "Method": "GetByCommande", "RelativePath": "api/DetailsCommande/commande/{commandeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.DiagnosticController", "Method": "GetCommandesFournisseurs", "RelativePath": "api/Diagnostic/commandes-fournisseurs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DiagnosticController", "Method": "FixProduitsFournisseurs", "RelativePath": "api/Diagnostic/fix-produits-fournisseurs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.DiagnosticController", "Method": "GetProduitsSansFournisseur", "RelativePath": "api/Diagnostic/produits-sans-fournisseur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.FavorisController", "Method": "Ajouter<PERSON>av<PERSON>", "RelativePath": "api/Favoris", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.FavoriCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}]}, {"ContainingType": "WebApiPfe.Controllers.FavorisController", "Method": "GetFavoriById", "RelativePath": "api/Favoris/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "WebApiPfe.Controllers.FavorisController", "Method": "<PERSON><PERSON><PERSON>er<PERSON>av<PERSON>", "RelativePath": "api/Favoris/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}]}, {"ContainingType": "WebApiPfe.Controllers.FavorisController", "Method": "ListerParClient", "RelativePath": "api/Favoris/client/{clientId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FavorisController", "Method": "VerifierFavoriExiste", "RelativePath": "api/Favoris/verifier", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": false}, {"Name": "produitId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FormesController", "Method": "GetAll", "RelativePath": "api/Formes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.Forme, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FormesController", "Method": "Create", "RelativePath": "api/Formes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateFormeDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.FormesController", "Method": "GetById", "RelativePath": "api/Formes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.Models.Entity.Forme", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FormesController", "Method": "Update", "RelativePath": "api/Formes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "forme", "Type": "WebApiPfe.Models.Entity.Forme", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.FormesController", "Method": "Delete", "RelativePath": "api/Formes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.FormesController", "Method": "GetByCategorie", "RelativePath": "api/Formes/by-categorie/{categorieId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categorieId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.Forme, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FormesController", "Method": "GetForDropdown", "RelativePath": "api/Formes/dropdown", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FormesController", "Method": "GetAllEnriched", "RelativePath": "api/Formes/enriched", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.FormeDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "GetAll", "RelativePath": "api/Fournis<PERSON>urs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "onlyActive", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.FournisseurDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "Create", "RelativePath": "api/Fournis<PERSON>urs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "MatriculeFiscale", "Type": "System.String", "IsRequired": false}, {"Name": "RaisonSociale", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "RIB", "Type": "System.String", "IsRequired": false}, {"Name": "CodeBanque", "Type": "System.String", "IsRequired": false}, {"Name": "Commission", "Type": "System.Decimal", "IsRequired": false}, {"Name": "DelaiPreparationJours", "Type": "System.Int32", "IsRequired": false}, {"Name": "FraisLivraisonBase", "Type": "System.Decimal", "IsRequired": false}, {"Name": "LogoFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Rue", "Type": "System.String", "IsRequired": false}, {"Name": "Ville", "Type": "System.String", "IsRequired": false}, {"Name": "CodePostal", "Type": "System.String", "IsRequired": false}, {"Name": "Pays", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}, {"Name": "Nom", "Type": "System.String", "IsRequired": false}, {"Name": "Prenom", "Type": "System.String", "IsRequired": false}, {"Name": "PhoneNumber", "Type": "System.String", "IsRequired": false}, {"Name": "DateNaissance", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Password", "Type": "System.String", "IsRequired": false}, {"Name": "EstActif", "Type": "System.Boolean", "IsRequired": false}, {"Name": "adress<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "adresseVille", "Type": "System.String", "IsRequired": false}, {"Name": "adresseCodePostal", "Type": "System.String", "IsRequired": false}, {"Name": "adresse<PERSON>ays", "Type": "System.String", "IsRequired": false}, {"Name": "adresseEstPrincipale", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.FournisseurDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "GetById", "RelativePath": "api/Fournisseurs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.FournisseurDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "Update", "RelativePath": "api/Fournisseurs/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.FournisseurUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "Delete", "RelativePath": "api/Fournisseurs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "AjouterAdresse", "RelativePath": "api/Fournisseurs/{id}/adresses", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.AdresseCreateDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.AdresseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "UpdateCommission", "RelativePath": "api/Fournisseurs/{id}/commission", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "commission", "Type": "System.Decimal", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "GetProduits", "RelativePath": "api/Fournisseurs/{id}/produits", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "ToggleStatus", "RelativePath": "api/Fournisseurs/{id}/toggle-status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "Exists", "RelativePath": "api/Fournisseurs/exists/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.FournisseursController", "Method": "ValiderRIB", "RelativePath": "api/Fournisseurs/valider-rib", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "rib", "Type": "System.String", "IsRequired": false}, {"Name": "codeBanque", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ItemPanierController", "Method": "AddItem", "RelativePath": "api/<PERSON><PERSON><PERSON><PERSON><PERSON>", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "panierId", "Type": "System.Int32", "IsRequired": false}, {"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.AddItemPanierDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ItemPanierDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ItemPanierController", "Method": "GetItem", "RelativePath": "api/ItemPanier/{itemId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "panierId", "Type": "System.Int32", "IsRequired": false}, {"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ItemPanierDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ItemPanierController", "Method": "UpdateItem", "RelativePath": "api/ItemPanier/{itemId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "panierId", "Type": "System.Int32", "IsRequired": false}, {"Name": "itemId", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.UpdateItemPanierDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ItemPanierController", "Method": "RemoveItem", "RelativePath": "api/ItemPanier/{itemId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "panierId", "Type": "System.Int32", "IsRequired": false}, {"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.LignesCommandeFournisseurController", "Method": "GetByCommandeId", "RelativePath": "api/LignesCommandeFournisseur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.LignesCommandeFournisseurController", "Method": "Create", "RelativePath": "api/LignesCommandeFournisseur", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": false}, {"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateLigneCommandeFournisseurDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.LignesCommandeFournisseurController", "Method": "GetById", "RelativePath": "api/LignesCommandeFournisseur/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": false}, {"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetLigneCommandeFournisseur"}, {"ContainingType": "WebApiPfe.Controllers.LignesCommandeFournisseurController", "Method": "Update", "RelativePath": "api/LignesCommandeFournisseur/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": false}, {"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.UpdateLigneCommandeFournisseurDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.LignesCommandeFournisseurController", "Method": "Delete", "RelativePath": "api/LignesCommandeFournisseur/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": false}, {"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.LivraisonController", "Method": "Create", "RelativePath": "api/<PERSON><PERSON>son", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateLivraisonDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.LivraisonDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.LivraisonController", "Method": "Get", "RelativePath": "api/Livraison/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.LivraisonDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.LivraisonController", "Method": "Update", "RelativePath": "api/Livraison/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.UpdateLivraisonDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.LivraisonController", "Method": "GenererEtiquette", "RelativePath": "api/Livraison/{livraisonId}/etiquette", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "livraisonId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.LivraisonController", "Method": "ChangeStatut", "RelativePath": "api/<PERSON>raison/{livraisonId}/statut", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "livraisonId", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.ChangeStatutLivraisonDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.LivraisonController", "Method": "GetStatutsDisponibles", "RelativePath": "api/Livraison/{livraisonId}/statuts-disponibles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "livraisonId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.ReadDTOs.StatutLivraisonDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.LivraisonController", "Method": "GetByCommande", "RelativePath": "api/Livraison/commande/{commandeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.ReadDTOs.LivraisonDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.MarquesController", "Method": "GetAll", "RelativePath": "api/Marques", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.Marque, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.MarquesController", "Method": "Create", "RelativePath": "api/Marques", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "marque", "Type": "WebApiPfe.Models.Entity.Marque", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.Models.Entity.Marque", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.MarquesController", "Method": "GetById", "RelativePath": "api/Marques/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.Models.Entity.Marque", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.MarquesController", "Method": "Update", "RelativePath": "api/Marques/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "marque", "Type": "WebApiPfe.Models.Entity.Marque", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.MarquesController", "Method": "Delete", "RelativePath": "api/Marques/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.MarquesController", "Method": "GetForDropdown", "RelativePath": "api/Marques/dropdown", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.MarquesController", "Method": "GetAllEnriched", "RelativePath": "api/Marques/enriched", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.MarqueDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.NotificationController", "Method": "Create", "RelativePath": "api/Notification", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateNotificationDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.NotificationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.NotificationController", "Method": "Get", "RelativePath": "api/Notification/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.NotificationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.NotificationController", "Method": "Delete", "RelativePath": "api/Notification/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.NotificationController", "Method": "MarkAsRead", "RelativePath": "api/Notification/{id}/read", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.NotificationController", "Method": "GetByUser", "RelativePath": "api/Notification/user/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.ReadDTOs.NotificationDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.NotificationController", "Method": "GetUnreadByUser", "RelativePath": "api/Notification/user/{userId}/unread", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.ReadDTOs.NotificationDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PaiementsController", "Method": "ProcesserPaiement", "RelativePath": "api/Paiements", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.ReadDTOs.PaiementDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ValidationProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 401}]}, {"ContainingType": "WebApiPfe.Controllers.PaiementsController", "Method": "GetPaiements", "RelativePath": "api/Paiements", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PaiementsController", "Method": "GetPaiement", "RelativePath": "api/Paiements/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PaiementsController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Paiements/rembourser", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.ReadDTOs.RemboursementDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.PanierController", "Method": "Create", "RelativePath": "api/Panier", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreatePanierDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.PanierDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PanierController", "Method": "Update", "RelativePath": "api/Panier/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.UpdatePanierDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.PanierController", "Method": "Delete", "RelativePath": "api/Panier/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.PanierController", "Method": "AjouterItem", "RelativePath": "api/Panier/{panierId}/items", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "panierId", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.AddItemPanierDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ItemPanierDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PanierController", "Method": "GetPanierActif", "RelativePath": "api/Panier/client/{clientId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "clientId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.PanierDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PanierController", "Method": "ModifierItem", "RelativePath": "api/Panier/items/{itemId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.UpdateItemPanierDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.PanierController", "Method": "SupprimerItem", "RelativePath": "api/Panier/items/{itemId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "itemId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "Create", "RelativePath": "api/Produits", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ReferenceOriginal", "Type": "System.String", "IsRequired": false}, {"Name": "ReferenceFournisseur", "Type": "System.String", "IsRequired": false}, {"Name": "CodeABarre", "Type": "System.String", "IsRequired": false}, {"Name": "Nom", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "PrixAchatHT", "Type": "System.Decimal", "IsRequired": false}, {"Name": "PrixVenteHT", "Type": "System.Decimal", "IsRequired": false}, {"Name": "TauxTVAId", "Type": "System.Int32", "IsRequired": false}, {"Name": "Stock", "Type": "System.Int32", "IsRequired": false}, {"Name": "SousCategorieId", "Type": "System.Int32", "IsRequired": false}, {"Name": "MarqueId", "Type": "System.Int32", "IsRequired": false}, {"Name": "FormeId", "Type": "System.Int32", "IsRequired": false}, {"Name": "FournisseurId", "Type": "System.Int32", "IsRequired": false}, {"Name": "ImageFiles", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "PourcentageRemise", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ProduitDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetAll", "RelativePath": "api/Produits", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "Update", "RelativePath": "api/Produits/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "Id", "Type": "System.Int32", "IsRequired": false}, {"Name": "Nom", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "PrixVenteHT", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Stock", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PourcentageRemise", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ImageFiles", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "Delete", "RelativePath": "api/Produits/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetById", "RelativePath": "api/Produits/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ProduitDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "DeleteImage", "RelativePath": "api/Produits/{id}/images/{imageId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "UpdatePrix", "RelativePath": "api/Produits/{id}/prix", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.ProduitPrixUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "UpdateStock", "RelativePath": "api/Produits/{id}/stock", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "stockDto", "Type": "WebApiPfe.DTOs.UpdateDTOs.ProduitStockUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetImagesForProduit", "RelativePath": "api/Produits/{produitId}/images", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ImageProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "UpdateImage", "RelativePath": "api/Produits/{produitId}/images/{imageId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageId", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageDto", "Type": "WebApiPfe.DTOs.UpdateDTOs.ImageProduitUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "SetMainImage", "RelativePath": "api/Produits/{produitId}/images/{imageId}/set-main", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetByCategorie", "RelativePath": "api/Produits/by-categorie/{categorieId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categorieId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetByCodeABarre", "RelativePath": "api/Produits/by-code-barre/{codeABarre}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "codeABarre", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ProduitDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetByForme", "RelativePath": "api/Produits/by-forme/{formeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "formeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetByFournisseur", "RelativePath": "api/Produits/by-fournisseur/{fournisseurId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fournisseurId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetByMarque", "RelativePath": "api/Produits/by-marque/{marqueId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "marqueId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetByReferenceFournisseur", "RelativePath": "api/Produits/by-reference-fournisseur/{reference}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "reference", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ProduitDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetByReferenceOriginal", "RelativePath": "api/Produits/by-reference-original/{reference}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "reference", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.ProduitDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetBySousCategorie", "RelativePath": "api/Produits/by-sous-categorie/{sousCategorieId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "sousCategorieId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetForDropdown", "RelativePath": "api/Produits/dropdown", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetProduitsEnStock", "RelativePath": "api/Produits/en-stock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetMeilleuresVentes", "RelativePath": "api/Produits/meilleures-ventes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetNouveauxArrivages", "RelativePath": "api/Produits/nouveaux-arrivages", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "GetProduitsEnPromotion", "RelativePath": "api/Produits/promotions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.ProduitsController", "Method": "Search", "RelativePath": "api/Produits/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "term", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.ProduitDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsController", "Method": "GetAllActivePromotions", "RelativePath": "api/Promotions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.PromotionDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsController", "Method": "CreatePromotion", "RelativePath": "api/Promotions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreatePromotionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.PromotionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsController", "Method": "GetPromotionById", "RelativePath": "api/Promotions/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.PromotionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsController", "Method": "UpdatePromotion", "RelativePath": "api/Promotions/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreatePromotionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.PromotionsController", "Method": "DeletePromotion", "RelativePath": "api/Promotions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.PromotionsController", "Method": "CalculateFinalPrice", "RelativePath": "api/Promotions/calculate-price", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": false}, {"Name": "codePromo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Decimal", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsController", "Method": "GetPromotionsForProduct", "RelativePath": "api/Promotions/produit/{produitId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "produitId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.PromotionDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsController", "Method": "ValidatePromoCode", "RelativePath": "api/Promotions/validate-code", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsUtiliseesController", "Method": "ApplyPromotion", "RelativePath": "api/PromotionsUtilisees/apply", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.ApplyPromotionDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsUtiliseesController", "Method": "GetByCommande", "RelativePath": "api/PromotionsUtilisees/commande/{commandeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsUtiliseesController", "Method": "GetTotalSavings", "RelativePath": "api/PromotionsUtilisees/economies", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Decimal", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.PromotionsUtiliseesController", "Method": "GetByPromotion", "RelativePath": "api/PromotionsUtilisees/promotion/{promotionId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "promotionId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.RemboursementController", "Method": "Create", "RelativePath": "api/Remboursement", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.CreateDTOs.CreateRemboursementDto", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.RemboursementDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.RemboursementController", "Method": "Get", "RelativePath": "api/Remboursement/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.RemboursementDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.RemboursementController", "Method": "Update", "RelativePath": "api/Remboursement/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.UpdateDTOs.UpdateRemboursementDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.RemboursementController", "Method": "ProcessRemboursement", "RelativePath": "api/Remboursement/{id}/process", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.ReadDTOs.ProcessRemboursementDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.RemboursementController", "Method": "GetByCommande", "RelativePath": "api/Remboursement/commande/{commandeId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "commandeId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.ReadDTOs.RemboursementDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.RemboursementController", "Method": "GetByStatut", "RelativePath": "api/Remboursement/statut/{statut}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "statut", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.ReadDTOs.RemboursementDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.RemboursementController", "Method": "GetByUser", "RelativePath": "api/Remboursement/utilisateur/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[WebApiPfe.DTOs.ReadDTOs.RemboursementDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "GetAll", "RelativePath": "api/SousCategories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.SousCategorie, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "Create", "RelativePath": "api/SousCategories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.ReadDTOs.SousCategorieDto+Create", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.Models.Entity.SousCategorie", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "GetById", "RelativePath": "api/SousCategories/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.Models.Entity.SousCategorie", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "Update", "RelativePath": "api/SousCategories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "dto", "Type": "WebApiPfe.DTOs.ReadDTOs.SousCategorieDto+Update", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "Delete", "RelativePath": "api/SousCategories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "GetProduitsCount", "RelativePath": "api/SousCategories/{id}/produits-count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Int32", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "GetByCategorie", "RelativePath": "api/SousCategories/by-categorie/{categorieId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categorieId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.SousCategorie, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "DebugProduits", "RelativePath": "api/SousCategories/debug", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fournisseurId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "GetForDropdown", "RelativePath": "api/SousCategories/dropdown/{categorieId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categorieId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "GetAllEnriched", "RelativePath": "api/SousCategories/enriched", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fournisseurId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.DTOs.ReadDTOs.SousCategorieDto, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.SousCategoriesController", "Method": "GetAllForFournisseur", "RelativePath": "api/SousCategories/fournisseur", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.SousCategorie, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.TestCommandeController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/TestCommande", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.Controllers.CommandeSimpleDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.TestDataController", "Method": "CreateTestCommandeFournisseur", "RelativePath": "api/TestData/create-test-commande-fournisseur", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.TestDataController", "Method": "FixAllCommandes", "RelativePath": "api/TestData/fix-all-commandes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "GetAll", "RelativePath": "api/tva", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inclureInactifs", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.TauxTVA, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "Create", "RelativePath": "api/tva", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "WebApiPfe.DTOs.ReadDTOs.TauxTVADto+Create", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.DTOs.ReadDTOs.TauxTVADto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 201}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}]}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "GetById", "RelativePath": "api/tva/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WebApiPfe.Models.Entity.TauxTVA", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "Update", "RelativePath": "api/tva/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "taux", "Type": "WebApiPfe.Models.Entity.TauxTVA", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "Delete", "RelativePath": "api/tva/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "GetTauxActuel", "RelativePath": "api/tva/actuel", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WebApiPfe.Models.Entity.TauxTVA", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "GetByCategorie", "RelativePath": "api/tva/by-categorie/{categorieId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categorieId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[WebApiPfe.Models.Entity.TauxTVA, WebApiPfe, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "CalculerHT", "RelativePath": "api/tva/calculer-ht", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tauxId", "Type": "System.Int32", "IsRequired": false}, {"Name": "prixTTC", "Type": "System.Decimal", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Decimal", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "CalculerTTC", "RelativePath": "api/tva/calculer-ttc", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tauxId", "Type": "System.Int32", "IsRequired": false}, {"Name": "prixHT", "Type": "System.Decimal", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Decimal", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "GetForDropdown", "RelativePath": "api/tva/dropdown", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WebApiPfe.Controllers.TauxTVAController", "Method": "GetDropdownByCategorie", "RelativePath": "api/tva/dropdown/{categorieId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categorieId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.Dictionary`2[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]