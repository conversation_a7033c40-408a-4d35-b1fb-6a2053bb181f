﻿using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.Services.Interfaces
{
    public interface IAvisService
    {
        Task<List<AvisDto>> GetAvisByProduitAsync(int produitId);
        Task<AvisDto> AjouterAvisAsync(AvisCreateDto dto, int clientId);
        Task<AvisDto?> GetByIdAsync(int produitId, int id);
        Task SupprimerAvisAsync(int id);
        Task<int> GetNombreAvisAsync(int produitId);
        Task<double> GetNoteMoyenneAsync(int produitId);
        Task<AvisDto?> GetAvisClientProduitAsync(int clientId, int produitId);

        // Méthodes pour la modération (Admin)
        Task<List<AvisModerationDto>> GetAvisForModerationAsync(AvisFilterDto filter);
        Task<AvisModerationDto?> GetAvisModerationAsync(int id);
        Task<AvisModerationDto> ModererAvisAsync(int id, ModererAvisDto dto, int moderateurId);
        Task<AvisModerationDto> SupprimerCommentaireAsync(int id, int moderateurId, string? raisonSuppression = null);
        Task<AvisModerationDto> RestaurerCommentaireAsync(int id, int moderateurId);
        Task<AvisStatsDto> GetAvisStatsAsync();

        // Méthodes pour les fournisseurs
        Task<List<AvisModerationDto>> GetAvisFournisseurAsync(int fournisseurId, AvisFilterDto filter);
        Task<AvisStatsDto> GetAvisStatsFournisseurAsync(int fournisseurId);
        Task<AvisModerationDto> RepondreAvisAsync(int id, string reponse, int fournisseurId);
    }
}
