<div class="container-fluid">
  <!-- En-tête -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h2 class="mb-0">
        <i class="bi bi-shield-check me-2"></i>
        Modération des Avis
      </h2>
      <p class="text-muted mb-0"><PERSON><PERSON>rez les avis clients et leurs commentaires</p>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="row mb-4" *ngIf="stats">
    <div class="col-md-3">
      <div class="card bg-primary text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ stats.totalAvis }}</h4>
              <p class="mb-0">Total Avis</p>
            </div>
            <i class="bi bi-chat-square-text fs-1"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ stats.avisPublies }}</h4>
              <p class="mb-0">Publiés</p>
            </div>
            <i class="bi bi-check-circle fs-1"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-warning text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ stats.avisSignales }}</h4>
              <p class="mb-0">Signalés</p>
            </div>
            <i class="bi bi-exclamation-triangle fs-1"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-danger text-white">
        <div class="card-body">
          <div class="d-flex justify-content-between">
            <div>
              <h4 class="mb-0">{{ stats.commentairesSupprimés || 0 }}</h4>
              <p class="mb-0">Commentaires supprimés</p>
            </div>
            <i class="bi bi-trash fs-1"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filtres -->
  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">
        <i class="bi bi-funnel me-2"></i>
        Filtres
      </h5>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-lg-3 col-md-6">
          <label class="form-label">
            <i class="bi bi-bookmark-check me-2"></i>
            Statut
          </label>
          <select class="form-select" [(ngModel)]="filter.statut" (change)="onFilterChange()">
            <option [value]="undefined">Tous les statuts</option>
            <option [value]="StatutAvis.Publie">✅ Publié</option>
            <option [value]="StatutAvis.Signale">⚠️ Signalé</option>
          </select>
        </div>
        <div class="col-lg-3 col-md-6">
          <label class="form-label">
            <i class="bi bi-search me-2"></i>
            Recherche
          </label>
          <input type="text" class="form-control" placeholder="Produit, client, commentaire..."
                 [(ngModel)]="filter.recherche" (input)="onFilterChange()">
        </div>
        <div class="col-lg-2 col-md-6">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" (click)="resetFilters()" title="Réinitialiser les filtres">
              <i class="bi bi-arrow-clockwise me-1"></i>
              <span class="d-none d-lg-inline">Réinitialiser</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger alert-dismissible">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="clearError()"></button>
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Chargement...</span>
    </div>
  </div>

  <!-- Liste des avis -->
  <div class="card" *ngIf="!loading">
    <div class="card-header">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
          <i class="bi bi-chat-square-text"></i>
          Avis à modérer
          <span class="badge bg-primary ms-2">{{ avis.length }}</span>
        </h5>
      </div>
    </div>
    <div class="card-body p-0">
      <div *ngIf="avis.length === 0" class="text-center py-5">
        <i class="bi bi-inbox display-1 text-muted"></i>
        <h4 class="text-muted mt-3">Aucun avis trouvé</h4>
        <p class="text-muted">Aucun avis ne correspond aux critères de recherche.</p>
      </div>

      <div *ngFor="let avisItem of avis; trackBy: trackByAvisId" class="avis-item border-bottom">
        <div class="row g-0">
          <div class="col-md-8">
            <div class="p-4">
              <!-- Informations produit et client -->
              <div class="d-flex justify-content-between align-items-start mb-3">
                <div>
                  <h6 class="mb-1">{{ avisItem.produitNom }}</h6>
                  <small class="text-muted">
                    Par {{ avisItem.clientNom }} {{ avisItem.clientPrenom }}
                    • {{ formatDate(avisItem.datePublication) }}
                  </small>
                </div>
                <div class="text-end">
                  <!-- Note -->
                  <div class="mb-2">
                    <span *ngFor="let i of [1,2,3,4,5]" 
                          class="text-warning me-1"
                          [class.text-muted]="i > avisItem.note">
                      <i class="bi bi-star-fill"></i>
                    </span>
                    <span class="ms-2 fw-bold">{{ avisItem.note }}/5</span>
                  </div>
                  <!-- Statut -->
                  <span class="badge" [class]="getStatutClass(avisItem.statut)">
                    {{ getStatutText(avisItem.statut) }}
                  </span>
                </div>
              </div>

              <!-- Commentaire -->
              <div class="mb-3">
                <div *ngIf="avisItem.commentaire && avisItem.commentaire.trim()" 
                     class="bg-light p-3 rounded">
                  <p class="mb-0">{{ avisItem.commentaire }}</p>
                </div>
                <div *ngIf="!avisItem.commentaire || !avisItem.commentaire.trim()" 
                     class="text-muted fst-italic">
                  <i class="bi bi-chat-square-x me-2"></i>
                  Commentaire supprimé
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="p-4 border-start h-100 d-flex flex-column justify-content-center">
              <!-- Actions -->
              <div class="btn-group-vertical w-100">
                <button class="btn btn-outline-danger mb-2"
                        *ngIf="peutSupprimerCommentaire(avisItem)"
                        (click)="openSuppressionModal(avisItem)"
                        title="Supprimer le commentaire">
                  <i class="bi bi-trash me-2"></i>
                  Supprimer commentaire
                </button>

                <button class="btn btn-outline-info"
                        (click)="openHistoryModal(avisItem)"
                        title="Voir l'historique">
                  <i class="bi bi-clock-history me-2"></i>
                  Historique
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Modales inline pour cet avis -->
        <div class="inline-modals" *ngIf="selectedAvis?.id === avisItem.id">

          <!-- Modal de suppression de commentaire inline -->
          <div *ngIf="showSuppressionModal" class="inline-modal suppression-modal">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Supprimer le commentaire
              </h5>
              <button type="button" class="btn-close" (click)="closeSuppressionModal()"></button>
            </div>
            <div class="modal-body">
              <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Cette action va :</strong>
                <ul class="mb-0 mt-2">
                  <li>Supprimer uniquement le commentaire (la note reste visible)</li>
                  <li>Envoyer une notification au client : <em>"Votre avis était supprimé à cause de [raison] mais ne t'inquiète pas, votre note est mise en considération"</em></li>
                </ul>
              </div>
              <form (ngSubmit)="supprimerCommentaire()">
                <div class="mb-3">
                  <label class="form-label">
                    <i class="bi bi-chat-text me-1"></i>
                    Raison de la suppression <span class="text-danger">*</span>
                  </label>
                  <textarea class="form-control" [(ngModel)]="suppressionForm.raison"
                            name="raison" rows="3" required
                            placeholder="Expliquez pourquoi ce commentaire est supprimé (ex: contenu inapproprié, spam, etc.)..."></textarea>
                </div>
                <div class="d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" (click)="closeSuppressionModal()">
                    <i class="bi bi-x-circle me-1"></i>
                    Annuler
                  </button>
                  <button type="submit" class="btn btn-danger" [disabled]="!suppressionForm.raison || !suppressionForm.raison.trim()">
                    <i class="bi bi-trash me-1"></i>
                    Supprimer et notifier
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- Modal d'historique inline -->
          <div *ngIf="showHistoryModal" class="inline-modal history-modal">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="bi bi-clock-history me-2"></i>
                Historique de l'avis #{{ selectedAvis?.id }}
              </h5>
              <button type="button" class="btn-close" (click)="closeHistoryModal()"></button>
            </div>
            <div class="modal-body">
              <div class="timeline">
                <div class="timeline-item">
                  <div class="timeline-marker bg-primary">
                    <i class="bi bi-plus-circle text-white"></i>
                  </div>
                  <div class="timeline-content">
                    <h6>Avis publié</h6>
                    <small class="text-muted">
                      {{ formatDate(selectedAvis?.datePublication || '') }}
                    </small>
                    <p class="mb-0">
                      Par {{ selectedAvis?.clientNom }} {{ selectedAvis?.clientPrenom }}
                    </p>
                  </div>
                </div>
                
                <div *ngIf="selectedAvis?.dateModeration" class="timeline-item">
                  <div class="timeline-marker bg-warning">
                    <i class="bi bi-pencil-square text-white"></i>
                  </div>
                  <div class="timeline-content">
                    <h6>Modération</h6>
                    <small class="text-muted">
                      {{ formatDate(selectedAvis?.dateModeration || '') }}
                    </small>
                    <p class="mb-0">
                      Par {{ selectedAvis?.nomModerateur || 'Administrateur' }}
                    </p>
                    <div *ngIf="selectedAvis?.commentaireModeration" class="mt-2">
                      <small class="text-muted">{{ selectedAvis?.commentaireModeration }}</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
