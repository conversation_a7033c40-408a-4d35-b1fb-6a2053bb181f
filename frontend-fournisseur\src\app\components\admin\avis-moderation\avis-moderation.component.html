<div class="avis-moderation-container">
  <div class="header-section">
    <h2><i class="bi bi-chat-square-text"></i> Modération des Avis</h2>
    <p class="text-muted"><PERSON><PERSON><PERSON> et modérez les avis clients sur les produits</p>
  </div>

  <!-- Tableau de bord des statistiques -->
  <div class="stats-dashboard">
    <div class="row g-4">
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="stat-card card">
          <div class="card-body">
            <div class="stat-icon bg-primary">
              <i class="bi bi-chat-square-text text-primary"></i>
            </div>
            <h3 class="stat-number">{{ stats?.totalAvis || 0 }}</h3>
            <p class="stat-label">Total Avis</p>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="stat-card card">
          <div class="card-body">
            <div class="stat-icon bg-success">
              <i class="bi bi-check-circle text-success"></i>
            </div>
            <h3 class="stat-number">{{ stats?.avisPublies || 0 }}</h3>
            <p class="stat-label">Publiés</p>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="stat-card card">
          <div class="card-body">
            <div class="stat-icon bg-warning">
              <i class="bi bi-exclamation-triangle text-warning"></i>
            </div>
            <h3 class="stat-number">{{ stats?.avisCommentaireSupprime || 0 }}</h3>
            <p class="stat-label">Commentaires supprimés</p>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="stat-card card">
          <div class="card-body">
            <div class="stat-icon bg-danger">
              <i class="bi bi-flag text-danger"></i>
            </div>
            <h3 class="stat-number">{{ stats?.avisSignales || 0 }}</h3>
            <p class="stat-label">Signalés</p>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="stat-card card">
          <div class="card-body">
            <div class="stat-icon bg-info">
              <i class="bi bi-star text-warning"></i>
            </div>
            <h3 class="stat-number">{{ (stats?.noteMoyenneGlobale || 0).toFixed(1) }}</h3>
            <p class="stat-label">Note Moyenne</p>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="stat-card card">
          <div class="card-body">
            <div class="stat-icon bg-primary">
              <i class="bi bi-calendar-event text-primary"></i>
            </div>
            <h3 class="stat-number">{{ stats?.avisRecents?.length || 0 }}</h3>
            <p class="stat-label">Récents</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Filtres -->
  <div class="filters-section card">
    <div class="card-body">
      <div class="filters-header">
        <h5>
          <i class="bi bi-funnel"></i>
          Filtres de recherche
        </h5>
      </div>

      <div class="row g-4">
        <div class="col-lg-2 col-md-6">
          <label class="form-label">
            <i class="bi bi-bookmark-check me-2"></i>
            Statut
          </label>
          <select class="form-select" [(ngModel)]="filter.statut" (change)="onFilterChange()">
            <option [value]="undefined">Tous les statuts</option>
            <option [value]="StatutAvis.Publie">✅ Publié</option>
            <option [value]="StatutAvis.CommentaireSupprime">⚠️ Commentaire supprimé</option>
            <option [value]="StatutAvis.Signale">🚩 Signalé</option>
          </select>
        </div>

        <div class="col-lg-2 col-md-6">
          <label class="form-label">
            <i class="bi bi-building me-2"></i>
            Fournisseur
          </label>
          <select class="form-select" [(ngModel)]="filter.fournisseurId" (change)="onFilterChange()">
            <option [value]="undefined">Tous les fournisseurs</option>
            <!-- TODO: Charger la liste des fournisseurs -->
          </select>
        </div>

        <div class="col-lg-3 col-md-6">
          <label class="form-label">
            <i class="bi bi-search me-2"></i>
            Recherche
          </label>
          <input type="text" class="form-control" placeholder="Produit, client, commentaire..."
                 [(ngModel)]="filter.recherche" (input)="onFilterChange()">
        </div>

        <div class="col-lg-2 col-md-6">
          <label class="form-label">
            <i class="bi bi-calendar me-2"></i>
            Date début
          </label>
          <input type="date" class="form-control" [(ngModel)]="filter.dateDebut" (change)="onFilterChange()">
        </div>

        <div class="col-lg-2 col-md-6">
          <label class="form-label">
            <i class="bi bi-calendar me-2"></i>
            Date fin
          </label>
          <input type="date" class="form-control" [(ngModel)]="filter.dateFin" (change)="onFilterChange()">
        </div>

        <div class="col-lg-1 col-md-6">
          <label class="form-label">&nbsp;</label>
          <div class="d-flex flex-column gap-2">
            <button class="btn btn-outline-secondary" (click)="resetFilters()" title="Réinitialiser les filtres">
              <i class="bi bi-arrow-clockwise"></i>
            </button>
            <button class="btn btn-primary" (click)="exportAvis()" title="Exporter les avis">
              <i class="bi bi-download"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Message d'erreur -->
  <div *ngIf="error" class="alert alert-danger">
    <i class="bi bi-exclamation-triangle"></i> {{ error }}
  </div>

  <!-- Loading -->
  <div *ngIf="loading" class="loading-spinner">
    <div class="spinner-border" role="status">
      <span class="visually-hidden">Chargement...</span>
    </div>
    <div class="loading-text">
      <strong>Chargement des avis...</strong>
      <br>
      <small>Veuillez patienter</small>
    </div>
  </div>

  <!-- Liste des avis -->
  <div *ngIf="!loading" class="avis-list">
    <div class="card">
      <div class="card-header">
        <div class="row align-items-center">
          <div class="col">
            <div class="d-flex align-items-center">
              <div class="form-check me-4">
                <input class="form-check-input" type="checkbox"
                       [checked]="areAllAvisSelected()"
                       (change)="selectAllAvis()"
                       id="selectAll">
                <label class="form-check-label fw-bold" for="selectAll">
                  Tout sélectionner
                </label>
              </div>
              <h5 class="mb-0">
                <i class="bi bi-chat-square-text"></i>
                Avis à modérer
                <span class="badge bg-primary ms-2">{{ avis.length }}</span>
              </h5>
              <span *ngIf="selectedAvisIds.size > 0" class="badge bg-success ms-3">
                <i class="bi bi-check-circle me-1"></i>
                {{ selectedAvisIds.size }} sélectionné(s)
              </span>
            </div>
          </div>
          <div class="col-auto">
            <div class="btn-group">
              <button class="btn btn-outline-secondary"
                      [class.active]="filter.sortBy === 'datePublication'"
                      (click)="onSortChange('datePublication')"
                      title="Trier par date">
                <i class="bi bi-calendar me-1"></i>
                Date
                <i class="bi ms-1" [class.bi-arrow-up]="filter.sortBy === 'datePublication' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'datePublication' && filter.sortDesc"></i>
              </button>
              <button class="btn btn-outline-secondary"
                      [class.active]="filter.sortBy === 'note'"
                      (click)="onSortChange('note')"
                      title="Trier par note">
                <i class="bi bi-star me-1"></i>
                Note
                <i class="bi ms-1" [class.bi-arrow-up]="filter.sortBy === 'note' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'note' && filter.sortDesc"></i>
              </button>
              <button class="btn btn-outline-secondary"
                      [class.active]="filter.sortBy === 'statut'"
                      (click)="onSortChange('statut')"
                      title="Trier par statut">
                <i class="bi bi-bookmark me-1"></i>
                Statut
                <i class="bi ms-1" [class.bi-arrow-up]="filter.sortBy === 'statut' && !filter.sortDesc"
                   [class.bi-arrow-down]="filter.sortBy === 'statut' && filter.sortDesc"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="card-body p-0">
        <div *ngIf="avis.length === 0" class="empty-state">
          <i class="bi bi-chat-square-text"></i>
          <h5 class="mb-3">Aucun avis trouvé</h5>
          <p class="mb-0">
            Aucun avis ne correspond aux critères de recherche actuels.<br>
            Essayez de modifier vos filtres ou de réinitialiser la recherche.
          </p>
        </div>
        
        <div *ngFor="let avisItem of avis" class="avis-item">
          <div class="row g-4 align-items-start">
            <!-- Case à cocher -->
            <div class="col-auto">
              <div class="form-check">
                <input class="form-check-input" type="checkbox"
                       [checked]="isAvisSelected(avisItem.id)"
                       (change)="toggleAvisSelection(avisItem.id)"
                       [id]="'avis-' + avisItem.id">
              </div>
            </div>

            <!-- Informations produit et client -->
            <div class="col-lg-4 col-md-5">
              <div class="product-info">
                <h6>{{ avisItem.produitNom }}</h6>
                <div class="product-reference">
                  {{ avisItem.produitReference }}
                </div>
                <div class="mt-2">
                  <small class="text-muted">
                    <i class="bi bi-building me-1"></i>
                    {{ avisItem.fournisseurRaisonSociale }}
                  </small>
                </div>
              </div>

              <div class="client-info">
                <div class="client-name">
                  <i class="bi bi-person-circle me-2"></i>
                  {{ avisItem.clientPrenom }} {{ avisItem.clientNom }}
                </div>
                <div class="client-date">
                  <i class="bi bi-calendar3 me-1"></i>
                  {{ avisItem.datePublication | date:'dd/MM/yyyy à HH:mm' }}
                </div>
              </div>
            </div>

            <!-- Avis et commentaire -->
            <div class="col-lg-5 col-md-7">
              <div class="rating">
                <div class="stars">
                  <span *ngFor="let star of getStars(avisItem.note)"
                        class="bi" [class]="'bi-' + star"></span>
                </div>
                <div class="rating-value">{{ avisItem.note }}/5</div>
              </div>

              <div class="avis-comment"
                   [class.comment-supprime]="avisItem.statut === StatutAvis.CommentaireSupprime"
                   *ngIf="avisItem.commentaire">
                <div [title]="avisItem.commentaire">
                  {{ (avisItem.commentaire || '').length > 150 ?
                     (avisItem.commentaire || '').substring(0, 150) + '...' :
                     (avisItem.commentaire || '') }}
                </div>
              </div>

              <div *ngIf="!avisItem.commentaire" class="text-muted fst-italic">
                <i class="bi bi-chat-square-dots me-1"></i>
                Aucun commentaire
              </div>
            </div>

            <!-- Statut et actions -->
            <div class="col-lg-3 col-md-12">
              <div class="d-flex flex-column align-items-end">
                <div class="mb-3">
                  <span class="badge" [class]="'bg-' + getStatutColor(avisItem.statut)">
                    <i class="bi" [class]="'bi-' + getStatutIcon(avisItem.statut)"></i>
                    {{ getStatutLibelle(avisItem.statut) }}
                  </span>
                </div>

                <div *ngIf="avisItem.dateModeration" class="mb-3 text-end">
                  <small class="text-muted">
                    <i class="bi bi-clock-history me-1"></i>
                    Modéré le {{ avisItem.dateModeration | date:'dd/MM/yyyy' }}
                    <br *ngIf="avisItem.nomModerateur">
                    <span *ngIf="avisItem.nomModerateur" class="fw-bold">
                      par {{ avisItem.nomModerateur }}
                    </span>
                  </small>
                </div>

                <div class="btn-group">
                  <button class="btn btn-outline-primary"
                          (click)="openModerationModal(avisItem)"
                          title="Modérer cet avis">
                    <i class="bi bi-pencil-square"></i>
                    <span class="d-none d-lg-inline ms-1">Modérer</span>
                  </button>

                  <button class="btn btn-outline-warning"
                          *ngIf="peutSupprimerCommentaire(avisItem)"
                          (click)="openSuppressionModal(avisItem)"
                          title="Supprimer le commentaire">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span class="d-none d-lg-inline ms-1">Supprimer</span>
                  </button>

                  <button class="btn btn-outline-success"
                          *ngIf="peutRestaurerCommentaire(avisItem)"
                          (click)="restaurerCommentaire(avisItem)"
                          title="Restaurer le commentaire">
                    <i class="bi bi-arrow-clockwise"></i>
                    <span class="d-none d-lg-inline ms-1">Restaurer</span>
                  </button>

                  <button class="btn btn-outline-info"
                          (click)="openHistoryModal(avisItem)"
                          title="Voir l'historique">
                    <i class="bi bi-clock-history"></i>
                    <span class="d-none d-lg-inline ms-1">Historique</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Barre d'actions en lot -->
  <div *ngIf="showBatchActions" class="batch-actions-bar card">
    <div class="card-body">
      <div class="row align-items-center g-3">
        <div class="col-md-4">
          <h6 class="mb-0 text-primary">
            <i class="bi bi-check-square-fill me-2"></i>
            <strong>{{ selectedAvisIds.size }}</strong> avis sélectionné(s)
          </h6>
        </div>
        <div class="col-md-8">
          <div class="d-flex gap-3 align-items-center flex-wrap">
            <div class="flex-grow-1">
              <label class="form-label mb-1 fw-bold">Action à appliquer :</label>
              <select class="form-select" [(ngModel)]="batchAction.statut">
                <option [value]="StatutAvis.Publie">✅ Publier les avis</option>
                <option [value]="StatutAvis.CommentaireSupprime">⚠️ Supprimer les commentaires</option>
                <option [value]="StatutAvis.Signale">🚩 Signaler les avis</option>
              </select>
            </div>
            <div class="flex-grow-1">
              <label class="form-label mb-1 fw-bold">Commentaire :</label>
              <input type="text" class="form-control"
                     placeholder="Raison de l'action (optionnel)"
                     [(ngModel)]="batchAction.commentaire">
            </div>
            <div class="d-flex gap-2 align-self-end">
              <button class="btn btn-primary" (click)="executeBatchAction()">
                <i class="bi bi-check-circle me-1"></i>
                Appliquer
              </button>
              <button class="btn btn-outline-secondary" (click)="cancelBatchAction()">
                <i class="bi bi-x-circle me-1"></i>
                Annuler
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div *ngIf="avis.length > 0" class="pagination-section mt-3">
    <nav>
      <ul class="pagination justify-content-center">
        <li class="page-item" [class.disabled]="filter.page === 1">
          <button class="page-link" (click)="previousPage()">Précédent</button>
        </li>
        <li class="page-item active">
          <span class="page-link">{{ filter.page }}</span>
        </li>
        <li class="page-item">
          <button class="page-link" (click)="nextPage()">Suivant</button>
        </li>
      </ul>
    </nav>
  </div>
</div>

<!-- Modal de modération -->
<!-- Modal de modération -->
<div *ngIf="selectedAvis" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-chat-square-text"></i> Modérer l'avis
        </h5>
        <button type="button" class="btn-close" (click)="closeModerationModal()"></button>
      </div>

      <div class="modal-body">
        <!-- Détails de l'avis -->
        <div class="avis-details mb-4">
          <div class="row">
            <div class="col-md-6">
              <h6>Produit</h6>
              <p>{{ selectedAvis.produitNom }}</p>
              <small class="text-muted">Réf: {{ selectedAvis.produitReference }}</small>
            </div>
            <div class="col-md-6">
              <h6>Client</h6>
              <p>{{ selectedAvis.clientPrenom }} {{ selectedAvis.clientNom }}</p>
              <small class="text-muted">{{ selectedAvis.clientEmail }}</small>
            </div>
          </div>

          <div class="mt-3">
            <h6>Avis</h6>
            <div class="rating mb-2">
              <span *ngFor="let star of getStars(selectedAvis.note)"
                    class="bi" [class]="'bi-' + star" style="color: #ffc107;"></span>
              <span class="ms-2">{{ selectedAvis.note }}/5</span>
            </div>
            <p class="border p-3 rounded bg-light">{{ selectedAvis.commentaire || 'Aucun commentaire' }}</p>
          </div>
        </div>

        <!-- Formulaire de modération -->
        <form (ngSubmit)="modererAvis()">
          <div class="mb-3">
            <label class="form-label">Statut</label>
            <select class="form-select" [(ngModel)]="moderationForm.statut" name="statut" required>
              <option [value]="StatutAvis.Publie">Publier</option>
              <option [value]="StatutAvis.CommentaireSupprime">Supprimer commentaire</option>
              <option [value]="StatutAvis.Signale">Signaler</option>
            </select>
          </div>

          <div class="mb-3">
            <label class="form-label">Commentaire de modération (optionnel)</label>
            <textarea class="form-control" rows="3"
                      [(ngModel)]="moderationForm.commentaireModeration"
                      name="commentaireModeration"
                      placeholder="Raison du rejet, commentaire interne..."></textarea>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeModerationModal()">
          Annuler
        </button>
        <button type="button" class="btn btn-primary" (click)="modererAvis()">
          <i class="bi bi-check"></i> Modérer
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal d'historique de modération -->
<div *ngIf="showHistoryModal && selectedAvisHistory" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-clock-history"></i> Historique de l'avis
        </h5>
        <button type="button" class="btn-close" (click)="closeHistoryModal()"></button>
      </div>

      <div class="modal-body">
        <!-- Informations de l'avis -->
        <div class="avis-summary mb-4">
          <div class="row">
            <div class="col-md-6">
              <h6>Produit</h6>
              <p class="mb-1">{{ selectedAvisHistory.produitNom }}</p>
              <small class="text-muted">Réf: {{ selectedAvisHistory.produitReference }}</small>
            </div>
            <div class="col-md-6">
              <h6>Client</h6>
              <p class="mb-1">{{ selectedAvisHistory.clientPrenom }} {{ selectedAvisHistory.clientNom }}</p>
              <small class="text-muted">{{ selectedAvisHistory.clientEmail }}</small>
            </div>
          </div>

          <div class="mt-3">
            <div class="d-flex align-items-center mb-2">
              <div class="rating me-3">
                <span *ngFor="let star of getStars(selectedAvisHistory.note)"
                      class="bi" [class]="'bi-' + star" style="color: #ffc107;"></span>
                <span class="ms-2">{{ selectedAvisHistory.note }}/5</span>
              </div>
              <span class="badge" [class]="getStatutBadgeClass(selectedAvisHistory.statut)">
                <i class="bi" [class]="'bi-' + getStatutIcon(selectedAvisHistory.statut)"></i>
                {{ getStatutLibelle(selectedAvisHistory.statut) }}
              </span>
            </div>
            <p class="border p-3 rounded bg-light mb-0">
              {{ selectedAvisHistory.commentaire || 'Aucun commentaire' }}
            </p>
          </div>
        </div>

        <!-- Timeline de modération -->
        <div class="moderation-timeline">
          <h6>Historique de modération</h6>

          <div class="timeline">
            <!-- Publication initiale -->
            <div class="timeline-item">
              <div class="timeline-marker bg-primary">
                <i class="bi bi-plus-circle text-white"></i>
              </div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <h6 class="mb-1">Avis publié</h6>
                  <small class="text-muted">{{ formatDate(selectedAvisHistory.datePublication) }}</small>
                </div>
                <p class="mb-0">L'avis a été publié par le client</p>
              </div>
            </div>

            <!-- Modération (si applicable) -->
            <div *ngIf="selectedAvisHistory.dateModeration" class="timeline-item">
              <div class="timeline-marker" [class]="getStatutBadgeClass(selectedAvisHistory.statut)">
                <i class="bi" [class]="'bi-' + getStatutIcon(selectedAvisHistory.statut)"></i>
              </div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <h6 class="mb-1">Avis modéré</h6>
                  <small class="text-muted">{{ formatDate(selectedAvisHistory.dateModeration) }}</small>
                </div>
                <p class="mb-1">
                  Statut changé vers :
                  <span class="badge" [class]="getStatutBadgeClass(selectedAvisHistory.statut)">
                    {{ getStatutLibelle(selectedAvisHistory.statut) }}
                  </span>
                </p>
                <p *ngIf="selectedAvisHistory.nomModerateur" class="mb-1">
                  <strong>Modérateur :</strong> {{ selectedAvisHistory.nomModerateur }}
                </p>
                <p *ngIf="selectedAvisHistory.commentaireModeration" class="mb-0">
                  <strong>Commentaire :</strong> {{ selectedAvisHistory.commentaireModeration }}
                </p>
              </div>
            </div>

            <!-- État actuel -->
            <div class="timeline-item">
              <div class="timeline-marker bg-success">
                <i class="bi bi-check-circle text-white"></i>
              </div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <h6 class="mb-1">État actuel</h6>
                  <small class="text-muted">{{ formatDate(currentDate) }}</small>
                </div>
                <p class="mb-0">
                  L'avis est actuellement :
                  <span class="badge" [class]="getStatutBadgeClass(selectedAvisHistory.statut)">
                    {{ getStatutLibelle(selectedAvisHistory.statut) }}
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeHistoryModal()">
          Fermer
        </button>
        <button type="button" class="btn btn-primary" (click)="openModerationModal(selectedAvisHistory); closeHistoryModal()">
          <i class="bi bi-pencil"></i> Modérer cet avis
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Modal de suppression de commentaire -->
<div class="modal fade" [class.show]="showSuppressionModal" [style.display]="showSuppressionModal ? 'block' : 'none'"
     tabindex="-1" *ngIf="showSuppressionModal">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          <i class="bi bi-exclamation-triangle text-warning"></i>
          Supprimer le commentaire
        </h5>
        <button type="button" class="btn-close" (click)="closeSuppressionModal()"></button>
      </div>

      <div class="modal-body" *ngIf="selectedAvis">
        <div class="alert alert-warning">
          <i class="bi bi-info-circle"></i>
          <strong>Attention :</strong> Cette action supprimera le commentaire de l'avis mais conservera la note.
          Le commentaire pourra être restauré ultérieurement.
        </div>

        <!-- Informations de l'avis -->
        <div class="card mb-3">
          <div class="card-body">
            <h6 class="card-title">Avis de {{ selectedAvis.clientNom }} {{ selectedAvis.clientPrenom }}</h6>
            <div class="d-flex align-items-center mb-2">
              <div class="stars me-2">
                <span *ngFor="let star of getStars(selectedAvis.note)"
                      class="bi" [class]="'bi-' + star"
                      [class.text-warning]="star === 'star-fill'"></span>
              </div>
              <span class="text-muted">{{ selectedAvis.note }}/5</span>
            </div>
            <p class="card-text">{{ selectedAvis.commentaire }}</p>
            <small class="text-muted">
              Produit : {{ selectedAvis.produitNom }} ({{ selectedAvis.produitReference }})
            </small>
          </div>
        </div>

        <!-- Formulaire de suppression -->
        <form (ngSubmit)="supprimerCommentaire()">
          <div class="mb-3">
            <label class="form-label">Raison de la suppression <span class="text-danger">*</span></label>
            <textarea class="form-control" rows="3"
                      [(ngModel)]="suppressionForm.raisonSuppression"
                      name="raisonSuppression"
                      placeholder="Expliquez pourquoi ce commentaire doit être supprimé..."
                      required></textarea>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="closeSuppressionModal()">
          Annuler
        </button>
        <button type="button" class="btn btn-warning"
                (click)="supprimerCommentaire()"
                [disabled]="!suppressionForm.raisonSuppression.trim()">
          <i class="bi bi-exclamation-triangle"></i> Supprimer le commentaire
        </button>
      </div>
    </div>
  </div>
</div>
