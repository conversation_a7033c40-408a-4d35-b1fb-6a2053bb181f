﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Services.Interfaces;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CommandeFournisseurController : ControllerBase
    {
        private readonly ICommandeFournisseurService _service;
        private readonly ILogger<CommandeFournisseurController> _logger;
        private readonly AppDbContext _context;

        public CommandeFournisseurController(
            ICommandeFournisseurService service,
            ILogger<CommandeFournisseurController> logger,
            AppDbContext context)
        {
            _service = service;
            _logger = logger;
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<CommandeFournisseurDto>>> GetAll([FromQuery] int? fournisseurId = null)
        {
            try
            {
                _logger.LogInformation("🔍 Début récupération commandes fournisseurs");

                IEnumerable<CommandeFournisseurDto> commandes;

                if (fournisseurId.HasValue)
                {
                    _logger.LogInformation($"🔍 Récupération commandes pour fournisseur ID: {fournisseurId}");
                    commandes = await _service.GetByFournisseurAsync(fournisseurId.Value);
                }
                else
                {
                    commandes = await _service.GetAllAsync();
                }

                _logger.LogInformation($"🔍 {commandes?.Count() ?? 0} commandes trouvées");

                return Ok(commandes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erreur lors de la récupération des commandes");
                return StatusCode(500, new { Error = "Une erreur interne est survenue", Details = ex.Message });
            }
        }

        [HttpGet("test-direct")]
        public async Task<ActionResult> GetTestDirect()
        {
            try
            {
                var commandesFournisseurs = await _context.CommandesFournisseurs
                    .Include(cf => cf.Fournisseur)
                    .Include(cf => cf.LignesCommande)
                        .ThenInclude(l => l.Produit)
                    .Select(cf => new
                    {
                        Id = cf.Id,
                        Reference = cf.Reference,
                        CommandeClientId = cf.CommandeClientId,
                        FournisseurId = cf.FournisseurId,
                        NomFournisseur = cf.Fournisseur.RaisonSociale,
                        DateCreation = cf.DateCreation,
                        MontantTotal = cf.MontantTotal,
                        Statut = cf.Statut.ToString(),
                        NombreLignes = cf.LignesCommande.Count()
                    })
                    .ToListAsync();

                return Ok(new
                {
                    Total = commandesFournisseurs.Count,
                    Commandes = commandesFournisseurs
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<CommandeFournisseurDto>> GetById(int id)
        {
            try
            {
                var commande = await _service.GetByIdAsync(id);
                return Ok(commande);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération de la commande {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPost]
        public async Task<ActionResult<CommandeFournisseurDto>> Create(
            [FromBody] CreateCommandeFournisseurDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                var result = await _service.CreateAsync(dto);
                return CreatedAtAction(nameof(GetById), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la création de la commande");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }

        [HttpPut("{id}/statut")]
        public async Task<IActionResult> UpdateStatut(
            int id,
            [FromBody] UpdateStatutCommandeFournisseurDto dto)
        {
            if (!ModelState.IsValid)
                return BadRequest(ModelState);

            try
            {
                await _service.UpdateStatutAsync(id, dto);
                return NoContent();
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la mise à jour du statut de la commande {id}");
                return StatusCode(500, "Une erreur interne est survenue");
            }
        }
    }

}
