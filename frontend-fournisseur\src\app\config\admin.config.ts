export interface AdminCredentials {
  username: string;
  password: string;
  role: string;
  permissions: string[];
}

export const ADMIN_CONFIG: AdminCredentials = {
  username: 'adminOptiLet',
  password: 'adminOptiLet!2025',
  role: 'ADMIN',
  permissions: [
    'MANAGE_USERS',
    'MANAGE_PRODUCTS',
    'MANAGE_ORDERS',
    'MANAGE_CATEGORIES',
    'MANAGE_PROMOTIONS',
    'VIEW_STATISTICS',
    'MANAGE_SETTINGS',
    'MODERATE_CONTENT',
    'MANAGE_REFUNDS',
    'SYSTEM_ADMIN'
  ]
};

export const ADMIN_USER_INFO = {
  id: 1,
  username: ADMIN_CONFIG.username,
  email: '<EMAIL>',
  nom: 'Administrateur',
  prenom: 'OptiLet',
  role: ADMIN_CONFIG.role,
  permissions: ADMIN_CONFIG.permissions,
  isActive: true,
  lastLogin: new Date(),
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date(),
  profileImage: 'https://via.placeholder.com/150x150?text=ADMIN',
  phoneNumber: '+33123456789',
  department: 'Administration Système',
  twoFactorEnabled: false, 
  sessionTimeout: 3600
};
