﻿using AutoMapper;
using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.DTOs.UpdateDTOs;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.Services.Profile
{
    public class CommandeFournisseurProfile : AutoMapper.Profile
    {
        public CommandeFournisseurProfile()
        {
            // Mapping CommandeFournisseur
            CreateMap<CommandeFournisseur, CommandeFournisseurDto>()
                .ForMember(dest => dest.NomFournisseur, opt => opt.MapFrom(src => src.Fournisseur != null ? src.Fournisseur.RaisonSociale : ""))
                .ForMember(dest => dest.MatriculeFiscale, opt => opt.MapFrom(src => src.Fournisseur != null ? src.Fournisseur.MatriculeFiscale : ""))
                .ForMember(dest => dest.Statut, opt => opt.MapFrom(src => src.Statut.ToString()))
                .ForMember(dest => dest.LignesCommande, opt => opt.MapFrom(src => src.LignesCommande ?? new List<LigneCommandeFournisseur>()));

            // Mapping Create DTO vers Entité
            CreateMap<CreateCommandeFournisseurDto, CommandeFournisseur>();

            // Mapping LigneCommandeFournisseur (un seul mapping au lieu de deux)
            CreateMap<LigneCommandeFournisseur, LigneCommandeFournisseurDto>()
                .ForMember(dest => dest.NomProduit, opt => opt.MapFrom(src => src.Produit != null ? src.Produit.Nom : ""))
                .ForMember(dest => dest.ReferenceProduit, opt => opt.MapFrom(src => src.Produit != null ? src.Produit.ReferenceOriginal : ""))
                .ForMember(dest => dest.TotalLigne, opt => opt.MapFrom(src => src.MontantLigne))
                .ForMember(dest => dest.ImagePrincipale, opt => opt.MapFrom(src =>
                    src.Produit != null && src.Produit.Images != null && src.Produit.Images.Any()
                        ? src.Produit.Images.FirstOrDefault(i => i.IsMain) != null
                            ? src.Produit.Images.FirstOrDefault(i => i.IsMain).ImageUrl
                            : null
                        : null));

            // Mapping Create/Update DTO vers Entité
            CreateMap<CreateLigneCommandeFournisseurDto, LigneCommandeFournisseur>();
            CreateMap<UpdateLigneCommandeFournisseurDto, LigneCommandeFournisseur>();
        }
    }
}
