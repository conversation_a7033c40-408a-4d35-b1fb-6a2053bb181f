﻿using System.Collections.Generic;
using System.Threading.Tasks;
using WebApiPfe.Models;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.Services.Interfaces
{
    public interface ISousCategorieService
    {
        // CRUD de base
        Task<SousCategorie> GetByIdAsync(int id);
        Task<IEnumerable<SousCategorie>> GetAllAsync();
        Task<SousCategorie> CreateAsync(SousCategorie sousCategorie);
        Task UpdateAsync(SousCategorie sousCategorie);
        Task DeleteAsync(int id);

        // Méthodes spécifiques
        Task<IEnumerable<SousCategorie>> GetByCategorieAsync(int categorieId);
        Task<bool> ExistsAsync(int id);
        Task<bool> IsNameUniqueInCategorieAsync(string name, int categorieId, int? ignoreId = null);
        Task<int> GetProduitsCountAsync(int sousCategorieId);
        Task<Dictionary<int, string>> GetSousCategoriesForDropdownAsync(int categorieId);
        Task<IEnumerable<SousCategorie>> GetAllForFournisseurAsync(int fournisseurId);
    }
}
