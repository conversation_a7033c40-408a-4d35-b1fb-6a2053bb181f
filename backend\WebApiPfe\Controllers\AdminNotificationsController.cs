using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/admin/[controller]")]
    [AllowAnonymous] // Temporaire pour test
    public class NotificationsController : ControllerBase
    {
        public class AdminNotificationDto
        {
            public int Id { get; set; }
            public string Type { get; set; } = string.Empty;
            public string Titre { get; set; } = string.Empty;
            public string Message { get; set; } = string.Empty;
            public string? FournisseurNom { get; set; }
            public int? FournisseurId { get; set; }
            public int? ReferenceId { get; set; }
            public DateTime DateCreation { get; set; }
            public bool EstLue { get; set; }
            public string Priority { get; set; } = "MEDIUM";
            public string? ActionUrl { get; set; }
        }

        public class DemandeNotificationDetailDto
        {
            public int DemandeId { get; set; }
            public string Type { get; set; } = string.Empty;
            public string NomDemande { get; set; } = string.Empty;
            public string FournisseurRaisonSociale { get; set; } = string.Empty;
            public int FournisseurId { get; set; }
            public DateTime DateCreation { get; set; }
            public string Statut { get; set; } = string.Empty;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<AdminNotificationDto>>> GetAdminNotifications()
        {
            try
            {
                Console.WriteLine("🔔 GET /api/admin/notifications - Récupération des notifications admin");

                // Pour le moment, retourner des notifications mock
                var mockNotifications = GetMockNotifications();
                
                Console.WriteLine($"📊 Nombre de notifications admin: {mockNotifications.Count}");
                
                return Ok(mockNotifications);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans GetAdminNotifications: {ex.Message}");
                return StatusCode(500, new { message = "Erreur lors de la récupération des notifications admin", error = ex.Message });
            }
        }

        [HttpGet("demandes")]
        public async Task<ActionResult<IEnumerable<DemandeNotificationDetailDto>>> GetDemandesNotifications()
        {
            try
            {
                Console.WriteLine("🔔 GET /api/admin/notifications/demandes - Récupération des notifications de demandes");

                // Pour le moment, retourner des demandes mock
                var mockDemandes = GetMockDemandesNotifications();
                
                Console.WriteLine($"📊 Nombre de demandes notifications: {mockDemandes.Count}");
                
                return Ok(mockDemandes);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans GetDemandesNotifications: {ex.Message}");
                return StatusCode(500, new { message = "Erreur lors de la récupération des notifications de demandes", error = ex.Message });
            }
        }

        [HttpPut("{id}/read")]
        public async Task<ActionResult> MarkAsRead(int id)
        {
            try
            {
                Console.WriteLine($"✅ PUT /api/admin/notifications/{id}/read - Marquer comme lue");
                
                // En production, mettre à jour en base de données
                // await _notificationService.MarkAsReadAsync(id);
                
                return Ok(new { message = "Notification marquée comme lue" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans MarkAsRead: {ex.Message}");
                return StatusCode(500, new { message = "Erreur lors de la mise à jour", error = ex.Message });
            }
        }

        [HttpPut("mark-all-read")]
        public async Task<ActionResult> MarkAllAsRead()
        {
            try
            {
                Console.WriteLine("✅ PUT /api/admin/notifications/mark-all-read - Marquer toutes comme lues");
                
                // En production, mettre à jour toutes les notifications en base
                // await _notificationService.MarkAllAsReadAsync();
                
                return Ok(new { message = "Toutes les notifications marquées comme lues" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans MarkAllAsRead: {ex.Message}");
                return StatusCode(500, new { message = "Erreur lors de la mise à jour", error = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteNotification(int id)
        {
            try
            {
                Console.WriteLine($"🗑️ DELETE /api/admin/notifications/{id} - Supprimer notification");
                
                // En production, supprimer de la base de données
                // await _notificationService.DeleteAsync(id);
                
                return Ok(new { message = "Notification supprimée" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Erreur dans DeleteNotification: {ex.Message}");
                return StatusCode(500, new { message = "Erreur lors de la suppression", error = ex.Message });
            }
        }

        private List<AdminNotificationDto> GetMockNotifications()
        {
            return new List<AdminNotificationDto>
            {
                new AdminNotificationDto
                {
                    Id = 1,
                    Type = "DEMANDE_CATEGORIE",
                    Titre = "Nouvelle demande de catégorie",
                    Message = "Le fournisseur Optique Vision Plus souhaite créer une nouvelle catégorie nommée \"Lunettes de Sport\"",
                    FournisseurNom = "Optique Vision Plus",
                    FournisseurId = 11,
                    ReferenceId = 1,
                    DateCreation = DateTime.Now,
                    EstLue = false,
                    Priority = "HIGH",
                    ActionUrl = "/admin/dashboard/demandes"
                },
                new AdminNotificationDto
                {
                    Id = 2,
                    Type = "DEMANDE_SOUS_CATEGORIE",
                    Titre = "Nouvelle demande de sous-catégorie",
                    Message = "Le fournisseur Optique El Manar souhaite créer une nouvelle sous-catégorie nommée \"Lunettes de Natation\"",
                    FournisseurNom = "Optique El Manar",
                    FournisseurId = 12,
                    ReferenceId = 2,
                    DateCreation = DateTime.Now.AddHours(-1),
                    EstLue = false,
                    Priority = "HIGH",
                    ActionUrl = "/admin/dashboard/demandes"
                },
                new AdminNotificationDto
                {
                    Id = 3,
                    Type = "NOUVEAU_PRODUIT",
                    Titre = "Nouveau produit en attente",
                    Message = "Le fournisseur Optique Centrale a ajouté un nouveau produit \"Ray-Ban Aviator\" en attente de validation",
                    FournisseurNom = "Optique Centrale",
                    FournisseurId = 13,
                    ReferenceId = 101,
                    DateCreation = DateTime.Now.AddHours(-2),
                    EstLue = true,
                    Priority = "MEDIUM",
                    ActionUrl = "/admin/dashboard/products"
                }
            };
        }

        private List<DemandeNotificationDetailDto> GetMockDemandesNotifications()
        {
            return new List<DemandeNotificationDetailDto>
            {
                new DemandeNotificationDetailDto
                {
                    DemandeId = 1,
                    Type = "CATEGORIE",
                    NomDemande = "Lunettes de Sport",
                    FournisseurRaisonSociale = "Optique Vision Plus",
                    FournisseurId = 11,
                    DateCreation = DateTime.Now,
                    Statut = "EN_ATTENTE"
                },
                new DemandeNotificationDetailDto
                {
                    DemandeId = 2,
                    Type = "SOUS_CATEGORIE",
                    NomDemande = "Lunettes de Natation",
                    FournisseurRaisonSociale = "Optique El Manar",
                    FournisseurId = 12,
                    DateCreation = DateTime.Now.AddHours(-1),
                    Statut = "EN_ATTENTE"
                }
            };
        }
    }
}
