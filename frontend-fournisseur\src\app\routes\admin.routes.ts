import { Routes } from '@angular/router';
import { AdminAuthGuard } from '../guards/admin-auth.guard';
import { SuperAdminGuard } from '../guards/super-admin.guard';
import { RoleGuard } from '../guards/role.guard';
import { AdminRole, PermissionAction } from '../models/admin.model';

export const adminRoutes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'login',
    loadComponent: () => import('../components/admin/auth/admin-login/admin-login.component').then(m => m.AdminLoginComponent),
    title: 'Connexion Admin - Optique Vision'
  },
  {
    path: 'dashboard',
    canActivate: [AdminAuthGuard],
    loadComponent: () => import('../components/admin/layout/admin-layout/admin-layout.component').then(m => m.AdminLayoutComponent),
    title: 'Dashboard Admin - Optique Vision',
    children: [
      {
        path: '',
        loadComponent: () => import('../components/admin/admin-dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent),
        title: 'Tableau de bord - Admin'
      },
      {
        path: 'users',
        canActivate: [RoleGuard],
        data: {
          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],
          permissions: [{ resource: 'users', action: PermissionAction.READ }]
        },
        loadComponent: () => import('../components/admin/user-management/user-management.component').then(m => m.UserManagementComponent),
        title: 'Gestion des utilisateurs - Admin'
      },
      {
        path: 'users/roles',
        canActivate: [SuperAdminGuard],
        loadComponent: () => import('../components/admin/users/role-assignment/role-assignment.component').then(m => m.RoleAssignmentComponent),
        title: 'Attribution des rôles - Admin'
      },
      {
        path: 'products',
        canActivate: [RoleGuard],
        data: {
          permissions: [{ resource: 'products', action: PermissionAction.READ }]
        },
        loadComponent: () => import('../components/admin/product-management/product-management.component').then(m => m.ProductManagementComponent),
        title: 'Gestion des produits - Admin'
      },
      {
        path: 'products/validation',
        canActivate: [RoleGuard],
        data: { 
          permissions: [{ resource: 'products', action: PermissionAction.APPROVE }]
        },
        loadComponent: () => import('../components/admin/products/product-validation/product-validation.component').then(m => m.ProductValidationComponent),
        title: 'Validation des produits - Admin'
      },
      {
        path: 'categories',
        canActivate: [AdminAuthGuard],
        loadComponent: () => import('../components/admin/category-management/category-management.component').then(m => m.CategoryManagementComponent),
        title: 'Gestion des catégories - Admin'
      },
      {
        path: 'demandes',
        canActivate: [RoleGuard],
        data: {
          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],
          permissions: [{ resource: 'categories', action: PermissionAction.APPROVE }]
        },
        loadComponent: () => import('../components/admin/demandes-management/demandes-management.component').then(m => m.DemandesManagementComponent),
        title: 'Gestion des demandes - Admin'
      },
      {
        path: 'commissions',
        canActivate: [SuperAdminGuard],
        loadComponent: () => import('../components/admin/business/commission-rules/commission-rules.component').then(m => m.CommissionRulesComponent),
        title: 'Règles de commission - Admin'
      },
      {
        path: 'promotions',
        canActivate: [RoleGuard],
        data: {
          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],
          permissions: [{ resource: 'promotions', action: PermissionAction.CREATE }]
        },
        loadComponent: () => import('../components/admin/business/promotion-engine/promotion-engine.component').then(m => m.PromotionEngineComponent),
        title: 'Moteur de promotions - Admin'
      },
      {
        path: 'avis-moderation',
        canActivate: [RoleGuard],
        data: {
          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN],
          permissions: [{ resource: 'reviews', action: PermissionAction.MODERATE }]
        },
        loadComponent: () => import('../components/admin/avis-moderation/avis-moderation.component').then(m => m.AvisModerationComponent),
        title: 'Modération des avis - Admin'
      },
      {
        path: 'audit',
        canActivate: [RoleGuard],
        data: { 
          permissions: [{ resource: 'audit', action: PermissionAction.AUDIT }]
        },
        loadComponent: () => import('../components/admin/supervision/audit-log/audit-log.component').then(m => m.AuditLogComponent),
        title: 'Journal d\'audit - Admin'
      },
      {
        path: 'alerts',
        canActivate: [AdminAuthGuard],
        loadComponent: () => import('../components/admin/supervision/alerting-center/alerting-center.component').then(m => m.AlertingCenterComponent),
        title: 'Centre d\'alertes - Admin'
      },
      {
        path: 'batch',
        canActivate: [SuperAdminGuard],
        loadComponent: () => import('../components/admin/tools/batch-operations/batch-operations.component').then(m => m.BatchOperationsComponent),
        title: 'Opérations par lots - Admin'
      },
      {
        path: 'workflow',
        canActivate: [RoleGuard],
        data: { 
          roles: [AdminRole.ADMIN, AdminRole.SUPER_ADMIN]
        },
        loadComponent: () => import('../components/admin/tools/workflow-engine/workflow-engine.component').then(m => m.WorkflowEngineComponent),
        title: 'Moteur de workflow - Admin'
      },
      {
        path: 'settings',
        canActivate: [AdminAuthGuard],
        loadComponent: () => import('../components/admin/settings/settings.component').then(m => m.SettingsComponent),
        title: 'Paramètres système - Admin'
      },
      {
        path: 'profile',
        canActivate: [AdminAuthGuard],
        loadComponent: () => import('../components/admin/profile/admin-profile/admin-profile.component').then(m => m.AdminProfileComponent),
        title: 'Mon profil - Admin'
      },
      {
        path: 'reports',
        canActivate: [AdminAuthGuard],
        loadComponent: () => import('../components/admin/reports/reports.component').then(m => m.ReportsComponent),
        title: 'Rapports - Admin'
      },
      {
        path: 'orders',
        canActivate: [AdminAuthGuard],
        loadComponent: () => import('../components/admin/order-management/order-management.component').then(m => m.OrderManagementComponent),
        title: 'Gestion des commandes - Admin'
      },
      {
        path: 'stats',
        canActivate: [AdminAuthGuard],
        loadComponent: () => import('../components/admin/statistiques-admin/statistiques-admin.component').then(m => m.StatistiquesAdminComponent),
        title: 'Statistiques - Admin'
      }
    ]
  },
  {
    path: 'unauthorized',
    loadComponent: () => import('../components/admin/errors/unauthorized/unauthorized.component').then(m => m.UnauthorizedComponent),
    title: 'Accès non autorisé - Admin'
  },
  {
    path: '**',
    redirectTo: 'login'
  }
];
