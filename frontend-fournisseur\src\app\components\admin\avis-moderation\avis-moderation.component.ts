import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AvisModerationService, AvisModerationDto, AvisFilterDto, StatutAvis, ModererAvisDto, AvisStatsDto } from '../../../services/avis-moderation.service';

@Component({
  selector: 'app-avis-moderation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './avis-moderation.component.html',
  styleUrls: ['./avis-moderation.component.scss']
})
export class AvisModerationComponent implements OnInit {
  avis: AvisModerationDto[] = [];
  loading = false;
  error: string | null = null;
  stats: AvisStatsDto | null = null;
  loadingStats = false;

  // Filtres
  filter: AvisFilterDto = {
    page: 1,
    pageSize: 10,
    sortBy: 'datePublication',
    sortDesc: true
  };

  // Énumérations pour le template
  StatutAvis = StatutAvis;

  // Modération
  selectedAvis: AvisModerationDto | null = null;
  moderationForm = {
    statut: StatutAvis.Publie,
    commentaireModeration: ''
  };

  // Actions en lot
  selectedAvisIds: Set<number> = new Set();
  showBatchActions = false;
  batchAction = {
    statut: StatutAvis.Publie,
    commentaire: ''
  };

  // Suppression/Restauration de commentaires
  showSuppressionModal = false;
  suppressionForm = {
    raisonSuppression: ''
  };

  // Historique de modération
  showHistoryModal = false;
  selectedAvisHistory: AvisModerationDto | null = null;
  currentDate = new Date();

  constructor(private avisModerationService: AvisModerationService) { }

  ngOnInit(): void {
    this.loadAvis();
    this.loadStats();
  }

  loadAvis(): void {
    this.loading = true;
    this.error = null;

    this.avisModerationService.getAvisForModeration(this.filter).subscribe({
      next: (data) => {
        this.avis = data;
        this.loading = false;
        this.signalTestData(); // Vérifier les données de test
      },
      error: (error) => {
        console.error('Erreur lors du chargement des avis:', error);
        this.error = 'Erreur lors du chargement des avis';
        this.loading = false;
      }
    });
  }

  onFilterChange(): void {
    this.filter.page = 1; // Reset à la première page
    this.loadAvis();
  }

  onSortChange(sortBy: string): void {
    if (this.filter.sortBy === sortBy) {
      this.filter.sortDesc = !this.filter.sortDesc;
    } else {
      this.filter.sortBy = sortBy;
      this.filter.sortDesc = true;
    }
    this.loadAvis();
  }

  openModerationModal(avis: AvisModerationDto): void {
    this.selectedAvis = avis;
    this.moderationForm = {
      statut: avis.statut,
      commentaireModeration: avis.commentaireModeration || ''
    };
  }

  closeModerationModal(): void {
    this.selectedAvis = null;
    this.moderationForm = {
      statut: StatutAvis.Publie,
      commentaireModeration: ''
    };
  }

  modererAvis(): void {
    if (!this.selectedAvis) return;

    const dto: ModererAvisDto = {
      statut: this.moderationForm.statut,
      commentaireModeration: this.moderationForm.commentaireModeration || undefined
    };

    this.avisModerationService.modererAvis(this.selectedAvis.id, dto).subscribe({
      next: (updatedAvis) => {
        // Mettre à jour l'avis dans la liste
        const index = this.avis.findIndex(a => a.id === updatedAvis.id);
        if (index !== -1) {
          this.avis[index] = updatedAvis;
        }
        this.closeModerationModal();
        this.loadStats(); // Recharger les statistiques
      },
      error: (error) => {
        console.error('Erreur lors de la modération:', error);
        this.error = 'Erreur lors de la modération de l\'avis';
      }
    });
  }

  getStatutLibelle(statut: StatutAvis): string {
    return this.avisModerationService.getStatutLibelle(statut);
  }

  getStatutColor(statut: StatutAvis): string {
    return this.avisModerationService.getStatutColor(statut);
  }

  getStatutIcon(statut: StatutAvis): string {
    return this.avisModerationService.getStatutIcon(statut);
  }

  getStars(note: number): string[] {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(i <= note ? 'star-fill' : 'star');
    }
    return stars;
  }

  resetFilters(): void {
    this.filter = {
      page: 1,
      pageSize: 10,
      sortBy: 'datePublication',
      sortDesc: true
    };
    this.loadAvis();
  }

  exportAvis(): void {
    // TODO: Implémenter l'export des avis
    console.log('Export des avis à implémenter');
  }

  // Nouvelles méthodes pour les statistiques
  loadStats(): void {
    this.loadingStats = true;
    this.avisModerationService.getAvisStats().subscribe({
      next: (stats) => {
        this.stats = stats;
        this.loadingStats = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement des statistiques:', error);
        this.loadingStats = false;
      }
    });
  }

  // Méthodes pour les actions en lot
  toggleAvisSelection(avisId: number): void {
    if (this.selectedAvisIds.has(avisId)) {
      this.selectedAvisIds.delete(avisId);
    } else {
      this.selectedAvisIds.add(avisId);
    }
    this.showBatchActions = this.selectedAvisIds.size > 0;
  }

  selectAllAvis(): void {
    if (this.selectedAvisIds.size === this.avis.length) {
      this.selectedAvisIds.clear();
    } else {
      this.avis.forEach(avis => this.selectedAvisIds.add(avis.id));
    }
    this.showBatchActions = this.selectedAvisIds.size > 0;
  }

  isAvisSelected(avisId: number): boolean {
    return this.selectedAvisIds.has(avisId);
  }

  areAllAvisSelected(): boolean {
    return this.avis.length > 0 && this.selectedAvisIds.size === this.avis.length;
  }

  executeBatchAction(): void {
    if (this.selectedAvisIds.size === 0) return;

    const promises = Array.from(this.selectedAvisIds).map(avisId => {
      const dto: ModererAvisDto = {
        statut: this.batchAction.statut,
        commentaireModeration: this.batchAction.commentaire || undefined
      };
      return this.avisModerationService.modererAvis(avisId, dto).toPromise();
    });

    Promise.all(promises).then(() => {
      this.selectedAvisIds.clear();
      this.showBatchActions = false;
      this.loadAvis();
      this.loadStats();
    }).catch(error => {
      console.error('Erreur lors de l\'action en lot:', error);
      this.error = 'Erreur lors de l\'exécution de l\'action en lot';
    });
  }

  cancelBatchAction(): void {
    this.selectedAvisIds.clear();
    this.showBatchActions = false;
    this.batchAction = {
      statut: StatutAvis.Publie,
      commentaire: ''
    };
  }

  // Méthodes pour l'historique de modération
  openHistoryModal(avis: AvisModerationDto): void {
    this.selectedAvisHistory = avis;
    this.showHistoryModal = true;
  }

  closeHistoryModal(): void {
    this.selectedAvisHistory = null;
    this.showHistoryModal = false;
  }

  // Méthodes utilitaires pour l'affichage
  getStatutBadgeClass(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie: return 'bg-success';
      case StatutAvis.CommentaireSupprime: return 'bg-warning text-dark';
      case StatutAvis.Signale: return 'bg-danger';
      default: return 'bg-secondary';
    }
  }

  formatDate(date: Date | string): string {
    if (!date) return 'Non défini';
    const d = new Date(date);
    return d.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Pagination
  previousPage(): void {
    if (this.filter.page && this.filter.page > 1) {
      this.filter.page--;
      this.loadAvis();
    }
  }

  nextPage(): void {
    if (this.filter.page) {
      this.filter.page++;
      this.loadAvis();
    }
  }

  // Méthodes pour la suppression et restauration de commentaires
  openSuppressionModal(avis: AvisModerationDto): void {
    this.selectedAvis = avis;
    this.showSuppressionModal = true;
    this.suppressionForm.raisonSuppression = '';
  }

  closeSuppressionModal(): void {
    this.selectedAvis = null;
    this.showSuppressionModal = false;
    this.suppressionForm.raisonSuppression = '';
  }

  supprimerCommentaire(): void {
    if (!this.selectedAvis || !this.suppressionForm.raisonSuppression.trim()) return;

    this.avisModerationService.supprimerCommentaire(
      this.selectedAvis.id,
      this.suppressionForm.raisonSuppression
    ).subscribe({
      next: (updatedAvis) => {
        // Mettre à jour l'avis dans la liste
        const index = this.avis.findIndex(a => a.id === updatedAvis.id);
        if (index !== -1) {
          this.avis[index] = updatedAvis;
        }
        this.closeSuppressionModal();
        this.loadStats(); // Recharger les statistiques
      },
      error: (error) => {
        console.error('Erreur lors de la suppression du commentaire:', error);
        this.error = 'Erreur lors de la suppression du commentaire';
      }
    });
  }

  restaurerCommentaire(avis: AvisModerationDto): void {
    if (confirm('Êtes-vous sûr de vouloir restaurer ce commentaire ?')) {
      this.avisModerationService.restaurerCommentaire(avis.id).subscribe({
        next: (updatedAvis) => {
          // Mettre à jour l'avis dans la liste
          const index = this.avis.findIndex(a => a.id === updatedAvis.id);
          if (index !== -1) {
            this.avis[index] = updatedAvis;
          }
          this.loadStats(); // Recharger les statistiques
        },
        error: (error) => {
          console.error('Erreur lors de la restauration du commentaire:', error);
          this.error = 'Erreur lors de la restauration du commentaire';
        }
      });
    }
  }

  peutSupprimerCommentaire(avis: AvisModerationDto): boolean {
    return avis.statut === StatutAvis.Publie && !!avis.commentaire && avis.commentaire.trim() !== '';
  }

  peutRestaurerCommentaire(avis: AvisModerationDto): boolean {
    return avis.statut === StatutAvis.CommentaireSupprime;
  }

  // Méthode pour identifier les données de test
  isTestData(avis: AvisModerationDto): boolean {
    const testIndicators = [
      'Commentaire supprimé par l\'administrateur',
      'test',
      'Test',
      'TEST',
      'mock',
      'Mock',
      'MOCK',
      'exemple',
      'Exemple',
      'EXEMPLE'
    ];

    return testIndicators.some(indicator =>
      avis.commentaire?.includes(indicator) ||
      avis.clientNom?.includes(indicator) ||
      avis.clientPrenom?.includes(indicator) ||
      avis.produitNom?.includes(indicator)
    );
  }

  // Méthode pour signaler les données de test
  signalTestData(): void {
    const testAvis = this.avis.filter(avis => this.isTestData(avis));
    if (testAvis.length > 0) {
      console.warn(`⚠️ ${testAvis.length} avis contiennent des données de test:`, testAvis);
      // Optionnel : afficher une notification à l'utilisateur
      if (testAvis.length > 0) {
        this.error = `Attention: ${testAvis.length} avis contiennent des données de test qui devraient être nettoyées.`;
      }
    }
  }
}
