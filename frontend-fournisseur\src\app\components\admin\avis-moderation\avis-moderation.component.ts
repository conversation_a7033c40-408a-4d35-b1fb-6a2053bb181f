import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AvisModerationService, AvisModerationDto, AvisFilterDto, StatutAvis, AvisStatsDto } from '../../../services/avis-moderation.service';

@Component({
  selector: 'app-avis-moderation',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './avis-moderation.component.html',
  styleUrls: ['./avis-moderation.component.scss']
})
export class AvisModerationComponent implements OnInit {
  avis: AvisModerationDto[] = [];
  loading = false;
  error: string | null = null;
  stats: AvisStatsDto | null = null;

  // Filtres
  filter: AvisFilterDto = {
    page: 1,
    pageSize: 10,
    sortBy: 'datePublication',
    sortDesc: true
  };

  // Énumérations pour le template
  StatutAvis = StatutAvis;

  // Modales inline
  selectedAvis: AvisModerationDto | null = null;
  showSuppressionModal = false;
  showHistoryModal = false;
  activeAvisId: number | null = null;

  // Formulaire de suppression
  suppressionForm = {
    raison: ''
  };

  constructor(
    private avisModerationService: AvisModerationService
  ) {}

  ngOnInit(): void {
    this.loadAvis();
    this.loadStats();
  }

  loadAvis(): void {
    this.loading = true;
    this.error = null;

    this.avisModerationService.getAvisForModeration(this.filter).subscribe({
      next: (response: AvisModerationDto[]) => {
        this.avis = response || [];
        this.loading = false;
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des avis:', error);
        this.error = 'Erreur lors du chargement des avis';
        this.loading = false;
      }
    });
  }

  loadStats(): void {
    this.avisModerationService.getAvisStats().subscribe({
      next: (stats: AvisStatsDto) => {
        this.stats = stats;
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des statistiques:', error);
      }
    });
  }

  // === SUPPRESSION DE COMMENTAIRE ===
  openSuppressionModal(avis: AvisModerationDto): void {
    this.selectedAvis = avis;
    this.activeAvisId = avis.id;
    this.showSuppressionModal = true;
    this.suppressionForm.raison = '';
  }

  closeSuppressionModal(): void {
    this.showSuppressionModal = false;
    this.selectedAvis = null;
    this.activeAvisId = null;
    this.suppressionForm.raison = '';
  }

  supprimerCommentaire(): void {
    if (!this.selectedAvis || !this.suppressionForm.raison.trim()) {
      return;
    }

    this.loading = true;

    this.avisModerationService.supprimerCommentaire(this.selectedAvis.id, this.suppressionForm.raison).subscribe({
      next: () => {
        // Notification de succès à l'admin
        alert(`Commentaire supprimé avec succès. Une notification a été envoyée au client : "Votre avis était supprimé à cause de '${this.suppressionForm.raison}' mais ne t'inquiète pas, votre note est mise en considération"`);

        // Recharger les données
        this.loadAvis();
        this.loadStats();
        this.closeSuppressionModal();
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors de la suppression:', error);
        alert('Erreur lors de la suppression du commentaire');
        this.loading = false;
      }
    });
  }

  // === HISTORIQUE ===
  openHistoryModal(avis: AvisModerationDto): void {
    this.selectedAvis = avis;
    this.activeAvisId = avis.id;
    this.showHistoryModal = true;
  }

  closeHistoryModal(): void {
    this.showHistoryModal = false;
    this.selectedAvis = null;
    this.activeAvisId = null;
  }

  // === UTILITAIRES ===
  peutSupprimerCommentaire(avis: AvisModerationDto): boolean {
    return !!(avis.commentaire && avis.commentaire.trim().length > 0);
  }

  peutRestaurerCommentaire(avis: AvisModerationDto): boolean {
    return !avis.commentaire || avis.commentaire.trim().length === 0;
  }

  getStatutClass(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie:
        return 'badge-success';
      case StatutAvis.CommentaireSupprime:
        return 'badge-danger';
      case StatutAvis.Signale:
        return 'badge-warning';
      default:
        return 'badge-secondary';
    }
  }

  getStatutText(statut: StatutAvis): string {
    switch (statut) {
      case StatutAvis.Publie:
        return 'Publié';
      case StatutAvis.CommentaireSupprime:
        return 'Commentaire supprimé';
      case StatutAvis.Signale:
        return 'Signalé';
      default:
        return 'Inconnu';
    }
  }

  formatDate(date: string | Date): string {
    if (!date) return '';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // === FILTRES ET PAGINATION ===
  onFilterChange(): void {
    this.filter.page = 1;
    this.loadAvis();
  }

  onPageChange(page: number): void {
    this.filter.page = page;
    this.loadAvis();
  }

  resetFilters(): void {
    this.filter = {
      page: 1,
      pageSize: 10,
      sortBy: 'datePublication',
      sortDesc: true
    };
    this.loadAvis();
  }

  exportAvis(): void {
    // Fonctionnalité d'export (à implémenter si nécessaire)
    alert('Fonctionnalité d\'export à implémenter');
  }

  restaurerCommentaire(_avis: AvisModerationDto): void {
    // Fonctionnalité de restauration (à implémenter si nécessaire)
    alert('Fonctionnalité de restauration à implémenter');
  }

  // === GESTION DES ERREURS ===
  clearError(): void {
    this.error = null;
  }

  // === TRACKING ===
  trackByAvisId(_index: number, avis: AvisModerationDto): number {
    return avis.id;
  }
}