@use 'sass:color';
@import '../../../../styles.scss';

// Variables de couleurs et styles
$gradient-primary: linear-gradient(135deg, #{$primary-color}, #{color.adjust($primary-color, $lightness: 10%)});
$shadow-light: 0 2px 10px rgba(0, 0, 0, 0.08);
$shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.12);
$shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.15);
$border-radius: 12px;
$transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

.avis-moderation-container {
  padding: 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;

  .header-section {
    background: linear-gradient(135deg, $primary-color 0%, color.adjust($primary-color, $lightness: -10%) 100%);
    color: white;
    padding: 2rem;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);

    h2 {
      font-weight: 700;
      margin-bottom: 0.5rem;
      font-size: 2.2rem;

      i {
        margin-right: 0.75rem;
        background: rgba(255,255,255,0.2);
        padding: 0.5rem;
        border-radius: 50%;
        font-size: 1.5rem;
      }
    }

    p {
      opacity: 0.9;
      font-size: 1.1rem;
      margin-bottom: 0;
    }
  }

  // Styles pour le tableau de bord des statistiques en ligne
  .stats-dashboard-inline {
    margin: 0 1.5rem 2rem 1.5rem;

    .stats-container {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      padding: 1.5rem 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      overflow: hidden;

      // Bordure colorée en haut
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, $primary-color, $accent-color);
      }

      .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: 1;
        transition: all 0.3s ease;
        padding: 0.5rem;
        border-radius: 12px;

        &:hover {
          background: rgba($primary-color, 0.05);
          transform: translateY(-2px);
        }

        .stat-icon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          background: rgba($primary-color, 0.1);
          color: $primary-color;
          transition: all 0.3s ease;

          i {
            font-weight: bold;
          }
        }

        .stat-content {
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .stat-number {
            font-size: 1.8rem;
            font-weight: 800;
            line-height: 1;
            margin-bottom: 0.25rem;
            background: linear-gradient(135deg, $primary-color, $accent-color);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }

          .stat-label {
            font-size: 0.85rem;
            color: #6c757d;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            white-space: nowrap;
          }
        }

        // Variantes de couleur
        &.stat-success {
          .stat-icon {
            background: rgba(#28a745, 0.1);
            color: #28a745;
          }

          &:hover {
            background: rgba(#28a745, 0.05);
          }
        }

        &.stat-warning {
          .stat-icon {
            background: rgba(#ffc107, 0.1);
            color: #ffc107;
          }

          &:hover {
            background: rgba(#ffc107, 0.05);
          }
        }

        &.stat-danger {
          .stat-icon {
            background: rgba(#dc3545, 0.1);
            color: #dc3545;
          }

          &:hover {
            background: rgba(#dc3545, 0.05);
          }
        }

        &.stat-info {
          .stat-icon {
            background: rgba(#17a2b8, 0.1);
            color: #17a2b8;
          }

          &:hover {
            background: rgba(#17a2b8, 0.05);
          }
        }
      }

      .stat-separator {
        width: 1px;
        height: 60px;
        background: linear-gradient(to bottom, transparent, #e9ecef, transparent);
        margin: 0 0.5rem;
        flex-shrink: 0;
      }
      }

    }
  }

  .filters-section {
    margin: 0 1.5rem 2rem 1.5rem;
    border: none;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);

    .card-body {
      padding: 2rem;
    }

    .filters-header {
      display: flex;
      align-items: center;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 2px solid #f8f9fa;

      h5 {
        margin: 0;
        color: $primary-color;
        font-weight: 700;
        display: flex;
        align-items: center;

        i {
          margin-right: 0.5rem;
          background: rgba($primary-color, 0.1);
          padding: 0.5rem;
          border-radius: 8px;
        }
      }
    }

    .form-label {
      font-weight: 600;
      color: $text-color;
      margin-bottom: 0.75rem;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .form-select,
    .form-control {
      border: 2px solid #e9ecef;
      border-radius: 12px;
      padding: 0.75rem 1rem;
      font-weight: 500;
      transition: all 0.3s ease;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.15);
        transform: translateY(-2px);
      }

      &:hover {
        border-color: color.adjust($primary-color, $lightness: 20%);
      }
    }

    .btn-primary {
      background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -10%));
      border: none;
      border-radius: 12px;
      padding: 0.75rem 2rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba($primary-color, 0.3);
      }
    }
  }
  
  .avis-list {
    margin: 0 1.5rem;

    .card {
      border: none;
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      overflow: hidden;

      .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: none;
        padding: 1.5rem 2rem;

        h5 {
          color: $primary-color;
          font-weight: 700;
          margin: 0;
          display: flex;
          align-items: center;

          i {
            margin-right: 0.75rem;
            background: rgba($primary-color, 0.1);
            padding: 0.5rem;
            border-radius: 8px;
          }
        }

        .btn-group {
          .btn {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-weight: 600;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;

            &.active {
              background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -10%));
              border-color: $primary-color;
              color: white;
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba($primary-color, 0.3);
            }

            &:hover:not(.active) {
              background-color: #f8f9fa;
              border-color: $primary-color;
              transform: translateY(-1px);
            }
          }
        }
      }
    }

    .avis-item {
      padding: 1.5rem 2rem;
      border-bottom: 1px solid #f0f0f0;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transform: translateX(5px);
      }

      &:last-child {
        border-bottom: none !important;
      }

      // Styles pour les avis de test
      &.test-data-item {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-left: 4px solid #ffc107;

        &:hover {
          background: linear-gradient(135deg, #fff3cd 0%, #ffe082 100%);
        }
      }

      .test-data-indicator {
        position: absolute;
        top: 0.5rem;
        right: 1rem;
        background: linear-gradient(135deg, #ffc107, #ffb300);
        color: #000;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        z-index: 10;

        i {
          font-size: 1rem;
        }

        .btn {
          padding: 0.125rem 0.375rem;
          font-size: 0.75rem;
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
          }
        }
      }

      .form-check-input {
        margin-top: 0.125rem;
        width: 1.2rem;
        height: 1.2rem;
        border: 2px solid #dee2e6;
        border-radius: 4px;

        &:checked {
          background-color: $primary-color;
          border-color: $primary-color;
        }

        &:focus {
          box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
        }
      }

      .product-info {
        h6 {
          color: $primary-color;
          font-weight: 700;
          margin-bottom: 0.5rem;
          font-size: 1.1rem;
        }

        .product-reference {
          background: rgba($primary-color, 0.1);
          color: $primary-color;
          padding: 0.25rem 0.75rem;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 600;
          display: inline-block;
        }
      }

      .client-info {
        padding: 1rem;
        background: rgba(#f8f9fa, 0.5);
        border-radius: 12px;
        margin-top: 1rem;

        .client-name {
          font-weight: 700;
          color: $text-color;
          margin-bottom: 0.25rem;
        }

        .client-date {
          color: #6c757d;
          font-size: 0.9rem;
        }
      }

      .rating {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        margin: 0.5rem 0;

        .stars {
          display: flex;
          gap: 2px;
        }

        .bi-star-fill {
          color: #ffc107 !important;
          font-size: 1.1rem;
          filter: drop-shadow(0 1px 2px rgba(255, 193, 7, 0.3));
        }

        .bi-star {
          color: #e0e0e0 !important;
          font-size: 1.1rem;
        }

        .rating-value {
          margin-left: 0.5rem;
          font-weight: 700;
          color: $primary-color;
          background: rgba($primary-color, 0.1);
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          font-size: 0.9rem;
        }
      }

      .avis-comment {
        background: #f8f9fa;
        border-left: 4px solid $primary-color;
        padding: 1rem;
        border-radius: 0 8px 8px 0;
        margin: 1rem 0;
        font-style: italic;
        color: #495057;
        line-height: 1.6;

        &.comment-supprime {
          background: #fff3cd;
          border-left-color: #ffc107;
          color: #856404;
        }
      }

      .badge {
        font-size: 0.8rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        i {
          margin-right: 0.5rem;
        }

        &.bg-warning {
          background: linear-gradient(135deg, #ffc107, #ffb300) !important;
          color: #000;
          box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
        }

        &.bg-success {
          background: linear-gradient(135deg, #28a745, #20c997) !important;
          box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        &.bg-danger {
          background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
          box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        }

        &.bg-info {
          background: linear-gradient(135deg, #17a2b8, #20c997) !important;
          box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
        }
      }

      .btn-group {
        .btn {
          border-radius: 8px;
          font-weight: 600;
          padding: 0.5rem 1rem;
          transition: all 0.3s ease;
          border: 2px solid;

          &.btn-outline-primary {
            border-color: $primary-color;
            color: $primary-color;

            &:hover {
              background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -10%));
              border-color: $primary-color;
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba($primary-color, 0.3);
            }
          }

          &.btn-outline-warning {
            border-color: #ffc107;
            color: #ffc107;

            &:hover {
              background: linear-gradient(135deg, #ffc107, #ffb300);
              border-color: #ffc107;
              color: #000;
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
            }
          }

          &.btn-outline-success {
            border-color: #28a745;
            color: #28a745;

            &:hover {
              background: linear-gradient(135deg, #28a745, #20c997);
              border-color: #28a745;
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            }
          }

          &.btn-outline-info {
            border-color: #17a2b8;
            color: #17a2b8;

            &:hover {
              background: linear-gradient(135deg, #17a2b8, #20c997);
              border-color: #17a2b8;
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
            }
          }
        }
      }
    }
  }
  
  .pagination-section {
    margin: 2rem 1.5rem;

    .pagination-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: white;
      border-radius: 16px;
      padding: 1.5rem 2rem;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      border-left: 4px solid $primary-color;

      .pagination-info {
        .pagination-text {
          color: #6c757d;
          font-weight: 500;

          i {
            color: $primary-color;
          }

          strong {
            color: $primary-color;
          }
        }
      }

      .pagination-nav {
        .pagination {
          margin: 0;

          .page-link {
            color: $primary-color;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            margin: 0 0.25rem;
            padding: 0.75rem 1.25rem;
            font-weight: 600;
            transition: all 0.3s ease;

            &:hover:not(:disabled) {
              background: linear-gradient(135deg, #f8f9fa, #e9ecef);
              border-color: $primary-color;
              transform: translateY(-2px);
              box-shadow: 0 4px 15px rgba($primary-color, 0.2);
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }

          .page-item.active .page-link {
            background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -10%));
            border-color: $primary-color;
            color: white;
            box-shadow: 0 4px 15px rgba($primary-color, 0.3);

            &.current-page {
              cursor: default;
            }
          }

          .page-item.disabled .page-link {
            color: #6c757d;
            background-color: #fff;
            border-color: #e9ecef;
            opacity: 0.5;
          }
        }
      }

      .pagination-actions {
        .btn {
          border-radius: 12px;
          font-weight: 600;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba($primary-color, 0.2);
          }
        }
      }
    }
  }

  .alert {
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin: 1rem 1.5rem;

    &.alert-danger {
      background: linear-gradient(135deg, #f8d7da, #f5c6cb);
      color: #721c24;
      border-left: 4px solid #dc3545;

      i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
      }
    }

    &.alert-success {
      background: linear-gradient(135deg, #d4edda, #c3e6cb);
      color: #155724;
      border-left: 4px solid #28a745;

      i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
      }
    }

    &.alert-warning {
      background: linear-gradient(135deg, #fff3cd, #ffeaa7);
      color: #856404;
      border-left: 4px solid #ffc107;

      i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
      }
    }

    // Styles spécifiques pour l'alerte de données de test
    &.test-data-alert {
      border: 2px solid #ffc107;
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(255, 193, 7, 0.2);
      margin: 1rem 1.5rem;

      .alert-heading {
        color: #856404;
        font-weight: 700;

        i {
          background: rgba(255, 193, 7, 0.2);
          padding: 0.5rem;
          border-radius: 50%;
        }
      }

      .test-data-actions {
        margin-top: 1rem;

        .btn {
          border-radius: 8px;
          font-weight: 600;
          transition: all 0.3s ease;

          &.btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: none;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
            }
          }

          &.btn-outline-secondary {
            border: 2px solid #6c757d;

            &:hover {
              background: #6c757d;
              transform: translateY(-2px);
            }
          }
        }
      }
    }
  }

  // Styles pour les modals
  .modal-content {
    border: none;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);

    .modal-header {
      background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -10%));
      color: white;
      border-radius: 16px 16px 0 0;
      padding: 1.5rem 2rem;

      .modal-title {
        font-weight: 700;
        font-size: 1.3rem;

        i {
          margin-right: 0.75rem;
          background: rgba(255,255,255,0.2);
          padding: 0.5rem;
          border-radius: 50%;
        }
      }

      .btn-close {
        filter: invert(1);
        opacity: 0.8;

        &:hover {
          opacity: 1;
        }
      }
    }

    .modal-body {
      padding: 2rem;
    }

    .modal-footer {
      padding: 1.5rem 2rem;
      border-top: 1px solid #e9ecef;

      .btn {
        border-radius: 8px;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;

        &.btn-primary {
          background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -10%));
          border: none;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba($primary-color, 0.3);
          }
        }

        &.btn-secondary {
          background: #6c757d;
          border: none;

          &:hover {
            background: #5a6268;
            transform: translateY(-2px);
          }
        }

        &.btn-warning {
          background: linear-gradient(135deg, #ffc107, #ffb300);
          border: none;
          color: #000;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
          }
        }
      }
    }
  }
  
  // Styles pour les états de chargement
  .loading-spinner {
    margin: 3rem 1.5rem;
    text-align: center;

    .spinner-border {
      width: 3rem;
      height: 3rem;
      border-width: 0.3em;
      color: $primary-color;
    }

    .loading-text {
      margin-top: 1rem;
      color: #6c757d;
      font-weight: 500;
    }
  }

  // Styles pour l'état vide
  .empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;

    i {
      font-size: 4rem;
      opacity: 0.5;
      margin-bottom: 1rem;
      display: block;
      color: $primary-color;
    }

    p {
      font-size: 1.1rem;
      margin: 0;
    }
  }

  // Animations
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  .avis-item {
    animation: fadeInUp 0.3s ease-out;
  }

  .stat-card:hover {
    animation: pulse 0.6s ease-in-out;
  }

  // Styles pour la barre d'actions en lot
  .batch-actions-bar {
    margin: 2rem 1.5rem;
    border: none;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border-left: 4px solid $primary-color;

    .card-body {
      padding: 2rem;
    }

    h6 {
      color: $primary-color;
      font-weight: 700;

      i {
        margin-right: 0.5rem;
      }
    }

    .form-label {
      color: $primary-color;
      font-size: 0.85rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: 600;
    }

    .form-select,
    .form-control {
      border: 2px solid #e9ecef;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.15);
      }
    }

    .btn {
      border-radius: 8px;
      font-weight: 600;
      padding: 0.75rem 1.5rem;
      transition: all 0.3s ease;

      &.btn-primary {
        background: linear-gradient(135deg, $primary-color, color.adjust($primary-color, $lightness: -10%));
        border: none;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba($primary-color, 0.3);
        }
      }

      &.btn-outline-secondary {
        border: 2px solid #6c757d;

        &:hover {
          background: #6c757d;
          transform: translateY(-2px);
        }
      }
    }
  }

  // Styles pour la timeline d'historique
  .moderation-timeline {
    .timeline {
      position: relative;
      padding-left: 2rem;

      &::before {
        content: '';
        position: absolute;
        left: 1rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, $primary-color, #e9ecef);
      }
    }

    .timeline-item {
      position: relative;
      margin-bottom: 2rem;

      &:last-child {
        margin-bottom: 0;
      }

      .timeline-marker {
        position: absolute;
        left: -2rem;
        top: 0;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);

        i {
          font-size: 0.875rem;
        }
      }

      .timeline-content {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        border-left: 3px solid $primary-color;

        .timeline-header {
          display: flex;
          justify-content: between;
          align-items: center;
          margin-bottom: 0.5rem;

          h6 {
            color: $text-color;
            font-weight: 600;
            margin: 0;
          }

          small {
            color: #6c757d;
            font-weight: 500;
          }
        }

        p {
          margin-bottom: 0.5rem;
          color: $text-color;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .badge {
          font-size: 0.75rem;
        }
      }
    }
  }

// Modal styles
.modal {
  .modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  }
  
  .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    
    .modal-title {
      color: $text-color;
      font-weight: 600;
      
      i {
        margin-right: 0.5rem;
        color: $primary-color;
      }
    }
  }
  
  .modal-body {
    padding: 1.5rem;
    
    .avis-details {
      h6 {
        color: $text-color;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }
      
      .rating {
        .bi-star-fill {
          color: #ffc107 !important;
        }
        
        .bi-star {
          color: #e0e0e0 !important;
        }
      }
      
      .bg-light {
        background-color: #f8f9fa !important;
      }
    }
    
    .form-label {
      font-weight: 500;
      color: $text-color;
    }
    
    .form-select,
    .form-control {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
  }
  
  .modal-footer {
    border-top: 1px solid #e0e0e0;
    padding: 1rem 1.5rem;
    
    .btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.btn-primary {
        background-color: $primary-color;
        border-color: $primary-color;
        
        &:hover {
          background-color: color.adjust($primary-color, $lightness: -10%);
          border-color: color.adjust($primary-color, $lightness: -10%);
        }
      }
    }
  }
}

// Responsive design amélioré
@media (max-width: 768px) {
  .avis-moderation-container {
    .header-section {
      padding: 1.5rem;
      margin: -1.5rem -1.5rem 1.5rem -1.5rem;

      h2 {
        font-size: 1.8rem;
      }
    }

    .stats-dashboard-inline {
      margin-left: 0.5rem;
      margin-right: 0.5rem;

      .stats-container {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;

        .stat-item {
          justify-content: center;
          text-align: center;

          .stat-icon {
            width: 40px;
            height: 40px;
            font-size: 1.2rem;
          }

          .stat-content {
            align-items: center;

            .stat-number {
              font-size: 1.5rem;
            }

            .stat-label {
              font-size: 0.75rem;
            }
          }
        }

        .stat-separator {
          width: 80%;
          height: 1px;
          background: linear-gradient(to right, transparent, #e9ecef, transparent);
          margin: 0;
        }
      }
    }

    .filters-section,
    .avis-list,
    .batch-actions-bar,
    .pagination-section {
      margin-left: 0.5rem;
      margin-right: 0.5rem;
    }

    .filters-section .card-body {
      padding: 1.5rem 1rem;
    }

    .avis-item {
      padding: 1rem;

      .row {
        flex-direction: column;
      }

      .col-lg-3 {
        text-align: left !important;
        margin-top: 1rem;
      }

      .btn-group {
        .btn {
          padding: 0.5rem;

          span {
            display: none !important;
          }
        }
      }
    }

    .batch-actions-bar {
      .pagination-container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
      }
    }

    .pagination-section {
      .pagination-container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;

        .pagination-nav {
          order: 2;
        }

        .pagination-info {
          order: 1;
        }

        .pagination-actions {
          order: 3;
        }
      }
    }
  }

  .modal-dialog {
    margin: 1rem;
  }
}

// Responsive pour tablettes
@media (max-width: 992px) and (min-width: 769px) {
  .avis-moderation-container {
    .stats-dashboard-inline {
      .stats-container {
        .stat-item {
          .stat-content {
            .stat-number {
              font-size: 1.6rem;
            }

            .stat-label {
              font-size: 0.8rem;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .avis-moderation-container {
    .stats-dashboard-inline {
      .stats-container {
        .stat-item {
          .stat-content {
            .stat-number {
              font-size: 1.25rem;
            }

            .stat-label {
              font-size: 0.7rem;
            }
          }
        }
      }
    }

    .filters-section {
      .row {
        .col-lg-2,
        .col-lg-3 {
          min-width: 100%;
          margin-bottom: 1rem;
        }
      }
    }
  }
}

// Styles pour les modales inline
.inline-modals {
  margin-top: 1rem;
  padding: 0 1rem;

  .inline-modal {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    margin-bottom: 1rem;
    animation: slideDown 0.3s ease-out;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $primary-color, $accent-color);
    }

    .modal-header {
      padding: 1.5rem 2rem 1rem 2rem;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);

      .modal-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 700;
        color: $primary-color;
        display: flex;
        align-items: center;

        i {
          background: rgba($primary-color, 0.1);
          padding: 0.5rem;
          border-radius: 50%;
          margin-right: 0.75rem;
        }
      }

      .btn-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        opacity: 0.6;
        transition: all 0.3s ease;
        padding: 0.5rem;
        border-radius: 50%;

        &:hover {
          opacity: 1;
          background: rgba(220, 53, 69, 0.1);
          transform: scale(1.1);
        }
      }
    }

    .modal-body {
      padding: 2rem;

      .alert {
        border-radius: 12px;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;

        &.alert-warning {
          background: linear-gradient(135deg, #fff3cd, #ffeaa7);
          color: #856404;
          border-left: 4px solid #ffc107;
        }
      }

      .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;

        i {
          margin-right: 0.5rem;
          color: $primary-color;
        }
      }

      .form-select,
      .form-control {
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;

        &:focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
        }
      }

      .btn {
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;

        &.btn-primary {
          background: linear-gradient(135deg, $primary-color, $accent-color);
          border: none;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba($primary-color, 0.3);
          }
        }

        &.btn-secondary {
          background: #6c757d;
          border: none;

          &:hover {
            background: #5a6268;
            transform: translateY(-2px);
          }
        }

        &.btn-warning {
          background: linear-gradient(135deg, #ffc107, #ffb300);
          border: none;
          color: #000;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
          }
        }
      }
    }

    // Styles spécifiques pour chaque type de modal
    &.moderation-modal {
      border-color: $primary-color;

      &::before {
        background: linear-gradient(90deg, $primary-color, $accent-color);
      }
    }

    &.suppression-modal {
      border-color: #ffc107;

      &::before {
        background: linear-gradient(90deg, #ffc107, #ffb300);
      }
    }

    &.history-modal {
      border-color: #17a2b8;

      &::before {
        background: linear-gradient(90deg, #17a2b8, #138496);
      }
    }
  }
}

// Animation pour les modales inline
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Styles pour les nouvelles cartes d'avis modernes
.avis-card {
  background: white;
  border-radius: $border-radius;
  box-shadow: $shadow-light;
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: $transition;
  border: 1px solid rgba(0, 0, 0, 0.05);

  &:hover {
    transform: translateY(-3px);
    box-shadow: $shadow-medium;
  }

  .avis-card-content {
    display: flex;
    min-height: 200px;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .avis-main-content {
    flex: 1;
    padding: 1.5rem;
  }

  .avis-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }

    .avis-info {
      flex: 1;

      .product-name {
        font-size: 1.25rem;
        font-weight: 600;
        color: $primary-color;
        margin-bottom: 0.5rem;
      }

      .client-info {
        color: #6c757d;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .date-separator {
          margin: 0 0.25rem;
        }

        .bi {
          color: $primary-color;
        }
      }
    }

    .avis-rating-status {
      text-align: right;

      @media (max-width: 768px) {
        text-align: left;
      }

      .rating-display {
        margin-bottom: 0.75rem;

        .stars {
          display: flex;
          gap: 0.125rem;
          margin-bottom: 0.5rem;

          .star {
            font-size: 1.1rem;
            color: #ddd;
            transition: $transition;

            &.filled {
              color: #ffc107;
            }
          }
        }

        .rating-number {
          font-weight: 600;
          color: #495057;
          font-size: 0.9rem;
        }
      }

      .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.375rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        .bi {
          font-size: 0.6rem;
        }

        &.badge-success {
          background: linear-gradient(135deg, #d4edda, #c3e6cb);
          color: #155724;
        }

        &.badge-warning {
          background: linear-gradient(135deg, #fff3cd, #ffeaa7);
          color: #856404;
        }

        &.badge-danger {
          background: linear-gradient(135deg, #f8d7da, #f5c6cb);
          color: #721c24;
        }

        &.badge-secondary {
          background: linear-gradient(135deg, #e2e3e5, #d6d8db);
          color: #383d41;
        }
      }
    }
  }

  .avis-comment {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 3px solid $primary-color;

    .comment-content {
      display: flex;
      align-items: flex-start;
      gap: 0.5rem;
      font-style: italic;
      line-height: 1.6;

      .bi {
        color: $primary-color;
        margin-top: 0.125rem;
        flex-shrink: 0;
      }
    }

    .comment-deleted {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #6c757d;
      font-style: italic;

      .bi {
        color: #dc3545;
      }
    }
  }

  .avis-actions {
    background: #f8f9fa;
    padding: 1.5rem;
    border-left: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    justify-content: center;
    min-width: 200px;

    @media (max-width: 768px) {
      border-left: none;
      border-top: 1px solid #dee2e6;
      flex-direction: row;
      min-width: auto;
    }

    .action-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      border-radius: 8px;
      border: 2px solid;
      background: transparent;
      font-weight: 600;
      transition: $transition;
      cursor: pointer;
      text-decoration: none;
      justify-content: center;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &.danger-btn {
        border-color: #dc3545;
        color: #dc3545;

        &:hover {
          background: #dc3545;
          color: white;
        }
      }

      &.info-btn {
        border-color: #17a2b8;
        color: #17a2b8;

        &:hover {
          background: #17a2b8;
          color: white;
        }
      }

      .bi {
        font-size: 1rem;
      }
    }
  }
}
