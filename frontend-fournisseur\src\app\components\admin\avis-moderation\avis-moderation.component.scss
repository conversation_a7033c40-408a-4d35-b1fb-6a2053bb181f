// Styles élégants pour la modération des avis
@use 'sass:color';
@import '../../../../styles.scss';

// Variables de couleurs et styles élégants
$gradient-primary: linear-gradient(135deg, #{$primary-color} 0%, #{color.adjust($primary-color, $lightness: 15%)} 50%, #{color.adjust($primary-color, $lightness: -5%)} 100%);
$gradient-elegant: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
$gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-danger: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
$gradient-background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

$shadow-elegant: 0 10px 40px rgba(0, 0, 0, 0.1);
$shadow-light: 0 4px 20px rgba(0, 0, 0, 0.08);
$shadow-medium: 0 8px 30px rgba(0, 0, 0, 0.12);
$shadow-heavy: 0 15px 50px rgba(0, 0, 0, 0.15);
$shadow-floating: 0 20px 60px rgba(0, 0, 0, 0.1);

$border-radius: 16px;
$border-radius-large: 24px;
$transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
$transition-fast: all 0.2s ease-out;

// Container principal élégant
.container-fluid {
  background: $gradient-background;
  min-height: 100vh;
  padding: 2rem;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 300px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    z-index: 0;
  }
  
  > * {
    position: relative;
    z-index: 1;
  }

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

// En-tête élégant et sophistiqué
.page-header {
  background: $gradient-elegant;
  border-radius: $border-radius-large;
  padding: 3rem 2.5rem;
  margin-bottom: 3rem;
  box-shadow: $shadow-floating;
  color: white;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -10%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
    border-radius: 50%;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 2rem;
      text-align: center;
    }
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 1.5rem;

    .title-icon {
      background: rgba(255, 255, 255, 0.2);
      backdrop-filter: blur(10px);
      border-radius: 50%;
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .title-text {
      .page-title {
        font-size: 3rem;
        font-weight: 800;
        margin: 0;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        background: linear-gradient(45deg, #fff, rgba(255, 255, 255, 0.8));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        @media (max-width: 768px) {
          font-size: 2.2rem;
        }
      }

      .page-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
        font-weight: 300;
        letter-spacing: 0.5px;
      }
    }
  }

  .header-actions {
    .btn {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 2px solid rgba(255, 255, 255, 0.2);
      color: white;
      border-radius: 12px;
      padding: 1rem 2rem;
      font-weight: 600;
      font-size: 1rem;
      transition: $transition;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

      &:hover {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(-1px);
      }
    }
  }
}

// Statistiques élégantes avec glassmorphism
.stats-container {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 3rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 2px;
    
    &:hover {
      background: rgba(102, 126, 234, 0.5);
    }
  }
}

.stats-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: $border-radius;
  padding: 2rem 1.5rem;
  box-shadow: $shadow-elegant;
  transition: $transition;
  flex: 1;
  min-width: 220px;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, transparent, currentColor, transparent);
    opacity: 0.6;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: $shadow-floating;
    background: rgba(255, 255, 255, 0.35);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      inset: -2px;
      border-radius: 50%;
      padding: 2px;
      background: linear-gradient(45deg, currentColor, transparent, currentColor);
      mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
      mask-composite: exclude;
      opacity: 0.3;
    }
  }

  .stats-content {
    flex: 1;

    .stats-number {
      font-size: 2.5rem;
      font-weight: 800;
      line-height: 1;
      margin-bottom: 0.5rem;
      background: linear-gradient(45deg, currentColor, color.adjust(currentColor, $lightness: 20%));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .stats-label {
      font-size: 1rem;
      color: rgba(0, 0, 0, 0.7);
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  }

  &.stats-primary {
    color: #667eea;
    .stats-icon { background: $gradient-primary; }
  }

  &.stats-success {
    color: #11998e;
    .stats-icon { background: $gradient-success; }
  }

  &.stats-warning {
    color: #f093fb;
    .stats-icon { background: $gradient-warning; }
  }

  &.stats-danger {
    color: #fc466b;
    .stats-icon { background: $gradient-danger; }
  }
}

// Filtres élégants
.card {
  border: none;
  border-radius: $border-radius;
  box-shadow: $shadow-elegant;
  transition: $transition;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);

  &:hover {
    box-shadow: $shadow-floating;
    transform: translateY(-2px);
  }

  .card-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: $border-radius $border-radius 0 0;
    padding: 1.5rem;

    h5 {
      color: #667eea;
      font-weight: 700;
      margin: 0;
      font-size: 1.3rem;

      .bi {
        margin-right: 0.75rem;
        background: rgba(102, 126, 234, 0.2);
        padding: 0.5rem;
        border-radius: 50%;
        font-size: 1rem;
      }
    }
  }

  .card-body {
    padding: 2rem;

    .form-label {
      font-weight: 600;
      color: #333;
      margin-bottom: 0.75rem;
      font-size: 1rem;

      .bi {
        color: #667eea;
        margin-right: 0.5rem;
      }
    }

    .form-control, .form-select {
      border: 2px solid rgba(102, 126, 234, 0.2);
      border-radius: 12px;
      padding: 0.875rem 1.25rem;
      transition: $transition;
      font-size: 1rem;
      background: rgba(255, 255, 255, 0.8);

      &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
      }

      &::placeholder {
        color: rgba(0, 0, 0, 0.5);
      }
    }

    .btn {
      border-radius: 12px;
      padding: 0.875rem 1.5rem;
      font-weight: 600;
      transition: $transition;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      &.btn-outline-secondary {
        border-color: #667eea;
        color: #667eea;

        &:hover {
          background: $gradient-elegant;
          border-color: transparent;
          color: white;
        }
      }
    }
  }
}

// Cartes d'avis élégantes avec design moderne
.avis-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: $border-radius;
  box-shadow: $shadow-elegant;
  margin-bottom: 2rem;
  overflow: hidden;
  transition: $transition;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: $gradient-elegant;
    opacity: 0.8;
  }

  &:hover {
    transform: translateY(-10px);
    box-shadow: $shadow-floating;
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .avis-card-content {
    display: flex;
    min-height: 220px;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  .avis-main-content {
    flex: 1;
    padding: 2rem;
  }

  .avis-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }

    .avis-info {
      flex: 1;

      .product-name {
        font-size: 1.4rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.75rem;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .client-info {
        color: rgba(0, 0, 0, 0.6);
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 500;

        .date-separator {
          margin: 0 0.25rem;
          color: #667eea;
        }

        .bi {
          color: #667eea;
          font-size: 1.1rem;
        }
      }
    }

    .avis-rating-status {
      text-align: right;

      @media (max-width: 768px) {
        text-align: left;
      }

      .rating-display {
        margin-bottom: 1rem;

        .stars {
          display: flex;
          gap: 0.25rem;
          margin-bottom: 0.75rem;
          justify-content: flex-end;

          @media (max-width: 768px) {
            justify-content: flex-start;
          }

          .star {
            font-size: 1.3rem;
            color: #e0e0e0;
            transition: $transition-fast;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));

            &.filled {
              color: #ffd700;
              text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
            }
          }
        }

        .rating-number {
          font-weight: 700;
          color: #333;
          font-size: 1.1rem;
          background: linear-gradient(45deg, #ffd700, #ffed4e);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 2px solid transparent;

        .bi {
          font-size: 0.7rem;
          margin-right: 0.5rem;
        }

        &.badge-success {
          background: $gradient-success;
          color: white;
          border-color: rgba(17, 153, 142, 0.3);
        }

        &.badge-warning {
          background: $gradient-warning;
          color: white;
          border-color: rgba(240, 147, 251, 0.3);
        }

        &.badge-danger {
          background: $gradient-danger;
          color: white;
          border-color: rgba(252, 70, 107, 0.3);
        }

        &.badge-secondary {
          background: linear-gradient(135deg, #6c757d, #495057);
          color: white;
          border-color: rgba(108, 117, 125, 0.3);
        }
      }
    }
  }

  .avis-comment {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #667eea;
    position: relative;

    .comment-content {
      display: flex;
      align-items: flex-start;
      gap: 1rem;
      font-style: italic;
      line-height: 1.7;
      font-size: 1.05rem;
      color: rgba(0, 0, 0, 0.8);

      .bi {
        color: #667eea;
        margin-top: 0.25rem;
        flex-shrink: 0;
        font-size: 1.2rem;
      }
    }

    .comment-deleted {
      display: flex;
      align-items: center;
      gap: 1rem;
      color: rgba(252, 70, 107, 0.8);
      font-style: italic;
      font-weight: 600;

      .bi {
        color: #fc466b;
        font-size: 1.2rem;
      }
    }
  }

  .avis-actions {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-left: 1px solid rgba(102, 126, 234, 0.2);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
    min-width: 220px;

    @media (max-width: 768px) {
      border-left: none;
      border-top: 1px solid rgba(102, 126, 234, 0.2);
      flex-direction: row;
      min-width: auto;
    }

    .action-btn {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 1.5rem;
      border-radius: 12px;
      border: 2px solid;
      background: transparent;
      font-weight: 700;
      font-size: 0.95rem;
      transition: $transition;
      cursor: pointer;
      text-decoration: none;
      justify-content: center;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:hover::before {
        left: 100%;
      }

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      &.danger-btn {
        border-color: #fc466b;
        color: #fc466b;

        &:hover {
          background: $gradient-danger;
          color: white;
          border-color: transparent;
        }
      }

      &.info-btn {
        border-color: #667eea;
        color: #667eea;

        &:hover {
          background: $gradient-elegant;
          color: white;
          border-color: transparent;
        }
      }

      .bi {
        font-size: 1.1rem;
      }
    }
  }
}

// Modales inline élégantes
.inline-modal {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: $border-radius;
  box-shadow: $shadow-floating;
  margin-top: 1.5rem;
  overflow: hidden;
  animation: slideDownElegant 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);

  .modal-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    padding: 2rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;

    .modal-title {
      font-weight: 700;
      color: #667eea;
      margin: 0;
      display: flex;
      align-items: center;
      font-size: 1.3rem;

      .bi {
        margin-right: 0.75rem;
        background: rgba(102, 126, 234, 0.2);
        padding: 0.5rem;
        border-radius: 50%;
        font-size: 1rem;
      }
    }

    .btn-close {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #667eea;
      transition: $transition;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(102, 126, 234, 0.1);
        transform: scale(1.1);
      }
    }
  }

  .modal-body {
    padding: 2rem;

    .alert {
      border-radius: 12px;
      border: none;
      padding: 1.5rem;
      margin-bottom: 1.5rem;

      &.alert-info {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        color: #667eea;
        border-left: 4px solid #667eea;

        .bi {
          color: #667eea;
          margin-right: 0.75rem;
        }
      }

      ul {
        margin-bottom: 0;
        padding-left: 1.5rem;
      }
    }

    .form-label {
      font-weight: 700;
      color: #333;
      margin-bottom: 0.75rem;
      display: flex;
      align-items: center;
      font-size: 1.1rem;

      .bi {
        margin-right: 0.75rem;
        color: #667eea;
        background: rgba(102, 126, 234, 0.1);
        padding: 0.5rem;
        border-radius: 50%;
        font-size: 1rem;
      }

      .text-danger {
        margin-left: 0.5rem;
        color: #fc466b;
      }
    }

    .form-control {
      border: 2px solid rgba(102, 126, 234, 0.2);
      border-radius: 12px;
      padding: 1rem 1.25rem;
      transition: $transition;
      font-size: 1rem;
      background: rgba(255, 255, 255, 0.8);
      min-height: 120px;

      &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
      }

      &::placeholder {
        color: rgba(0, 0, 0, 0.5);
        font-style: italic;
      }
    }

    .btn {
      border-radius: 12px;
      padding: 1rem 2rem;
      font-weight: 700;
      font-size: 1rem;
      transition: $transition;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      &.btn-secondary {
        background: linear-gradient(135deg, #6c757d, #495057);
        border: none;
        color: white;

        &:hover {
          background: linear-gradient(135deg, #495057, #343a40);
        }
      }

      &.btn-danger {
        background: $gradient-danger;
        border: none;
        color: white;

        &:hover {
          background: linear-gradient(135deg, #3f5efb, #fc466b);
        }
      }

      .bi {
        font-size: 1.1rem;
      }
    }

    .d-flex.gap-2 {
      gap: 1rem !important;
      justify-content: flex-end;
      margin-top: 2rem;
    }
  }

  &.suppression-modal {
    border-left: 4px solid #fc466b;
  }

  &.history-modal {
    border-left: 4px solid #667eea;
  }
}

// Timeline élégante pour l'historique
.timeline {
  position: relative;
  padding-left: 2.5rem;

  &::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: $gradient-elegant;
    border-radius: 2px;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 2rem;

    .timeline-marker {
      position: absolute;
      left: -25px;
      top: 8px;
      width: 35px;
      height: 35px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 0.9rem;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      border: 3px solid white;

      &.bg-primary {
        background: $gradient-elegant;
      }

      &.bg-warning {
        background: $gradient-warning;
      }
    }

    .timeline-content {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      padding: 1.5rem;
      border-radius: 12px;
      border-left: 4px solid #667eea;
      box-shadow: $shadow-light;

      h6 {
        margin-bottom: 0.5rem;
        font-weight: 700;
        color: #667eea;
        font-size: 1.1rem;
      }

      small {
        color: rgba(0, 0, 0, 0.6);
        font-size: 0.9rem;
        font-weight: 500;
      }

      p {
        margin: 0.75rem 0 0 0;
        line-height: 1.6;
        color: rgba(0, 0, 0, 0.8);
      }
    }
  }
}

// Animations élégantes
@keyframes slideDownElegant {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// États spéciaux élégants
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 4rem;

  .spinner-border {
    width: 4rem;
    height: 4rem;
    border-width: 4px;
    color: #667eea;
  }
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: rgba(0, 0, 0, 0.6);

  .bi {
    font-size: 5rem;
    margin-bottom: 1.5rem;
    opacity: 0.3;
    color: #667eea;
  }

  h4 {
    margin-bottom: 0.75rem;
    font-weight: 700;
    color: #333;
    font-size: 1.5rem;
  }

  p {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

.alert {
  border-radius: $border-radius;
  border: none;
  padding: 1.5rem;

  &.alert-danger {
    background: linear-gradient(135deg, rgba(252, 70, 107, 0.1), rgba(63, 94, 251, 0.1));
    color: #fc466b;
    border-left: 4px solid #fc466b;

    .bi {
      color: #fc466b;
      margin-right: 0.75rem;
    }
  }

  .btn-close {
    background: none;
    border: none;
    opacity: 0.7;
    transition: $transition;
    width: 30px;
    height: 30px;
    border-radius: 50%;

    &:hover {
      opacity: 1;
      background: rgba(0, 0, 0, 0.05);
    }
  }
}

// Responsive élégant
@media (max-width: 576px) {
  .container-fluid {
    padding: 1rem 0.75rem;
  }

  .page-header {
    padding: 2rem 1.5rem;
    margin-bottom: 2rem;

    .title-text .page-title {
      font-size: 2rem;
    }

    .title-icon {
      width: 60px;
      height: 60px;
      font-size: 1.5rem;
    }
  }

  .stats-container {
    margin-bottom: 2rem;
  }

  .stats-card {
    padding: 1.5rem 1rem;
    min-width: auto;

    .stats-icon {
      width: 50px;
      height: 50px;
      font-size: 1.5rem;
    }

    .stats-content .stats-number {
      font-size: 2rem;
    }
  }

  .avis-card {
    margin-bottom: 1.5rem;

    .avis-main-content,
    .avis-actions {
      padding: 1.5rem;
    }

    .avis-actions {
      gap: 0.75rem;

      .action-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
      }
    }
  }

  .inline-modal {
    .modal-header,
    .modal-body {
      padding: 1.5rem;
    }
  }
}
