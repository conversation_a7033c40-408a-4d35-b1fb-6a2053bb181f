@import '../../../../styles.scss';

.avis-moderation-container {
  padding: 1.5rem;
  
  .header-section {
    margin-bottom: 2rem;

    h2 {
      color: $primary-color;
      font-weight: 600;
      margin-bottom: 0.5rem;

      i {
        margin-right: 0.5rem;
      }
    }
  }

  // Styles pour le tableau de bord des statistiques
  .stats-dashboard {
    .stat-card {
      border: none;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      border-radius: 8px;
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
      }

      .card-body {
        padding: 1.5rem 1rem;
      }

      .stat-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;

        i {
          opacity: 0.8;
        }
      }

      .stat-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
        color: $text-color;
      }

      .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0;
        font-weight: 500;
      }
    }
  }
  
  .filters-section {
    margin-bottom: 1.5rem;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    
    .card-body {
      padding: 1.5rem;
    }
    
    .form-label {
      font-weight: 500;
      color: $text-color;
      margin-bottom: 0.5rem;
    }
    
    .form-select,
    .form-control {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
  }
  
  .avis-list {
    .card {
      border: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 8px;
      
      .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        padding: 1rem 1.5rem;
        
        h5 {
          color: $text-color;
          font-weight: 600;
        }
        
        .btn-group {
          .btn {
            border-color: #e0e0e0;
            
            &.active {
              background-color: $primary-color;
              border-color: $primary-color;
              color: white;
            }
            
            &:hover:not(.active) {
              background-color: #f8f9fa;
            }
          }
        }
      }
    }
    
    .avis-item {
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      &:last-child {
        border-bottom: none !important;
      }

      .form-check-input {
        margin-top: 0.125rem;

        &:checked {
          background-color: $primary-color;
          border-color: $primary-color;
        }
      }
      
      .product-info {
        h6 {
          color: $text-color;
          font-weight: 600;
          margin-bottom: 0.25rem;
        }
      }
      
      .client-info {
        padding-top: 0.5rem;
        border-top: 1px solid #f0f0f0;
      }
      
      .rating {
        .bi-star-fill {
          color: #ffc107 !important;
        }
        
        .bi-star {
          color: #e0e0e0 !important;
        }
      }
      
      .badge {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        
        i {
          margin-right: 0.25rem;
        }
        
        &.bg-warning {
          background-color: #ffc107 !important;
          color: #000;
        }
        
        &.bg-success {
          background-color: #28a745 !important;
        }
        
        &.bg-danger {
          background-color: #dc3545 !important;
        }
        
        &.bg-info {
          background-color: #17a2b8 !important;
        }
      }
      
      .btn-outline-primary {
        border-color: $primary-color;
        color: $primary-color;
        
        &:hover {
          background-color: $primary-color;
          border-color: $primary-color;
        }
      }
    }
  }
  
  .pagination-section {
    .pagination {
      .page-link {
        color: $primary-color;
        border-color: #e0e0e0;
        
        &:hover {
          background-color: #f8f9fa;
          border-color: $primary-color;
        }
      }
      
      .page-item.active .page-link {
        background-color: $primary-color;
        border-color: $primary-color;
      }
      
      .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
        border-color: #e0e0e0;
      }
    }
  }
  
  .alert {
    border: none;
    border-radius: 6px;
    
    &.alert-danger {
      background-color: #f8d7da;
      color: #721c24;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }

  // Styles pour la barre d'actions en lot
  .batch-actions-bar {
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);

    .card-body {
      padding: 1rem 1.5rem;
    }

    h6 {
      color: $text-color;
      font-weight: 600;

      i {
        color: $primary-color;
        margin-right: 0.5rem;
      }
    }

    .form-select-sm,
    .form-control-sm {
      border: 1px solid #e0e0e0;
      border-radius: 4px;

      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }

    .btn-sm {
      border-radius: 4px;
      font-weight: 500;

      &.btn-primary {
        background-color: $primary-color;
        border-color: $primary-color;

        &:hover {
          background-color: darken($primary-color, 10%);
          border-color: darken($primary-color, 10%);
        }
      }
    }
  }

  // Styles pour la timeline d'historique
  .moderation-timeline {
    .timeline {
      position: relative;
      padding-left: 2rem;

      &::before {
        content: '';
        position: absolute;
        left: 1rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, $primary-color, #e9ecef);
      }
    }

    .timeline-item {
      position: relative;
      margin-bottom: 2rem;

      &:last-child {
        margin-bottom: 0;
      }

      .timeline-marker {
        position: absolute;
        left: -2rem;
        top: 0;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);

        i {
          font-size: 0.875rem;
        }
      }

      .timeline-content {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        border-left: 3px solid $primary-color;

        .timeline-header {
          display: flex;
          justify-content: between;
          align-items: center;
          margin-bottom: 0.5rem;

          h6 {
            color: $text-color;
            font-weight: 600;
            margin: 0;
          }

          small {
            color: #6c757d;
            font-weight: 500;
          }
        }

        p {
          margin-bottom: 0.5rem;
          color: $text-color;

          &:last-child {
            margin-bottom: 0;
          }
        }

        .badge {
          font-size: 0.75rem;
        }
      }
    }
  }
}

// Modal styles
.modal {
  .modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  }
  
  .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    
    .modal-title {
      color: $text-color;
      font-weight: 600;
      
      i {
        margin-right: 0.5rem;
        color: $primary-color;
      }
    }
  }
  
  .modal-body {
    padding: 1.5rem;
    
    .avis-details {
      h6 {
        color: $text-color;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }
      
      .rating {
        .bi-star-fill {
          color: #ffc107 !important;
        }
        
        .bi-star {
          color: #e0e0e0 !important;
        }
      }
      
      .bg-light {
        background-color: #f8f9fa !important;
      }
    }
    
    .form-label {
      font-weight: 500;
      color: $text-color;
    }
    
    .form-select,
    .form-control {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
      }
    }
  }
  
  .modal-footer {
    border-top: 1px solid #e0e0e0;
    padding: 1rem 1.5rem;
    
    .btn {
      border-radius: 6px;
      font-weight: 500;
      
      &.btn-primary {
        background-color: $primary-color;
        border-color: $primary-color;
        
        &:hover {
          background-color: darken($primary-color, 10%);
          border-color: darken($primary-color, 10%);
        }
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .avis-moderation-container {
    padding: 1rem;
    
    .filters-section .card-body {
      padding: 1rem;
    }
    
    .avis-item {
      .row {
        flex-direction: column;
      }
      
      .col-md-3 {
        text-align: left !important;
        margin-top: 1rem;
      }
    }
  }
  
  .modal-dialog {
    margin: 1rem;
  }
}
