import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap, map } from 'rxjs/operators';
import {
  Commande,
  CommandeFournisseur,
  LigneCommandeFournisseur,
  Livraison,
  StatutCommandeFournisseur,
  StatutLivraison,
  StatutCommande
} from '../models';
import { environment } from '../../environments/environment';

// Interfaces pour les réponses API
export interface CommandeResponse {
  success: boolean;
  data?: Commande;
  message?: string;
}

export interface CommandeListResponse {
  success: boolean;
  data?: Commande[];
  total?: number;
  page?: number;
  pageSize?: number;
  message?: string;
}

export interface CommandeFournisseurResponse {
  success: boolean;
  data?: CommandeFournisseur;
  message?: string;
}

export interface CommandeFournisseurListResponse {
  success: boolean;
  data?: CommandeFournisseur[];
  total?: number;
  page?: number;
  pageSize?: number;
  message?: string;
}

export interface LivraisonResponse {
  success: boolean;
  data?: Livraison;
  message?: string;
}

export interface StatutUpdateRequest {
  statut: StatutCommande | StatutLivraison;
  motif?: string;
  commentaire?: string;
}

export interface UpdateStatutCommandeFournisseurRequest {
  statut: StatutCommandeFournisseur;
  numeroBonLivraison?: string;
}

export interface CommandeFournisseurApiResponse {
  value: CommandeFournisseur[];
  count: number;
}

@Injectable({
  providedIn: 'root'
})
export class CommandeService {
  private readonly API_URL = environment.apiUrl || 'https://localhost:7264/api';

  constructor(private http: HttpClient) {}

  // ===== COMMANDES FOURNISSEUR =====

  /**
   * GET /api/CommandeFournisseur - Obtenir toutes les commandes fournisseur avec filtres
   */
  getCommandesFournisseur(filters?: any): Observable<CommandeFournisseurListResponse> {
    let params = new HttpParams();

    if (filters) {
      Object.keys(filters).forEach(key => {
        if (filters[key] !== null && filters[key] !== undefined) {
          params = params.set(key, filters[key].toString());
        }
      });
    }

    console.log('📋 Récupération des commandes fournisseur avec filtres:', filters);

    return this.http.get<CommandeFournisseurListResponse>(`${this.API_URL}/CommandeFournisseur`, { params })
      .pipe(
        tap(response => console.log('✅ Commandes fournisseur récupérées:', response))
      );
  }

  /**
   * POST /api/CommandeFournisseur - Créer une commande fournisseur
   */
  createCommandeFournisseur(commande: any): Observable<CommandeFournisseurResponse> {
    console.log('➕ Création d\'une nouvelle commande fournisseur:', commande);

    return this.http.post<CommandeFournisseurResponse>(`${this.API_URL}/CommandeFournisseur`, commande)
      .pipe(
        tap(response => console.log('✅ Commande fournisseur créée:', response))
      );
  }

  /**
   * GET /api/CommandeFournisseur/{id} - Obtenir une commande fournisseur par ID
   */
  getCommandeFournisseurById(id: number): Observable<CommandeFournisseurResponse> {
    console.log('🔍 Récupération de la commande fournisseur ID:', id);

    return this.http.get<CommandeFournisseurResponse>(`${this.API_URL}/CommandeFournisseur/${id}`)
      .pipe(
        tap(response => console.log('✅ Commande fournisseur récupérée:', response))
      );
  }

  /**
   * PUT /api/CommandeFournisseur/{id}/statut - Mettre à jour le statut d'une commande fournisseur
   */
  updateStatutCommandeFournisseur(id: number, request: UpdateStatutCommandeFournisseurRequest): Observable<CommandeFournisseurResponse> {
    console.log('🔄 Mise à jour du statut de la commande fournisseur ID:', id, request);

    return this.http.put<CommandeFournisseurResponse>(`${this.API_URL}/CommandeFournisseur/${id}/statut`, request)
      .pipe(
        tap(response => console.log('✅ Statut de la commande fournisseur mis à jour:', response))
      );
  }

  // ===== LIGNES COMMANDE FOURNISSEUR =====

  /**
   * Obtenir toutes les lignes de commande
   */
  getLignesCommandeFournisseur(): Observable<LigneCommandeFournisseur[]> {
    return this.http.get<LigneCommandeFournisseur[]>(`${this.API_URL}/LignesCommandeFournisseur`);
  }

  /**
   * Créer une ligne de commande
   */
  createLigneCommandeFournisseur(ligne: any): Observable<LigneCommandeFournisseur> {
    return this.http.post<LigneCommandeFournisseur>(`${this.API_URL}/LignesCommandeFournisseur`, ligne);
  }

  /**
   * Obtenir une ligne de commande par ID
   */
  getLigneCommandeFournisseurById(id: number): Observable<LigneCommandeFournisseur> {
    return this.http.get<LigneCommandeFournisseur>(`${this.API_URL}/LignesCommandeFournisseur/${id}`);
  }

  /**
   * Mettre à jour une ligne de commande
   */
  updateLigneCommandeFournisseur(id: number, ligne: any): Observable<void> {
    return this.http.put<void>(`${this.API_URL}/LignesCommandeFournisseur/${id}`, ligne);
  }

  /**
   * Supprimer une ligne de commande
   */
  deleteLigneCommandeFournisseur(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/LignesCommandeFournisseur/${id}`);
  }

  // ===== LIVRAISONS =====

  /**
   * Obtenir une livraison par ID
   */
  getLivraisonById(id: number): Observable<Livraison> {
    return this.http.get<Livraison>(`${this.API_URL}/Livraison/${id}`);
  }

  /**
   * Mettre à jour une livraison
   */
  updateLivraison(id: number, livraison: any): Observable<void> {
    return this.http.put<void>(`${this.API_URL}/Livraison/${id}`, livraison);
  }

  /**
   * Obtenir la livraison d'une commande
   */
  getLivraisonByCommande(commandeId: number): Observable<Livraison> {
    return this.http.get<Livraison>(`${this.API_URL}/Livraison/commande/${commandeId}`);
  }

  /**
   * Créer une livraison
   */
  createLivraison(livraison: any): Observable<Livraison> {
    return this.http.post<Livraison>(`${this.API_URL}/Livraison`, livraison);
  }

  /**
   * Mettre à jour le statut d'une livraison
   */
  updateStatutLivraison(livraisonId: number, statut: StatutLivraison): Observable<void> {
    return this.http.post<void>(`${this.API_URL}/Livraison/${livraisonId}/statut`, { statut });
  }

  /**
   * Obtenir les statuts disponibles pour une livraison
   */
  getStatutsDisponibles(livraisonId: number): Observable<StatutLivraison[]> {
    return this.http.get<StatutLivraison[]>(`${this.API_URL}/Livraison/${livraisonId}/statuts-disponibles`);
  }

  /**
   * Obtenir l'étiquette d'une livraison
   */
  getEtiquetteLivraison(livraisonId: number): Observable<Blob> {
    return this.http.get(`${this.API_URL}/Livraison/${livraisonId}/etiquette`, { 
      responseType: 'blob' 
    });
  }

  // ===== MÉTHODES UTILITAIRES =====

  /**
   * Obtenir les commandes d'un fournisseur spécifique
   */
  getCommandesByFournisseur(fournisseurId: number, statut?: StatutCommandeFournisseur): Observable<CommandeFournisseur[]> {
    let params = new HttpParams();
    if (statut) {
      params = params.set('statut', statut);
    }

    console.log('🔍 Appel API getCommandesByFournisseur avec fournisseurId:', fournisseurId);

    return this.http.get<CommandeFournisseurApiResponse>(`${this.API_URL}/CommandeFournisseur`, {
      params: params.set('fournisseurId', fournisseurId.toString())
    }).pipe(
      map(response => {
        console.log('📦 Réponse API brute:', response);
        const commandes = response.value || [];
        console.log('📋 Commandes extraites:', commandes);
        commandes.forEach(commande => {
          console.log(`📦 Commande ${commande.id}: ${commande.lignesCommande?.length || 0} lignes`);
        });
        return commandes;
      }),
      tap(commandes => console.log('✅ Commandes finales retournées:', commandes))
    );
  }

  /**
   * Obtenir les livraisons d'un fournisseur
   */
  getLivraisonsByFournisseur(fournisseurId: number): Observable<Livraison[]> {
    return this.http.get<Livraison[]>(`${this.API_URL}/Livraison`, {
      params: new HttpParams().set('fournisseurId', fournisseurId.toString())
    });
  }

  /**
   * Calculer le total d'une commande
   */
  calculateTotal(lignes: LigneCommandeFournisseur[]): number {
    return lignes.reduce((total, ligne) => total + ligne.montantTotal, 0);
  }

  /**
   * Vérifier si une commande peut être modifiée
   */
  canModifyCommande(statut: StatutCommandeFournisseur): boolean {
    return [StatutCommandeFournisseur.Nouvelle, StatutCommandeFournisseur.Acceptee].includes(statut);
  }

}
