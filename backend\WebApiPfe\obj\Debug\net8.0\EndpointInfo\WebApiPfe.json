{
  "openapi": "3.0.1",
  "info": {
    "title": "MyAPI",
    "version": "v1"
  },
  "paths": {
    "/api/Admin/utilisateurs": {
      "get": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "pageSize",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 10
            }
          },
          {
            "name": "search",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "role",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "estActif",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/utilisateurs/{id}/toggle": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/utilisateurs/{id}": {
      "delete": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/categories": {
      "post": {
        "tags": [
          "Admin"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CategorieGestionDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CategorieGestionDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CategorieGestionDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/categories/{id}": {
      "put": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CategorieGestionDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CategorieGestionDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CategorieGestionDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/categories/{id}/valider": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/categories/{id}/refuser": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/souscategories": {
      "post": {
        "tags": [
          "Admin"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.SousCategorieGestionDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.SousCategorieGestionDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.SousCategorieGestionDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/souscategories/{id}": {
      "put": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.SousCategorieGestionDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.SousCategorieGestionDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.SousCategorieGestionDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/souscategories/{id}/valider": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/souscategories/{id}/refuser": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/commandes": {
      "get": {
        "tags": [
          "Admin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/commandes/{id}/annuler": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits": {
      "get": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "pageSize",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 10
            }
          },
          {
            "name": "search",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "statut",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "stockCritique",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "fournisseur",
            "in": "query",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits/{produitId}/valider": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits/{produitId}/refuser": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "string"
              }
            },
            "text/json": {
              "schema": {
                "type": "string"
              }
            },
            "application/*+json": {
              "schema": {
                "type": "string"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits/{produitId}/enavant": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits/{produitId}/retirer-avant": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits/{produitId}/moderer": {
      "put": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.ProduitModerationDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.ProduitModerationDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.ProduitModerationDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits/{produitId}": {
      "delete": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits/stock-critique": {
      "get": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "seuil",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 10
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits/{produitId}/stock": {
      "patch": {
        "tags": [
          "Admin"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "integer",
                "format": "int32"
              }
            },
            "text/json": {
              "schema": {
                "type": "integer",
                "format": "int32"
              }
            },
            "application/*+json": {
              "schema": {
                "type": "integer",
                "format": "int32"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/produits/en-avant": {
      "get": {
        "tags": [
          "Admin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Admin/statistiques": {
      "get": {
        "tags": [
          "Admin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Adresses/{id}": {
      "get": {
        "tags": [
          "Adresses"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Adresses"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.AdresseUpdateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.AdresseUpdateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.AdresseUpdateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Adresses"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Adresses/entity/{entityId}": {
      "get": {
        "tags": [
          "Adresses"
        ],
        "parameters": [
          {
            "name": "entityId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Adresses": {
      "post": {
        "tags": [
          "Adresses"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AdresseCreateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AdresseCreateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AdresseCreateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Adresses/{id}/principale/{entityId}": {
      "patch": {
        "tags": [
          "Adresses"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "entityId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/login": {
      "post": {
        "tags": [
          "Auth"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.AuthDTO.LoginDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.AuthDTO.LoginDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.AuthDTO.LoginDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/register/client": {
      "post": {
        "tags": [
          "Auth"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.ClientCreateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.ClientCreateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.ClientCreateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/register/fournisseur": {
      "post": {
        "tags": [
          "Auth"
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "required": [
                  "CodeBanque",
                  "CodePostal",
                  "Email",
                  "EstActif",
                  "LogoFile",
                  "MatriculeFiscale",
                  "Nom",
                  "Password",
                  "PhoneNumber",
                  "Prenom",
                  "RaisonSociale",
                  "RIB",
                  "Rue",
                  "Ville"
                ],
                "type": "object",
                "properties": {
                  "MatriculeFiscale": {
                    "pattern": "^\\d{8}$",
                    "type": "string"
                  },
                  "RaisonSociale": {
                    "maxLength": 200,
                    "minLength": 0,
                    "type": "string"
                  },
                  "Description": {
                    "type": "string"
                  },
                  "RIB": {
                    "type": "string"
                  },
                  "CodeBanque": {
                    "maxLength": 3,
                    "minLength": 3,
                    "pattern": "^[0-9]+$",
                    "type": "string"
                  },
                  "Commission": {
                    "maximum": 1,
                    "minimum": 0,5,
                    "type": "number",
                    "format": "double"
                  },
                  "DelaiPreparationJours": {
                    "type": "integer",
                    "format": "int32"
                  },
                  "FraisLivraisonBase": {
                    "type": "number",
                    "format": "double"
                  },
                  "LogoFile": {
                    "type": "string",
                    "format": "binary"
                  },
                  "Rue": {
                    "type": "string"
                  },
                  "Ville": {
                    "type": "string"
                  },
                  "CodePostal": {
                    "pattern": "^\\d{4}$",
                    "type": "string"
                  },
                  "Pays": {
                    "type": "string"
                  },
                  "Email": {
                    "type": "string",
                    "format": "email"
                  },
                  "Nom": {
                    "maxLength": 50,
                    "minLength": 0,
                    "type": "string"
                  },
                  "Prenom": {
                    "maxLength": 50,
                    "minLength": 0,
                    "type": "string"
                  },
                  "PhoneNumber": {
                    "type": "string",
                    "format": "tel"
                  },
                  "DateNaissance": {
                    "type": "string",
                    "format": "date-time"
                  },
                  "Password": {
                    "maxLength": 100,
                    "minLength": 8,
                    "type": "string"
                  },
                  "EstActif": {
                    "type": "boolean"
                  }
                }
              },
              "encoding": {
                "MatriculeFiscale": {
                  "style": "form"
                },
                "RaisonSociale": {
                  "style": "form"
                },
                "Description": {
                  "style": "form"
                },
                "RIB": {
                  "style": "form"
                },
                "CodeBanque": {
                  "style": "form"
                },
                "Commission": {
                  "style": "form"
                },
                "DelaiPreparationJours": {
                  "style": "form"
                },
                "FraisLivraisonBase": {
                  "style": "form"
                },
                "LogoFile": {
                  "style": "form"
                },
                "Rue": {
                  "style": "form"
                },
                "Ville": {
                  "style": "form"
                },
                "CodePostal": {
                  "style": "form"
                },
                "Pays": {
                  "style": "form"
                },
                "Email": {
                  "style": "form"
                },
                "Nom": {
                  "style": "form"
                },
                "Prenom": {
                  "style": "form"
                },
                "PhoneNumber": {
                  "style": "form"
                },
                "DateNaissance": {
                  "style": "form"
                },
                "Password": {
                  "style": "form"
                },
                "EstActif": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/register/admin": {
      "post": {
        "tags": [
          "Auth"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AdminCreateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AdminCreateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AdminCreateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/init-admin": {
      "post": {
        "tags": [
          "Auth"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Avis/produit/{produitId}": {
      "get": {
        "tags": [
          "Avis"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Avis/{produitId}/mon-avis": {
      "get": {
        "tags": [
          "Avis"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Avis/{produitId}/{id}": {
      "get": {
        "tags": [
          "Avis"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Avis/{produitId}": {
      "post": {
        "tags": [
          "Avis"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AvisCreateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AvisCreateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AvisCreateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Avis/{produitId}/moyenne": {
      "get": {
        "tags": [
          "Avis"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "application/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "text/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              }
            }
          }
        }
      }
    },
    "/api/AvisModeration": {
      "get": {
        "tags": [
          "AvisModeration"
        ],
        "parameters": [
          {
            "name": "Statut",
            "in": "query",
            "schema": {
              "$ref": "#/components/schemas/WebApiPfe.Models.Entity.StatutAvis"
            }
          },
          {
            "name": "FournisseurId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "ProduitId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "DateDebut",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "DateFin",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "Recherche",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PageSize",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "SortBy",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "SortDesc",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/AvisModeration/{id}": {
      "get": {
        "tags": [
          "AvisModeration"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/AvisModeration/{id}/moderer": {
      "put": {
        "tags": [
          "AvisModeration"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.ModererAvisDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.ModererAvisDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.ModererAvisDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/AvisModeration/fournisseur": {
      "get": {
        "tags": [
          "AvisModeration"
        ],
        "parameters": [
          {
            "name": "Statut",
            "in": "query",
            "schema": {
              "$ref": "#/components/schemas/WebApiPfe.Models.Entity.StatutAvis"
            }
          },
          {
            "name": "FournisseurId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "ProduitId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "DateDebut",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "DateFin",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "Recherche",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "Page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "PageSize",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "SortBy",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "SortDesc",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/AvisModeration/{id}/repondre": {
      "put": {
        "tags": [
          "AvisModeration"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "string"
              }
            },
            "text/json": {
              "schema": {
                "type": "string"
              }
            },
            "application/*+json": {
              "schema": {
                "type": "string"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/AvisModeration/statistiques": {
      "get": {
        "tags": [
          "AvisModeration"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisStatsDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisStatsDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisStatsDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/AvisModeration/statistiques/fournisseur": {
      "get": {
        "tags": [
          "AvisModeration"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisStatsDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisStatsDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisStatsDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Categories": {
      "get": {
        "tags": [
          "Categories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Categorie"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Categorie"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Categorie"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "Categories"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateCategorieDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateCategorieDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateCategorieDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Categories/admin": {
      "get": {
        "tags": [
          "Categories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CategorieAdminDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CategorieAdminDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CategorieAdminDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Categories/enriched": {
      "get": {
        "tags": [
          "Categories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Categories/fournisseur": {
      "get": {
        "tags": [
          "Categories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Categories/{id}": {
      "get": {
        "tags": [
          "Categories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CategorieDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Categories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateCategorieDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateCategorieDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateCategorieDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Categories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Categories/{id}/sous-categories": {
      "get": {
        "tags": [
          "Categories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Categories/{id}/produits-count": {
      "get": {
        "tags": [
          "Categories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              },
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              },
              "text/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/Categories/dropdown": {
      "get": {
        "tags": [
          "Categories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Categories/{id}/toggle-visibility": {
      "patch": {
        "tags": [
          "Categories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Clients": {
      "get": {
        "tags": [
          "Clients"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ClientDto"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "Clients"
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "required": [
                  "Email",
                  "EstActif",
                  "Nom",
                  "Password",
                  "PhoneNumber",
                  "Prenom"
                ],
                "type": "object",
                "properties": {
                  "Email": {
                    "type": "string",
                    "format": "email"
                  },
                  "Nom": {
                    "maxLength": 50,
                    "minLength": 0,
                    "type": "string"
                  },
                  "Prenom": {
                    "maxLength": 50,
                    "minLength": 0,
                    "type": "string"
                  },
                  "PhoneNumber": {
                    "type": "string",
                    "format": "tel"
                  },
                  "DateNaissance": {
                    "type": "string",
                    "format": "date-time"
                  },
                  "Password": {
                    "maxLength": 100,
                    "minLength": 8,
                    "type": "string"
                  },
                  "EstActif": {
                    "type": "boolean"
                  },
                  "adresseRue": {
                    "type": "string"
                  },
                  "adresseVille": {
                    "type": "string"
                  },
                  "adresseCodePostal": {
                    "type": "string"
                  },
                  "adressePays": {
                    "type": "string"
                  },
                  "adresseEstPrincipale": {
                    "type": "boolean",
                    "default": true
                  }
                }
              },
              "encoding": {
                "Email": {
                  "style": "form"
                },
                "Nom": {
                  "style": "form"
                },
                "Prenom": {
                  "style": "form"
                },
                "PhoneNumber": {
                  "style": "form"
                },
                "DateNaissance": {
                  "style": "form"
                },
                "Password": {
                  "style": "form"
                },
                "EstActif": {
                  "style": "form"
                },
                "adresseRue": {
                  "style": "form"
                },
                "adresseVille": {
                  "style": "form"
                },
                "adresseCodePostal": {
                  "style": "form"
                },
                "adressePays": {
                  "style": "form"
                },
                "adresseEstPrincipale": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ClientDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Clients/{id}": {
      "get": {
        "tags": [
          "Clients"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ClientDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Clients"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ClientUpdateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ClientUpdateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ClientUpdateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Clients"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Clients/{id}/adresses": {
      "put": {
        "tags": [
          "Clients"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Clients/{id}/profile": {
      "put": {
        "tags": [
          "Clients"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ClientProfileUpdateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ClientProfileUpdateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ClientProfileUpdateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Clients/{id}/commandes": {
      "get": {
        "tags": [
          "Clients"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/CommandeFournisseur": {
      "get": {
        "tags": [
          "CommandeFournisseur"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "CommandeFournisseur"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateCommandeFournisseurDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateCommandeFournisseurDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateCommandeFournisseurDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/CommandeFournisseur/test-direct": {
      "get": {
        "tags": [
          "CommandeFournisseur"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/CommandeFournisseur/{id}": {
      "get": {
        "tags": [
          "CommandeFournisseur"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/CommandeFournisseur/{id}/statut": {
      "put": {
        "tags": [
          "CommandeFournisseur"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateStatutCommandeFournisseurDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateStatutCommandeFournisseurDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateStatutCommandeFournisseurDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Commandes": {
      "post": {
        "tags": [
          "Commandes"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateCommandeDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateCommandeDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateCommandeDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                }
              }
            }
          }
        }
      },
      "get": {
        "tags": [
          "Commandes"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Commandes/{id}": {
      "get": {
        "tags": [
          "Commandes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                }
              }
            }
          }
        }
      },
      "delete": {
        "tags": [
          "Commandes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Commandes/by-client/{clientId}": {
      "get": {
        "tags": [
          "Commandes"
        ],
        "parameters": [
          {
            "name": "clientId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Commandes/{id}/commandes-fournisseurs": {
      "get": {
        "tags": [
          "Commandes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Commandes/{id}/force-division": {
      "post": {
        "tags": [
          "Commandes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Commandes/{id}/finaliser": {
      "post": {
        "tags": [
          "Commandes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "string"
              }
            },
            "text/json": {
              "schema": {
                "type": "string"
              }
            },
            "application/*+json": {
              "schema": {
                "type": "string"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Commandes/calculer-frais-livraison": {
      "post": {
        "tags": [
          "Commandes"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.DTOs.CalculerFraisLivraisonRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.DTOs.CalculerFraisLivraisonRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.DTOs.CalculerFraisLivraisonRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FraisLivraisonResponseDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FraisLivraisonResponseDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FraisLivraisonResponseDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Demandes/categories": {
      "post": {
        "tags": [
          "Demandes"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDemandeCategorieDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDemandeCategorieDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDemandeCategorieDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "get": {
        "tags": [
          "Demandes"
        ],
        "parameters": [
          {
            "name": "fournisseurId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Demandes/categories/{id}": {
      "get": {
        "tags": [
          "Demandes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Demandes/categories/{id}/traiter": {
      "put": {
        "tags": [
          "Demandes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.TraiterDemandeDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.TraiterDemandeDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.TraiterDemandeDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Demandes/sous-categories": {
      "post": {
        "tags": [
          "Demandes"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDemandeSousCategorieDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDemandeSousCategorieDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDemandeSousCategorieDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "get": {
        "tags": [
          "Demandes"
        ],
        "parameters": [
          {
            "name": "fournisseurId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Demandes/sous-categories/{id}": {
      "get": {
        "tags": [
          "Demandes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Demandes/sous-categories/{id}/traiter": {
      "put": {
        "tags": [
          "Demandes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.TraiterDemandeDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.TraiterDemandeDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.TraiterDemandeDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Demandes/statistiques/en-attente": {
      "get": {
        "tags": [
          "Demandes"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/DemandesCategories": {
      "get": {
        "tags": [
          "DemandesCategories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "DemandesCategories"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CreateDemandeCategorieDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CreateDemandeCategorieDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CreateDemandeCategorieDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/DemandesCategories/test": {
      "get": {
        "tags": [
          "DemandesCategories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "string"
                }
              },
              "application/json": {
                "schema": {
                  "type": "string"
                }
              },
              "text/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          }
        }
      }
    },
    "/api/DemandesCategories/statut/{statut}": {
      "get": {
        "tags": [
          "DemandesCategories"
        ],
        "parameters": [
          {
            "name": "statut",
            "in": "path",
            "required": true,
            "schema": {
              "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutDemande"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/DemandesCategories/mes-demandes": {
      "get": {
        "tags": [
          "DemandesCategories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/DemandesCategories/{id}": {
      "get": {
        "tags": [
          "DemandesCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                }
              }
            }
          }
        }
      },
      "delete": {
        "tags": [
          "DemandesCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/DemandesCategories/{id}/traiter": {
      "put": {
        "tags": [
          "DemandesCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.TraiterDemandeCategorieDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.TraiterDemandeCategorieDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.TraiterDemandeCategorieDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeCategorieDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/DemandesSousCategories": {
      "get": {
        "tags": [
          "DemandesSousCategories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "DemandesSousCategories"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CreateDemandeSousCategorieDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CreateDemandeSousCategorieDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.CreateDemandeSousCategorieDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/DemandesSousCategories/test": {
      "get": {
        "tags": [
          "DemandesSousCategories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "string"
                }
              },
              "application/json": {
                "schema": {
                  "type": "string"
                }
              },
              "text/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          }
        }
      }
    },
    "/api/DemandesSousCategories/statut/{statut}": {
      "get": {
        "tags": [
          "DemandesSousCategories"
        ],
        "parameters": [
          {
            "name": "statut",
            "in": "path",
            "required": true,
            "schema": {
              "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutDemande"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/DemandesSousCategories/mes-demandes": {
      "get": {
        "tags": [
          "DemandesSousCategories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/DemandesSousCategories/{id}": {
      "get": {
        "tags": [
          "DemandesSousCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                }
              }
            }
          }
        }
      },
      "delete": {
        "tags": [
          "DemandesSousCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/DemandesSousCategories/{id}/traiter": {
      "put": {
        "tags": [
          "DemandesSousCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.TraiterDemandeSousCategorieDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.TraiterDemandeSousCategorieDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.TraiterDemandeSousCategorieDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.DemandeSousCategorieDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/DetailsCommande/{id}": {
      "get": {
        "tags": [
          "DetailsCommande"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "DetailsCommande"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateDetailsCommandeDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateDetailsCommandeDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateDetailsCommandeDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "DetailsCommande"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/DetailsCommande/commande/{commandeId}": {
      "get": {
        "tags": [
          "DetailsCommande"
        ],
        "parameters": [
          {
            "name": "commandeId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/DetailsCommande": {
      "post": {
        "tags": [
          "DetailsCommande"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDetailsCommandeDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDetailsCommandeDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDetailsCommandeDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/DetailsCommande/{id}/recalculer": {
      "post": {
        "tags": [
          "DetailsCommande"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Diagnostic/produits-sans-fournisseur": {
      "get": {
        "tags": [
          "Diagnostic"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Diagnostic/commandes-fournisseurs": {
      "get": {
        "tags": [
          "Diagnostic"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Diagnostic/fix-produits-fournisseurs": {
      "post": {
        "tags": [
          "Diagnostic"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Favoris": {
      "post": {
        "tags": [
          "Favoris"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.FavoriCreateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.FavoriCreateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.FavoriCreateDto"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request"
          }
        }
      }
    },
    "/api/Favoris/{id}": {
      "get": {
        "tags": [
          "Favoris"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
                }
              }
            }
          },
          "404": {
            "description": "Not Found"
          }
        }
      },
      "delete": {
        "tags": [
          "Favoris"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "204": {
            "description": "No Content"
          },
          "404": {
            "description": "Not Found"
          }
        }
      }
    },
    "/api/Favoris/verifier": {
      "get": {
        "tags": [
          "Favoris"
        ],
        "parameters": [
          {
            "name": "clientId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "produitId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "boolean"
                }
              },
              "application/json": {
                "schema": {
                  "type": "boolean"
                }
              },
              "text/json": {
                "schema": {
                  "type": "boolean"
                }
              }
            }
          }
        }
      }
    },
    "/api/Favoris/client/{clientId}": {
      "get": {
        "tags": [
          "Favoris"
        ],
        "parameters": [
          {
            "name": "clientId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Formes": {
      "get": {
        "tags": [
          "Formes"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "Formes"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateFormeDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateFormeDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateFormeDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Formes/enriched": {
      "get": {
        "tags": [
          "Formes"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FormeDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FormeDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FormeDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Formes/{id}": {
      "get": {
        "tags": [
          "Formes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Formes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Formes"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Formes/by-categorie/{categorieId}": {
      "get": {
        "tags": [
          "Formes"
        ],
        "parameters": [
          {
            "name": "categorieId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Forme"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Formes/dropdown": {
      "get": {
        "tags": [
          "Formes"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Fournisseurs": {
      "get": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "onlyActive",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "Fournisseurs"
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "required": [
                  "CodeBanque",
                  "CodePostal",
                  "Email",
                  "EstActif",
                  "LogoFile",
                  "MatriculeFiscale",
                  "Nom",
                  "Password",
                  "PhoneNumber",
                  "Prenom",
                  "RaisonSociale",
                  "RIB",
                  "Rue",
                  "Ville"
                ],
                "type": "object",
                "properties": {
                  "MatriculeFiscale": {
                    "pattern": "^\\d{8}$",
                    "type": "string"
                  },
                  "RaisonSociale": {
                    "maxLength": 200,
                    "minLength": 0,
                    "type": "string"
                  },
                  "Description": {
                    "type": "string"
                  },
                  "RIB": {
                    "type": "string"
                  },
                  "CodeBanque": {
                    "maxLength": 3,
                    "minLength": 3,
                    "pattern": "^[0-9]+$",
                    "type": "string"
                  },
                  "Commission": {
                    "maximum": 1,
                    "minimum": 0,5,
                    "type": "number",
                    "format": "double"
                  },
                  "DelaiPreparationJours": {
                    "type": "integer",
                    "format": "int32"
                  },
                  "FraisLivraisonBase": {
                    "type": "number",
                    "format": "double"
                  },
                  "LogoFile": {
                    "type": "string",
                    "format": "binary"
                  },
                  "Rue": {
                    "type": "string"
                  },
                  "Ville": {
                    "type": "string"
                  },
                  "CodePostal": {
                    "pattern": "^\\d{4}$",
                    "type": "string"
                  },
                  "Pays": {
                    "type": "string"
                  },
                  "Email": {
                    "type": "string",
                    "format": "email"
                  },
                  "Nom": {
                    "maxLength": 50,
                    "minLength": 0,
                    "type": "string"
                  },
                  "Prenom": {
                    "maxLength": 50,
                    "minLength": 0,
                    "type": "string"
                  },
                  "PhoneNumber": {
                    "type": "string",
                    "format": "tel"
                  },
                  "DateNaissance": {
                    "type": "string",
                    "format": "date-time"
                  },
                  "Password": {
                    "maxLength": 100,
                    "minLength": 8,
                    "type": "string"
                  },
                  "EstActif": {
                    "type": "boolean"
                  },
                  "adresseRue": {
                    "type": "string"
                  },
                  "adresseVille": {
                    "type": "string"
                  },
                  "adresseCodePostal": {
                    "type": "string"
                  },
                  "adressePays": {
                    "type": "string"
                  },
                  "adresseEstPrincipale": {
                    "type": "boolean",
                    "default": true
                  }
                }
              },
              "encoding": {
                "MatriculeFiscale": {
                  "style": "form"
                },
                "RaisonSociale": {
                  "style": "form"
                },
                "Description": {
                  "style": "form"
                },
                "RIB": {
                  "style": "form"
                },
                "CodeBanque": {
                  "style": "form"
                },
                "Commission": {
                  "style": "form"
                },
                "DelaiPreparationJours": {
                  "style": "form"
                },
                "FraisLivraisonBase": {
                  "style": "form"
                },
                "LogoFile": {
                  "style": "form"
                },
                "Rue": {
                  "style": "form"
                },
                "Ville": {
                  "style": "form"
                },
                "CodePostal": {
                  "style": "form"
                },
                "Pays": {
                  "style": "form"
                },
                "Email": {
                  "style": "form"
                },
                "Nom": {
                  "style": "form"
                },
                "Prenom": {
                  "style": "form"
                },
                "PhoneNumber": {
                  "style": "form"
                },
                "DateNaissance": {
                  "style": "form"
                },
                "Password": {
                  "style": "form"
                },
                "EstActif": {
                  "style": "form"
                },
                "adresseRue": {
                  "style": "form"
                },
                "adresseVille": {
                  "style": "form"
                },
                "adresseCodePostal": {
                  "style": "form"
                },
                "adressePays": {
                  "style": "form"
                },
                "adresseEstPrincipale": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Fournisseurs/{id}": {
      "get": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.FournisseurUpdateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.FournisseurUpdateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.FournisseurUpdateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Fournisseurs/{id}/toggle-status": {
      "patch": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Fournisseurs/{id}/produits": {
      "get": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Fournisseurs/valider-rib": {
      "get": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "rib",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "codeBanque",
            "in": "query",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "boolean"
                }
              },
              "application/json": {
                "schema": {
                  "type": "boolean"
                }
              },
              "text/json": {
                "schema": {
                  "type": "boolean"
                }
              }
            }
          }
        }
      }
    },
    "/api/Fournisseurs/exists/{id}": {
      "get": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "boolean"
                }
              },
              "application/json": {
                "schema": {
                  "type": "boolean"
                }
              },
              "text/json": {
                "schema": {
                  "type": "boolean"
                }
              }
            }
          }
        }
      }
    },
    "/api/Fournisseurs/{id}/commission": {
      "patch": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "number",
                "format": "double"
              }
            },
            "text/json": {
              "schema": {
                "type": "number",
                "format": "double"
              }
            },
            "application/*+json": {
              "schema": {
                "type": "number",
                "format": "double"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Fournisseurs/{id}/adresses": {
      "post": {
        "tags": [
          "Fournisseurs"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AdresseCreateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AdresseCreateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AdresseCreateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/ItemPanier": {
      "post": {
        "tags": [
          "ItemPanier"
        ],
        "parameters": [
          {
            "name": "panierId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AddItemPanierDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AddItemPanierDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AddItemPanierDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/ItemPanier/{itemId}": {
      "get": {
        "tags": [
          "ItemPanier"
        ],
        "parameters": [
          {
            "name": "panierId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "itemId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "ItemPanier"
        ],
        "parameters": [
          {
            "name": "panierId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "itemId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateItemPanierDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateItemPanierDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateItemPanierDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "ItemPanier"
        ],
        "parameters": [
          {
            "name": "panierId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "itemId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/LignesCommandeFournisseur": {
      "get": {
        "tags": [
          "LignesCommandeFournisseur"
        ],
        "parameters": [
          {
            "name": "commandeId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "LignesCommandeFournisseur"
        ],
        "parameters": [
          {
            "name": "commandeId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateLigneCommandeFournisseurDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateLigneCommandeFournisseurDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateLigneCommandeFournisseurDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/LignesCommandeFournisseur/{id}": {
      "get": {
        "tags": [
          "LignesCommandeFournisseur"
        ],
        "operationId": "GetLigneCommandeFournisseur",
        "parameters": [
          {
            "name": "commandeId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "LignesCommandeFournisseur"
        ],
        "parameters": [
          {
            "name": "commandeId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateLigneCommandeFournisseurDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateLigneCommandeFournisseurDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateLigneCommandeFournisseurDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "LignesCommandeFournisseur"
        ],
        "parameters": [
          {
            "name": "commandeId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Livraison/{id}": {
      "get": {
        "tags": [
          "Livraison"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LivraisonDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LivraisonDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LivraisonDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Livraison"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateLivraisonDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateLivraisonDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateLivraisonDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Livraison/commande/{commandeId}": {
      "get": {
        "tags": [
          "Livraison"
        ],
        "parameters": [
          {
            "name": "commandeId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LivraisonDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LivraisonDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LivraisonDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Livraison": {
      "post": {
        "tags": [
          "Livraison"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateLivraisonDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateLivraisonDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateLivraisonDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LivraisonDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LivraisonDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LivraisonDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Livraison/{livraisonId}/statut": {
      "post": {
        "tags": [
          "Livraison"
        ],
        "parameters": [
          {
            "name": "livraisonId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ChangeStatutLivraisonDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ChangeStatutLivraisonDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ChangeStatutLivraisonDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Livraison/{livraisonId}/statuts-disponibles": {
      "get": {
        "tags": [
          "Livraison"
        ],
        "parameters": [
          {
            "name": "livraisonId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.StatutLivraisonDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.StatutLivraisonDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.StatutLivraisonDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Livraison/{livraisonId}/etiquette": {
      "get": {
        "tags": [
          "Livraison"
        ],
        "parameters": [
          {
            "name": "livraisonId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "string"
                }
              },
              "application/json": {
                "schema": {
                  "type": "string"
                }
              },
              "text/json": {
                "schema": {
                  "type": "string"
                }
              }
            }
          }
        }
      }
    },
    "/api/Marques": {
      "get": {
        "tags": [
          "Marques"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "Marques"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
                }
              }
            }
          }
        }
      }
    },
    "/api/Marques/enriched": {
      "get": {
        "tags": [
          "Marques"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.MarqueDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.MarqueDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.MarqueDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Marques/{id}": {
      "get": {
        "tags": [
          "Marques"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Marques"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.Marque"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Marques"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Marques/dropdown": {
      "get": {
        "tags": [
          "Marques"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Notification/{id}": {
      "get": {
        "tags": [
          "Notification"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                }
              }
            }
          }
        }
      },
      "delete": {
        "tags": [
          "Notification"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Notification/user/{userId}": {
      "get": {
        "tags": [
          "Notification"
        ],
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Notification/user/{userId}/unread": {
      "get": {
        "tags": [
          "Notification"
        ],
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Notification": {
      "post": {
        "tags": [
          "Notification"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateNotificationDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateNotificationDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateNotificationDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.NotificationDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Notification/{id}/read": {
      "patch": {
        "tags": [
          "Notification"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Paiements": {
      "post": {
        "tags": [
          "Paiements"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ValidationProblemDetails"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ValidationProblemDetails"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/Microsoft.AspNetCore.Mvc.ValidationProblemDetails"
                }
              }
            }
          },
          "401": {
            "description": "Unauthorized"
          }
        }
      },
      "get": {
        "tags": [
          "Paiements"
        ],
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "pageSize",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 10
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Paiements/rembourser": {
      "post": {
        "tags": [
          "Paiements"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Paiements/{id}": {
      "get": {
        "tags": [
          "Paiements"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Panier/client/{clientId}": {
      "get": {
        "tags": [
          "Panier"
        ],
        "parameters": [
          {
            "name": "clientId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PanierDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PanierDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PanierDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Panier": {
      "post": {
        "tags": [
          "Panier"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreatePanierDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreatePanierDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreatePanierDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PanierDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PanierDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PanierDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Panier/{id}": {
      "put": {
        "tags": [
          "Panier"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdatePanierDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdatePanierDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdatePanierDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Panier"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Panier/{panierId}/items": {
      "post": {
        "tags": [
          "Panier"
        ],
        "parameters": [
          {
            "name": "panierId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AddItemPanierDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AddItemPanierDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.AddItemPanierDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Panier/items/{itemId}": {
      "put": {
        "tags": [
          "Panier"
        ],
        "parameters": [
          {
            "name": "itemId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateItemPanierDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateItemPanierDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateItemPanierDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Panier"
        ],
        "parameters": [
          {
            "name": "itemId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Produits": {
      "post": {
        "tags": [
          "Produits"
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "required": [
                  "CodeABarre",
                  "FormeId",
                  "FournisseurId",
                  "MarqueId",
                  "Nom",
                  "PrixAchatHT",
                  "PrixVenteHT",
                  "ReferenceOriginal",
                  "SousCategorieId",
                  "Stock",
                  "TauxTVAId"
                ],
                "type": "object",
                "properties": {
                  "ReferenceOriginal": {
                    "maxLength": 50,
                    "minLength": 0,
                    "type": "string"
                  },
                  "ReferenceFournisseur": {
                    "maxLength": 50,
                    "minLength": 0,
                    "type": "string"
                  },
                  "CodeABarre": {
                    "maxLength": 30,
                    "minLength": 0,
                    "type": "string"
                  },
                  "Nom": {
                    "maxLength": 100,
                    "minLength": 0,
                    "type": "string"
                  },
                  "Description": {
                    "type": "string"
                  },
                  "PrixAchatHT": {
                    "minimum": 0,01,
                    "type": "number",
                    "format": "double"
                  },
                  "PrixVenteHT": {
                    "minimum": 0,01,
                    "type": "number",
                    "format": "double"
                  },
                  "TauxTVAId": {
                    "type": "integer",
                    "format": "int32"
                  },
                  "Stock": {
                    "maximum": 2147483647,
                    "minimum": 0,
                    "type": "integer",
                    "format": "int32"
                  },
                  "SousCategorieId": {
                    "type": "integer",
                    "format": "int32"
                  },
                  "MarqueId": {
                    "type": "integer",
                    "format": "int32"
                  },
                  "FormeId": {
                    "type": "integer",
                    "format": "int32"
                  },
                  "FournisseurId": {
                    "type": "integer",
                    "format": "int32"
                  },
                  "ImageFiles": {
                    "type": "array",
                    "items": {
                      "type": "string",
                      "format": "binary"
                    }
                  },
                  "PourcentageRemise": {
                    "maximum": 100,
                    "minimum": 0,
                    "type": "integer",
                    "format": "int32"
                  }
                }
              },
              "encoding": {
                "ReferenceOriginal": {
                  "style": "form"
                },
                "ReferenceFournisseur": {
                  "style": "form"
                },
                "CodeABarre": {
                  "style": "form"
                },
                "Nom": {
                  "style": "form"
                },
                "Description": {
                  "style": "form"
                },
                "PrixAchatHT": {
                  "style": "form"
                },
                "PrixVenteHT": {
                  "style": "form"
                },
                "TauxTVAId": {
                  "style": "form"
                },
                "Stock": {
                  "style": "form"
                },
                "SousCategorieId": {
                  "style": "form"
                },
                "MarqueId": {
                  "style": "form"
                },
                "FormeId": {
                  "style": "form"
                },
                "FournisseurId": {
                  "style": "form"
                },
                "ImageFiles": {
                  "style": "form"
                },
                "PourcentageRemise": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              }
            }
          }
        }
      },
      "get": {
        "tags": [
          "Produits"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/{id}": {
      "put": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "required": [
                  "Id"
                ],
                "type": "object",
                "properties": {
                  "Id": {
                    "type": "integer",
                    "format": "int32"
                  },
                  "Nom": {
                    "maxLength": 100,
                    "minLength": 0,
                    "type": "string"
                  },
                  "Description": {
                    "type": "string"
                  },
                  "PrixVenteHT": {
                    "minimum": 0,01,
                    "type": "number",
                    "format": "double"
                  },
                  "Stock": {
                    "type": "integer",
                    "format": "int32"
                  },
                  "PourcentageRemise": {
                    "maximum": 100,
                    "minimum": 0,
                    "type": "number",
                    "format": "double"
                  },
                  "ImageFiles": {
                    "type": "array",
                    "items": {
                      "type": "string",
                      "format": "binary"
                    }
                  }
                }
              },
              "encoding": {
                "Id": {
                  "style": "form"
                },
                "Nom": {
                  "style": "form"
                },
                "Description": {
                  "style": "form"
                },
                "PrixVenteHT": {
                  "style": "form"
                },
                "Stock": {
                  "style": "form"
                },
                "PourcentageRemise": {
                  "style": "form"
                },
                "ImageFiles": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/by-reference-original/{reference}": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "reference",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/by-reference-fournisseur/{reference}": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "reference",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/by-code-barre/{codeABarre}": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "codeABarre",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/search": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "term",
            "in": "query",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/by-sous-categorie/{sousCategorieId}": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "sousCategorieId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/by-categorie/{categorieId}": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "categorieId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/by-fournisseur/{fournisseurId}": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "fournisseurId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/by-marque/{marqueId}": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "marqueId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/by-forme/{formeId}": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "formeId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/promotions": {
      "get": {
        "tags": [
          "Produits"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/en-stock": {
      "get": {
        "tags": [
          "Produits"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/{produitId}/images": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ImageProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ImageProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ImageProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/dropdown": {
      "get": {
        "tags": [
          "Produits"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/meilleures-ventes": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "limit",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 20
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/nouveaux-arrivages": {
      "get": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "limit",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 10
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProduitDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Produits/{produitId}/images/{imageId}": {
      "put": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "imageId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ImageProduitUpdateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ImageProduitUpdateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ImageProduitUpdateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Produits/{produitId}/images/{imageId}/set-main": {
      "patch": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "imageId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Produits/{id}/images/{imageId}": {
      "delete": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "imageId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Produits/{id}/stock": {
      "patch": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ProduitStockUpdateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ProduitStockUpdateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ProduitStockUpdateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Produits/{id}/prix": {
      "patch": {
        "tags": [
          "Produits"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ProduitPrixUpdateDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ProduitPrixUpdateDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.ProduitPrixUpdateDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Promotions": {
      "get": {
        "tags": [
          "Promotions"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "Promotions"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreatePromotionDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreatePromotionDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreatePromotionDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Promotions/{id}": {
      "get": {
        "tags": [
          "Promotions"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Promotions"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreatePromotionDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreatePromotionDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreatePromotionDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Promotions"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Promotions/produit/{produitId}": {
      "get": {
        "tags": [
          "Promotions"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Promotions/validate-code": {
      "get": {
        "tags": [
          "Promotions"
        ],
        "parameters": [
          {
            "name": "code",
            "in": "query",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "boolean"
                }
              },
              "application/json": {
                "schema": {
                  "type": "boolean"
                }
              },
              "text/json": {
                "schema": {
                  "type": "boolean"
                }
              }
            }
          }
        }
      }
    },
    "/api/Promotions/calculate-price": {
      "get": {
        "tags": [
          "Promotions"
        ],
        "parameters": [
          {
            "name": "produitId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "codePromo",
            "in": "query",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "application/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "text/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              }
            }
          }
        }
      }
    },
    "/api/PromotionsUtilisees/apply": {
      "post": {
        "tags": [
          "PromotionsUtilisees"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.ApplyPromotionDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.ApplyPromotionDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.ApplyPromotionDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/PromotionsUtilisees/commande/{commandeId}": {
      "get": {
        "tags": [
          "PromotionsUtilisees"
        ],
        "parameters": [
          {
            "name": "commandeId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/PromotionsUtilisees/promotion/{promotionId}": {
      "get": {
        "tags": [
          "PromotionsUtilisees"
        ],
        "parameters": [
          {
            "name": "promotionId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/PromotionsUtilisees/economies": {
      "get": {
        "tags": [
          "PromotionsUtilisees"
        ],
        "parameters": [
          {
            "name": "startDate",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "endDate",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "application/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "text/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              }
            }
          }
        }
      }
    },
    "/api/Remboursement/{id}": {
      "get": {
        "tags": [
          "Remboursement"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Remboursement"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateRemboursementDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateRemboursementDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.UpdateDTOs.UpdateRemboursementDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Remboursement/commande/{commandeId}": {
      "get": {
        "tags": [
          "Remboursement"
        ],
        "parameters": [
          {
            "name": "commandeId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Remboursement/statut/{statut}": {
      "get": {
        "tags": [
          "Remboursement"
        ],
        "parameters": [
          {
            "name": "statut",
            "in": "path",
            "required": true,
            "schema": {
              "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutRemboursement"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/Remboursement": {
      "post": {
        "tags": [
          "Remboursement"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateRemboursementDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateRemboursementDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateRemboursementDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                }
              }
            }
          }
        }
      }
    },
    "/api/Remboursement/{id}/process": {
      "post": {
        "tags": [
          "Remboursement"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProcessRemboursementDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProcessRemboursementDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ProcessRemboursementDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Remboursement/utilisateur/{userId}": {
      "get": {
        "tags": [
          "Remboursement"
        ],
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.RemboursementDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/SousCategories": {
      "get": {
        "tags": [
          "SousCategories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "SousCategories"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto.Create"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto.Create"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto.Create"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                }
              }
            }
          }
        }
      }
    },
    "/api/SousCategories/fournisseur": {
      "get": {
        "tags": [
          "SousCategories"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/SousCategories/debug": {
      "get": {
        "tags": [
          "SousCategories"
        ],
        "parameters": [
          {
            "name": "fournisseurId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/SousCategories/enriched": {
      "get": {
        "tags": [
          "SousCategories"
        ],
        "parameters": [
          {
            "name": "fournisseurId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/SousCategories/{id}": {
      "get": {
        "tags": [
          "SousCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "SousCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto.Update"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto.Update"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto.Update"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "SousCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/SousCategories/by-categorie/{categorieId}": {
      "get": {
        "tags": [
          "SousCategories"
        ],
        "parameters": [
          {
            "name": "categorieId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.SousCategorie"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/SousCategories/{id}/produits-count": {
      "get": {
        "tags": [
          "SousCategories"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              },
              "application/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              },
              "text/json": {
                "schema": {
                  "type": "integer",
                  "format": "int32"
                }
              }
            }
          }
        }
      }
    },
    "/api/SousCategories/dropdown/{categorieId}": {
      "get": {
        "tags": [
          "SousCategories"
        ],
        "parameters": [
          {
            "name": "categorieId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tva": {
      "get": {
        "tags": [
          "TauxTVA"
        ],
        "parameters": [
          {
            "name": "inclureInactifs",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": false
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "TauxTVA"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.TauxTVADto.Create"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.TauxTVADto.Create"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.TauxTVADto.Create"
              }
            }
          }
        },
        "responses": {
          "201": {
            "description": "Created",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.TauxTVADto"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.TauxTVADto"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.TauxTVADto"
                }
              }
            }
          },
          "400": {
            "description": "Bad Request"
          }
        }
      }
    },
    "/api/tva/actuel": {
      "get": {
        "tags": [
          "TauxTVA"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                }
              }
            }
          }
        }
      }
    },
    "/api/tva/{id}": {
      "get": {
        "tags": [
          "TauxTVA"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "TauxTVA"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "TauxTVA"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/tva/dropdown": {
      "get": {
        "tags": [
          "TauxTVA"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tva/by-categorie/{categorieId}": {
      "get": {
        "tags": [
          "TauxTVA"
        ],
        "parameters": [
          {
            "name": "categorieId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/WebApiPfe.Models.Entity.TauxTVA"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tva/dropdown/{categorieId}": {
      "get": {
        "tags": [
          "TauxTVA"
        ],
        "parameters": [
          {
            "name": "categorieId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "application/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              },
              "text/json": {
                "schema": {
                  "type": "object",
                  "additionalProperties": {
                    "type": "string"
                  }
                }
              }
            }
          }
        }
      }
    },
    "/api/tva/calculer-ttc": {
      "post": {
        "tags": [
          "TauxTVA"
        ],
        "parameters": [
          {
            "name": "tauxId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "prixHT",
            "in": "query",
            "schema": {
              "type": "number",
              "format": "double"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "application/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "text/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              }
            }
          }
        }
      }
    },
    "/api/tva/calculer-ht": {
      "post": {
        "tags": [
          "TauxTVA"
        ],
        "parameters": [
          {
            "name": "tauxId",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "prixTTC",
            "in": "query",
            "schema": {
              "type": "number",
              "format": "double"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "application/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              },
              "text/json": {
                "schema": {
                  "type": "number",
                  "format": "double"
                }
              }
            }
          }
        }
      }
    },
    "/api/TestCommande": {
      "post": {
        "tags": [
          "TestCommande"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Controllers.CommandeSimpleDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Controllers.CommandeSimpleDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/WebApiPfe.Controllers.CommandeSimpleDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/TestData/create-test-commande-fournisseur": {
      "post": {
        "tags": [
          "TestData"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/TestData/fix-all-commandes": {
      "post": {
        "tags": [
          "TestData"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "Microsoft.AspNetCore.Mvc.ValidationProblemDetails": {
        "type": "object",
        "properties": {
          "type": {
            "type": "string",
            "nullable": true
          },
          "title": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "detail": {
            "type": "string",
            "nullable": true
          },
          "instance": {
            "type": "string",
            "nullable": true
          },
          "errors": {
            "type": "object",
            "additionalProperties": {
              "type": "array",
              "items": {
                "type": "string"
              }
            },
            "nullable": true
          }
        },
        "additionalProperties": { }
      },
      "WebApiPfe.Controllers.CommandeSimpleDto": {
        "type": "object",
        "properties": {
          "clientId": {
            "type": "integer",
            "format": "int32"
          },
          "detailsCommandes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.Controllers.DetailSimpleDto"
            },
            "nullable": true
          },
          "codePromo": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.Controllers.DetailSimpleDto": {
        "type": "object",
        "properties": {
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "quantite": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.AvisModerationDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "note": {
            "maximum": 5,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "commentaire": {
            "type": "string",
            "nullable": true
          },
          "datePublication": {
            "type": "string",
            "format": "date-time"
          },
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Entity.StatutAvis"
          },
          "statutLibelle": {
            "type": "string",
            "nullable": true
          },
          "dateModeration": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "commentaireModeration": {
            "type": "string",
            "nullable": true
          },
          "nomModerateur": {
            "type": "string",
            "nullable": true
          },
          "clientId": {
            "type": "integer",
            "format": "int32"
          },
          "clientNom": {
            "type": "string",
            "nullable": true
          },
          "clientPrenom": {
            "type": "string",
            "nullable": true
          },
          "clientEmail": {
            "type": "string",
            "nullable": true
          },
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "produitNom": {
            "type": "string",
            "nullable": true
          },
          "produitReference": {
            "type": "string",
            "nullable": true
          },
          "fournisseurId": {
            "type": "integer",
            "format": "int32"
          },
          "fournisseurNom": {
            "type": "string",
            "nullable": true
          },
          "fournisseurRaisonSociale": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.AvisStatsDto": {
        "type": "object",
        "properties": {
          "totalAvis": {
            "type": "integer",
            "format": "int32"
          },
          "avisEnAttente": {
            "type": "integer",
            "format": "int32"
          },
          "avisApprouves": {
            "type": "integer",
            "format": "int32"
          },
          "avisRejetes": {
            "type": "integer",
            "format": "int32"
          },
          "avisSignales": {
            "type": "integer",
            "format": "int32"
          },
          "noteMoyenneGlobale": {
            "type": "number",
            "format": "double"
          },
          "avisParNote": {
            "type": "object",
            "additionalProperties": {
              "type": "integer",
              "format": "int32"
            },
            "nullable": true
          },
          "avisRecents": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.AvisModerationDto"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.CategorieAdminDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estValidee": {
            "type": "boolean"
          },
          "sousCategories": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.Admin.SousCategorieAdminDto"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.CategorieGestionDto": {
        "type": "object",
        "properties": {
          "nom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.CreateDemandeCategorieDto": {
        "type": "object",
        "properties": {
          "nom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.CreateDemandeSousCategorieDto": {
        "type": "object",
        "properties": {
          "nom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "categorieId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.DemandeCategorieDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "fournisseurId": {
            "type": "integer",
            "format": "int32"
          },
          "nomFournisseur": {
            "type": "string",
            "nullable": true
          },
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutDemande"
          },
          "dateDemande": {
            "type": "string",
            "format": "date-time"
          },
          "dateTraitement": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "adminTraitantId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "nomAdminTraitant": {
            "type": "string",
            "nullable": true
          },
          "commentaireAdmin": {
            "type": "string",
            "nullable": true
          },
          "categorieCreeeId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "nomCategorieCreee": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.DemandeSousCategorieDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "categorieId": {
            "type": "integer",
            "format": "int32"
          },
          "nomCategorie": {
            "type": "string",
            "nullable": true
          },
          "fournisseurId": {
            "type": "integer",
            "format": "int32"
          },
          "nomFournisseur": {
            "type": "string",
            "nullable": true
          },
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutDemande"
          },
          "dateDemande": {
            "type": "string",
            "format": "date-time"
          },
          "dateTraitement": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "adminTraitantId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "nomAdminTraitant": {
            "type": "string",
            "nullable": true
          },
          "commentaireAdmin": {
            "type": "string",
            "nullable": true
          },
          "sousCategorieCreeeId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "nomSousCategorieCreee": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.ModererAvisDto": {
        "required": [
          "statut"
        ],
        "type": "object",
        "properties": {
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Entity.StatutAvis"
          },
          "commentaireModeration": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.ProduitModerationDto": {
        "type": "object",
        "properties": {
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "nouveauNom": {
            "type": "string",
            "nullable": true
          },
          "nouvelleDescription": {
            "type": "string",
            "nullable": true
          },
          "raison": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.SousCategorieAdminDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estValidee": {
            "type": "boolean"
          },
          "categorieId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.SousCategorieGestionDto": {
        "type": "object",
        "properties": {
          "categorieId": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.TraiterDemandeCategorieDto": {
        "type": "object",
        "properties": {
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutDemande"
          },
          "commentaireAdmin": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.Admin.TraiterDemandeSousCategorieDto": {
        "type": "object",
        "properties": {
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutDemande"
          },
          "commentaireAdmin": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.AuthDTO.LoginDto": {
        "required": [
          "email",
          "password"
        ],
        "type": "object",
        "properties": {
          "email": {
            "minLength": 1,
            "type": "string"
          },
          "password": {
            "minLength": 1,
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.AddItemPanierDto": {
        "required": [
          "produitId",
          "quantite"
        ],
        "type": "object",
        "properties": {
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "quantite": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.AdminCreateDto": {
        "required": [
          "email",
          "motDePasse",
          "nom",
          "prenom"
        ],
        "type": "object",
        "properties": {
          "nom": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "prenom": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "email": {
            "maxLength": 255,
            "minLength": 0,
            "type": "string",
            "format": "email"
          },
          "motDePasse": {
            "maxLength": 100,
            "minLength": 6,
            "type": "string"
          },
          "telephone": {
            "maxLength": 20,
            "minLength": 0,
            "type": "string",
            "format": "tel",
            "nullable": true
          },
          "estActif": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.AdresseCreateDto": {
        "required": [
          "codePostal",
          "entityId",
          "pays",
          "rue",
          "ville"
        ],
        "type": "object",
        "properties": {
          "rue": {
            "minLength": 1,
            "type": "string"
          },
          "ville": {
            "minLength": 1,
            "type": "string"
          },
          "codePostal": {
            "minLength": 1,
            "pattern": "^\\d{4}$",
            "type": "string"
          },
          "pays": {
            "minLength": 1,
            "type": "string"
          },
          "estPrincipale": {
            "type": "boolean"
          },
          "entityId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.ApplyPromotionDto": {
        "required": [
          "commandeId"
        ],
        "type": "object",
        "properties": {
          "commandeId": {
            "type": "integer",
            "format": "int32"
          },
          "codePromo": {
            "maxLength": 20,
            "minLength": 0,
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.AvisCreateDto": {
        "required": [
          "note",
          "produitId"
        ],
        "type": "object",
        "properties": {
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "note": {
            "maximum": 5,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "commentaire": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.ClientCreateDto": {
        "required": [
          "email",
          "estActif",
          "nom",
          "password",
          "phoneNumber",
          "prenom"
        ],
        "type": "object",
        "properties": {
          "email": {
            "minLength": 1,
            "type": "string",
            "format": "email"
          },
          "nom": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "prenom": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "phoneNumber": {
            "minLength": 1,
            "type": "string",
            "format": "tel"
          },
          "dateNaissance": {
            "type": "string",
            "format": "date-time"
          },
          "password": {
            "maxLength": 100,
            "minLength": 8,
            "type": "string"
          },
          "estActif": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateCategorieDto": {
        "required": [
          "nom"
        ],
        "type": "object",
        "properties": {
          "nom": {
            "maxLength": 100,
            "minLength": 3,
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateCommandeDto": {
        "required": [
          "clientId",
          "detailsCommandes"
        ],
        "type": "object",
        "properties": {
          "clientId": {
            "type": "integer",
            "format": "int32"
          },
          "detailsCommandes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateDetailsCommandeSimpleDto"
            }
          },
          "codePromo": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateCommandeFournisseurDto": {
        "required": [
          "fournisseurId",
          "lignes"
        ],
        "type": "object",
        "properties": {
          "fournisseurId": {
            "type": "integer",
            "format": "int32"
          },
          "lignes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.CreateDTOs.CreateLigneCommandeFournisseurDto"
            }
          },
          "fraisLivraison": {
            "minimum": 0,
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateDemandeCategorieDto": {
        "required": [
          "nom"
        ],
        "type": "object",
        "properties": {
          "nom": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "description": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateDemandeSousCategorieDto": {
        "required": [
          "categorieId",
          "nom"
        ],
        "type": "object",
        "properties": {
          "nom": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "description": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          },
          "categorieId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateDetailsCommandeDto": {
        "required": [
          "commandeId",
          "produitId",
          "quantite"
        ],
        "type": "object",
        "properties": {
          "commandeId": {
            "type": "integer",
            "format": "int32"
          },
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "quantite": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateDetailsCommandeSimpleDto": {
        "required": [
          "produitId",
          "quantite"
        ],
        "type": "object",
        "properties": {
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "quantite": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateFormeDto": {
        "required": [
          "categorieId",
          "nom"
        ],
        "type": "object",
        "properties": {
          "nom": {
            "minLength": 1,
            "type": "string"
          },
          "categorieId": {
            "type": "integer",
            "format": "int32"
          },
          "imageUrl": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateLigneCommandeFournisseurDto": {
        "required": [
          "prixUnitaire",
          "produitId",
          "quantite"
        ],
        "type": "object",
        "properties": {
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "quantite": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "prixUnitaire": {
            "minimum": 0,01,
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateLivraisonDto": {
        "required": [
          "adresseLivraisonId",
          "commandeId",
          "transporteur"
        ],
        "type": "object",
        "properties": {
          "transporteur": {
            "minLength": 1,
            "type": "string"
          },
          "commandeId": {
            "type": "integer",
            "format": "int32"
          },
          "adresseLivraisonId": {
            "type": "integer",
            "format": "int32"
          },
          "fournisseurId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateNotificationDto": {
        "required": [
          "message",
          "titre",
          "type",
          "utilisateurId"
        ],
        "type": "object",
        "properties": {
          "titre": {
            "maxLength": 200,
            "minLength": 3,
            "type": "string"
          },
          "message": {
            "maxLength": 500,
            "minLength": 5,
            "type": "string"
          },
          "type": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.TypeNotification"
          },
          "utilisateurId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreatePanierDto": {
        "required": [
          "clientId"
        ],
        "type": "object",
        "properties": {
          "clientId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreatePromotionDto": {
        "required": [
          "dateDebut",
          "pourcentageRemise",
          "type"
        ],
        "type": "object",
        "properties": {
          "type": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.TypePromotion"
          },
          "pourcentageRemise": {
            "maximum": 100,
            "minimum": 0,01,
            "type": "number",
            "format": "double"
          },
          "dateDebut": {
            "type": "string",
            "format": "date-time"
          },
          "dateFin": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "codePromo": {
            "maxLength": 20,
            "minLength": 4,
            "pattern": "^[A-Z0-9]+$",
            "type": "string",
            "nullable": true
          },
          "categorieId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "sousCategorieId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "marqueId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "fournisseurId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "formeId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "produitsApplicablesIds": {
            "type": "array",
            "items": {
              "type": "integer",
              "format": "int32"
            },
            "nullable": true
          },
          "appliquerSurHT": {
            "type": "boolean"
          },
          "dureeJours": {
            "type": "integer",
            "format": "int32",
            "readOnly": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.CreateRemboursementDto": {
        "required": [
          "initiateurId",
          "montant",
          "paiementId"
        ],
        "type": "object",
        "properties": {
          "paiementId": {
            "type": "integer",
            "format": "int32"
          },
          "montant": {
            "maximum": 100000,
            "minimum": 0,01,
            "type": "number",
            "format": "double"
          },
          "raison": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          },
          "initiateurId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.CreateDTOs.FavoriCreateDto": {
        "required": [
          "clientId",
          "produitId",
          "stockStatus"
        ],
        "type": "object",
        "properties": {
          "stockStatus": {
            "type": "boolean"
          },
          "clientId": {
            "type": "integer",
            "format": "int32"
          },
          "produitId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.AdresseDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "rue": {
            "type": "string",
            "nullable": true
          },
          "ville": {
            "type": "string",
            "nullable": true
          },
          "codePostal": {
            "type": "string",
            "nullable": true
          },
          "pays": {
            "type": "string",
            "nullable": true
          },
          "estPrincipale": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.AvisDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "note": {
            "maximum": 5,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "commentaire": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          },
          "datePublication": {
            "type": "string",
            "format": "date-time"
          },
          "clientNom": {
            "type": "string",
            "nullable": true
          },
          "clientPrenom": {
            "type": "string",
            "nullable": true
          },
          "clientEmail": {
            "type": "string",
            "nullable": true
          },
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "produitNom": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.CategorieDto": {
        "required": [
          "nom"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "maxLength": 100,
            "minLength": 3,
            "type": "string"
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estValidee": {
            "type": "boolean"
          },
          "sousCategoriesCount": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.ClientDto": {
        "required": [
          "email",
          "nom",
          "phoneNumber",
          "prenom",
          "role"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "email": {
            "type": "string",
            "nullable": true
          },
          "nom": {
            "type": "string",
            "nullable": true
          },
          "prenom": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "role": {
            "type": "string",
            "nullable": true
          },
          "dateNaissance": {
            "type": "string",
            "format": "date-time"
          },
          "derniereConnexion": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "estActif": {
            "type": "boolean"
          },
          "adresses": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
            },
            "nullable": true
          },
          "dateInscription": {
            "type": "string",
            "format": "date-time"
          },
          "nombreCommandes": {
            "type": "integer",
            "format": "int32"
          },
          "commandes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeDto"
            },
            "nullable": true
          },
          "avis": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
            },
            "nullable": true
          },
          "favoris": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto"
            },
            "nullable": true
          },
          "panier": {
            "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PanierDto"
          },
          "adresseLivraison": {
            "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.CommandeDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "clientId": {
            "type": "integer",
            "format": "int32"
          },
          "dateCreation": {
            "type": "string",
            "format": "date-time"
          },
          "statut": {
            "type": "string",
            "nullable": true
          },
          "montantTotal": {
            "type": "number",
            "format": "double"
          },
          "montantHT": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "montantTVA": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "fraisLivraison": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "codePromo": {
            "type": "string",
            "nullable": true
          },
          "details": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto"
            },
            "nullable": true
          },
          "promotionsUtilisees": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto"
            },
            "nullable": true
          },
          "commandesFournisseurs": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.CommandeFournisseurDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "reference": {
            "type": "string",
            "nullable": true
          },
          "commandeClientId": {
            "type": "integer",
            "format": "int32"
          },
          "fournisseurId": {
            "type": "integer",
            "format": "int32"
          },
          "nomFournisseur": {
            "type": "string",
            "nullable": true
          },
          "matriculeFiscale": {
            "type": "string",
            "nullable": true
          },
          "dateCreation": {
            "type": "string",
            "format": "date-time"
          },
          "dateLivraison": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "fraisLivraison": {
            "type": "number",
            "format": "double"
          },
          "statut": {
            "type": "string",
            "nullable": true
          },
          "numeroBonLivraison": {
            "type": "string",
            "nullable": true
          },
          "lignesCommande": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto"
            },
            "nullable": true
          },
          "montantTotal": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.DetailsCommandeDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "commandeId": {
            "type": "integer",
            "format": "int32"
          },
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "produitNom": {
            "type": "string",
            "nullable": true
          },
          "quantite": {
            "type": "integer",
            "format": "int32"
          },
          "prixUnitaireHT": {
            "type": "number",
            "format": "double"
          },
          "tauxTVA": {
            "type": "number",
            "format": "double"
          },
          "prixUnitaireTTC": {
            "type": "number",
            "format": "double"
          },
          "totalLigneTTC": {
            "type": "number",
            "format": "double"
          },
          "montantTVA": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.FavoriResponseDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "stockStatus": {
            "type": "boolean"
          },
          "clientId": {
            "type": "integer",
            "format": "int32"
          },
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "produitNom": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.FormeDto": {
        "required": [
          "categorieId",
          "imageUrl",
          "nom"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "categorieId": {
            "type": "integer",
            "format": "int32"
          },
          "categorieNom": {
            "type": "string",
            "nullable": true
          },
          "imageUrl": {
            "minLength": 1,
            "type": "string",
            "format": "uri"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.FournisseurDto": {
        "required": [
          "codeBanque",
          "email",
          "logoFile",
          "matriculeFiscale",
          "nom",
          "phoneNumber",
          "prenom",
          "raisonSociale",
          "ribMasque",
          "role"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "email": {
            "type": "string",
            "nullable": true
          },
          "nom": {
            "type": "string",
            "nullable": true
          },
          "prenom": {
            "type": "string",
            "nullable": true
          },
          "phoneNumber": {
            "type": "string",
            "nullable": true
          },
          "role": {
            "type": "string",
            "nullable": true
          },
          "dateNaissance": {
            "type": "string",
            "format": "date-time"
          },
          "dateInscription": {
            "type": "string",
            "format": "date-time"
          },
          "derniereConnexion": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "estActif": {
            "type": "boolean"
          },
          "adresses": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AdresseDto"
            },
            "nullable": true
          },
          "matriculeFiscale": {
            "type": "string",
            "nullable": true
          },
          "raisonSociale": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "ribMasque": {
            "type": "string",
            "nullable": true
          },
          "codeBanque": {
            "maxLength": 3,
            "minLength": 3,
            "pattern": "^[0-9]+$",
            "type": "string"
          },
          "commission": {
            "maximum": 1,
            "minimum": 0,5,
            "type": "number",
            "format": "double"
          },
          "delaiPreparationJours": {
            "type": "integer",
            "format": "int32"
          },
          "fraisLivraisonBase": {
            "type": "number",
            "format": "double"
          },
          "logoFile": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.FraisLivraisonDetailDto": {
        "type": "object",
        "properties": {
          "fournisseurId": {
            "type": "integer",
            "format": "int32"
          },
          "nomFournisseur": {
            "type": "string",
            "nullable": true
          },
          "fraisLivraison": {
            "type": "number",
            "format": "double"
          },
          "produitIds": {
            "type": "array",
            "items": {
              "type": "integer",
              "format": "int32"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.FraisLivraisonResponseDto": {
        "type": "object",
        "properties": {
          "fraisParFournisseur": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FraisLivraisonDetailDto"
            },
            "nullable": true
          },
          "fraisLivraisonTotal": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.ImageProduitDto": {
        "type": "object",
        "properties": {
          "imageUrl": {
            "type": "string",
            "nullable": true
          },
          "ordre": {
            "type": "integer",
            "format": "int32"
          },
          "isMain": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.ItemPanierDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "nomProduit": {
            "type": "string",
            "nullable": true
          },
          "referenceProduit": {
            "type": "string",
            "nullable": true
          },
          "quantite": {
            "type": "integer",
            "format": "int32"
          },
          "prixUnitaire": {
            "type": "number",
            "format": "double"
          },
          "prixApresPromotion": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "sousTotal": {
            "type": "number",
            "format": "double"
          },
          "dateAjout": {
            "type": "string",
            "format": "date-time"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.LigneCommandeFournisseurDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "commandeId": {
            "type": "integer",
            "format": "int32"
          },
          "produitId": {
            "type": "integer",
            "format": "int32"
          },
          "nomProduit": {
            "type": "string",
            "nullable": true
          },
          "referenceProduit": {
            "type": "string",
            "nullable": true
          },
          "quantite": {
            "type": "integer",
            "format": "int32"
          },
          "prixUnitaire": {
            "type": "number",
            "format": "double"
          },
          "totalLigne": {
            "type": "number",
            "format": "double"
          },
          "imagePrincipale": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.LivraisonDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "transporteur": {
            "type": "string",
            "nullable": true
          },
          "numeroSuivi": {
            "type": "string",
            "nullable": true
          },
          "poidsTotal": {
            "type": "number",
            "format": "double"
          },
          "commandeId": {
            "type": "integer",
            "format": "int32"
          },
          "fournisseurId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "adresseLivraisonId": {
            "type": "integer",
            "format": "int32"
          },
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.StatutLivraisonDto"
          },
          "dateChangementStatut": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.MarqueDto": {
        "required": [
          "logo",
          "name"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "logo": {
            "minLength": 1,
            "type": "string",
            "format": "uri"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.NotificationDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "contenu": {
            "type": "string",
            "nullable": true
          },
          "dateEnvoi": {
            "type": "string",
            "format": "date-time"
          },
          "estLue": {
            "type": "boolean"
          },
          "utilisateurId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.PaiementDto": {
        "type": "object",
        "properties": {
          "commandeId": {
            "type": "integer",
            "format": "int32"
          },
          "tokenCarte": {
            "type": "string",
            "nullable": true
          },
          "montant": {
            "type": "number",
            "format": "double"
          },
          "methode": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.PaiementResponseDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "transactionId": {
            "type": "string",
            "nullable": true
          },
          "transactionIdMasked": {
            "type": "string",
            "nullable": true,
            "readOnly": true
          },
          "montant": {
            "type": "number",
            "format": "double"
          },
          "statut": {
            "type": "string",
            "nullable": true
          },
          "dateCreation": {
            "type": "string",
            "format": "date-time"
          },
          "methode": {
            "type": "string",
            "nullable": true
          },
          "commandeId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.PanierDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "clientId": {
            "type": "integer",
            "format": "int32"
          },
          "nomClient": {
            "type": "string",
            "nullable": true
          },
          "dateCreation": {
            "type": "string",
            "format": "date-time"
          },
          "estActif": {
            "type": "boolean"
          },
          "codePromoApplique": {
            "type": "string",
            "nullable": true
          },
          "total": {
            "type": "number",
            "format": "double"
          },
          "items": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ItemPanierDto"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.ProcessRemboursementDto": {
        "required": [
          "nouveauStatut"
        ],
        "type": "object",
        "properties": {
          "nouveauStatut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutRemboursement"
          },
          "commentaire": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.ProduitDto": {
        "required": [
          "codeABarre",
          "nom",
          "referenceOriginal"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "referenceOriginal": {
            "type": "string",
            "nullable": true
          },
          "referenceFournisseur": {
            "type": "string",
            "nullable": true
          },
          "codeABarre": {
            "type": "string",
            "nullable": true
          },
          "nom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "prixAchatHT": {
            "type": "number",
            "format": "double"
          },
          "prixVenteHT": {
            "type": "number",
            "format": "double"
          },
          "prixVenteTTC": {
            "type": "number",
            "format": "double"
          },
          "prixApresRemises": {
            "type": "number",
            "format": "double"
          },
          "prixApresRemisesOutlet": {
            "type": "number",
            "format": "double"
          },
          "prixApresAutresPromotions": {
            "type": "number",
            "format": "double"
          },
          "pourcentageRemiseTotale": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "dateAjout": {
            "type": "string",
            "format": "date-time"
          },
          "estNouveau": {
            "type": "boolean",
            "readOnly": true
          },
          "tauxTVAId": {
            "type": "integer",
            "format": "int32"
          },
          "stock": {
            "type": "integer",
            "format": "int32"
          },
          "sousCategorieId": {
            "type": "integer",
            "format": "int32"
          },
          "marqueId": {
            "type": "integer",
            "format": "int32"
          },
          "formeId": {
            "type": "integer",
            "format": "int32"
          },
          "fournisseurId": {
            "type": "integer",
            "format": "int32"
          },
          "noteMoyenne": {
            "type": "number",
            "format": "double"
          },
          "nombreAvis": {
            "type": "integer",
            "format": "int32"
          },
          "avis": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.AvisDto"
            },
            "nullable": true
          },
          "marque": {
            "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.MarqueDto"
          },
          "forme": {
            "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FormeDto"
          },
          "fournisseur": {
            "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.FournisseurDto"
          },
          "sousCategorie": {
            "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.SousCategorieDto"
          },
          "images": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.ImageProduitDto"
            },
            "nullable": true
          },
          "imagePrincipaleUrl": {
            "type": "string",
            "nullable": true,
            "readOnly": true
          },
          "promotions": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.PromotionDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "type": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.TypePromotion"
          },
          "pourcentageRemise": {
            "type": "number",
            "format": "double"
          },
          "dateDebut": {
            "type": "string",
            "format": "date-time"
          },
          "dateFin": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "codePromo": {
            "type": "string",
            "nullable": true
          },
          "categorieId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "sousCategorieId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "marqueId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "fournisseurId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "formeId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "appliquerSurHT": {
            "type": "boolean"
          },
          "estValide": {
            "type": "boolean"
          },
          "produitsApplicablesIds": {
            "type": "array",
            "items": {
              "type": "integer",
              "format": "int32"
            },
            "nullable": true
          },
          "nomAffichage": {
            "type": "string",
            "nullable": true,
            "readOnly": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.PromotionUtiliseeDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "promotionId": {
            "type": "integer",
            "format": "int32"
          },
          "commandeId": {
            "type": "integer",
            "format": "int32"
          },
          "dateUtilisation": {
            "type": "string",
            "format": "date-time"
          },
          "montantEconomise": {
            "type": "number",
            "format": "double"
          },
          "codePromoUtilise": {
            "type": "string",
            "nullable": true
          },
          "promotion": {
            "$ref": "#/components/schemas/WebApiPfe.DTOs.ReadDTOs.PromotionDto"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.RemboursementDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "remboursementId": {
            "type": "string",
            "nullable": true
          },
          "paiementId": {
            "type": "integer",
            "format": "int32"
          },
          "montant": {
            "type": "number",
            "format": "double"
          },
          "dateDemande": {
            "type": "string",
            "format": "date-time"
          },
          "dateTraitement": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutRemboursement"
          },
          "raison": {
            "type": "string",
            "nullable": true
          },
          "initiateurId": {
            "type": "string",
            "nullable": true
          },
          "commandeId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.SousCategorieDto": {
        "required": [
          "categorieId",
          "nom"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "categorieId": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "categorieNom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estValidee": {
            "type": "boolean"
          },
          "produitsCount": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.SousCategorieDto.Create": {
        "required": [
          "categorieId",
          "nom"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "categorieId": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "categorieNom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estValidee": {
            "type": "boolean"
          },
          "produitsCount": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.SousCategorieDto.Update": {
        "required": [
          "categorieId",
          "nom"
        ],
        "type": "object",
        "properties": {
          "nom": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "categorieId": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "categorieNom": {
            "type": "string",
            "nullable": true
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estValidee": {
            "type": "boolean"
          },
          "produitsCount": {
            "type": "integer",
            "format": "int32"
          },
          "id": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.StatutLivraisonDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "code": {
            "type": "string",
            "nullable": true
          },
          "libelle": {
            "type": "string",
            "nullable": true
          },
          "estTerminal": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.TauxTVADto": {
        "required": [
          "categorieId",
          "dateEffet",
          "estActif",
          "libelle",
          "taux"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "libelle": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "taux": {
            "maximum": 99,99,
            "minimum": 0,01,
            "type": "number",
            "format": "double"
          },
          "categorieId": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estActif": {
            "type": "boolean"
          },
          "dateEffet": {
            "type": "string",
            "format": "date-time"
          },
          "dateFin": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.ReadDTOs.TauxTVADto.Create": {
        "required": [
          "categorieId",
          "dateEffet",
          "estActif",
          "libelle",
          "taux"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "libelle": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "taux": {
            "maximum": 99,99,
            "minimum": 0,01,
            "type": "number",
            "format": "double"
          },
          "categorieId": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estActif": {
            "type": "boolean"
          },
          "dateEffet": {
            "type": "string",
            "format": "date-time"
          },
          "dateFin": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.AdresseUpdateDto": {
        "type": "object",
        "properties": {
          "rue": {
            "type": "string",
            "nullable": true
          },
          "ville": {
            "type": "string",
            "nullable": true
          },
          "codePostal": {
            "pattern": "^\\d{4}$",
            "type": "string",
            "nullable": true
          },
          "pays": {
            "type": "string",
            "nullable": true
          },
          "estPrincipale": {
            "type": "boolean",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.ChangeStatutLivraisonDto": {
        "required": [
          "nouveauStatutId"
        ],
        "type": "object",
        "properties": {
          "nouveauStatutId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.ClientProfileUpdateDto": {
        "required": [
          "email",
          "nom",
          "phoneNumber",
          "prenom"
        ],
        "type": "object",
        "properties": {
          "email": {
            "minLength": 1,
            "type": "string",
            "format": "email"
          },
          "nom": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "prenom": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "phoneNumber": {
            "minLength": 1,
            "type": "string",
            "format": "tel"
          },
          "dateNaissance": {
            "type": "string",
            "format": "date-time"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.ClientUpdateDto": {
        "required": [
          "email",
          "estActif",
          "nom",
          "password",
          "phoneNumber",
          "prenom"
        ],
        "type": "object",
        "properties": {
          "email": {
            "minLength": 1,
            "type": "string",
            "format": "email"
          },
          "nom": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "prenom": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "phoneNumber": {
            "minLength": 1,
            "type": "string",
            "format": "tel"
          },
          "dateNaissance": {
            "type": "string",
            "format": "date-time"
          },
          "password": {
            "maxLength": 100,
            "minLength": 8,
            "type": "string"
          },
          "estActif": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.FournisseurUpdateDto": {
        "type": "object",
        "properties": {
          "description": {
            "type": "string",
            "nullable": true
          },
          "commission": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "fraisLivraisonBase": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "delaiPreparationJours": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "logo": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.ImageProduitUpdateDto": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "ordre": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "isMain": {
            "type": "boolean",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.ProduitPrixUpdateDto": {
        "type": "object",
        "properties": {
          "nouveauPrixHT": {
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.ProduitStockUpdateDto": {
        "required": [
          "id",
          "stock"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "stock": {
            "maximum": 2147483647,
            "minimum": 0,
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.TraiterDemandeDto": {
        "required": [
          "statut"
        ],
        "type": "object",
        "properties": {
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutDemande"
          },
          "commentaireAdmin": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.UpdateCategorieDto": {
        "required": [
          "id",
          "nom"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "maxLength": 100,
            "minLength": 3,
            "type": "string"
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estValidee": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.UpdateDetailsCommandeDto": {
        "type": "object",
        "properties": {
          "quantite": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.UpdateItemPanierDto": {
        "type": "object",
        "properties": {
          "quantite": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "prixApresPromotion": {
            "minimum": 0,
            "type": "number",
            "format": "double",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.UpdateLigneCommandeFournisseurDto": {
        "type": "object",
        "properties": {
          "quantite": {
            "maximum": 2147483647,
            "minimum": 1,
            "type": "integer",
            "format": "int32"
          },
          "prixUnitaire": {
            "minimum": 0,01,
            "type": "number",
            "format": "double"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.UpdateLivraisonDto": {
        "type": "object",
        "properties": {
          "numeroSuivi": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string",
            "nullable": true
          },
          "poidsTotal": {
            "maximum": 100,
            "minimum": 0,1,
            "type": "number",
            "format": "double",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.UpdatePanierDto": {
        "type": "object",
        "properties": {
          "estActif": {
            "type": "boolean",
            "nullable": true
          },
          "codePromoApplique": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.UpdateRemboursementDto": {
        "type": "object",
        "properties": {
          "raison": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          },
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutRemboursement"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.DTOs.UpdateDTOs.UpdateStatutCommandeFournisseurDto": {
        "required": [
          "statut"
        ],
        "type": "object",
        "properties": {
          "statut": {
            "$ref": "#/components/schemas/WebApiPfe.Models.Enum.StatutCommandeFournisseur"
          },
          "numeroBonLivraison": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.Models.DTOs.CalculerFraisLivraisonRequest": {
        "type": "object",
        "properties": {
          "adresseId": {
            "type": "integer",
            "format": "int32"
          },
          "produitIds": {
            "type": "array",
            "items": {
              "type": "integer",
              "format": "int32"
            },
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.Models.Entity.Categorie": {
        "required": [
          "nom"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "minLength": 1,
            "type": "string"
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estValidee": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.Models.Entity.Forme": {
        "required": [
          "categorieId",
          "imageUrl",
          "nom"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "maxLength": 100,
            "minLength": 1,
            "type": "string"
          },
          "categorieId": {
            "type": "integer",
            "format": "int32"
          },
          "imageUrl": {
            "maxLength": 500,
            "minLength": 1,
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.Models.Entity.Marque": {
        "required": [
          "logo",
          "name"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "name": {
            "maxLength": 100,
            "minLength": 1,
            "type": "string"
          },
          "logo": {
            "maxLength": 500,
            "minLength": 1,
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.Models.Entity.SousCategorie": {
        "required": [
          "categorieId",
          "nom"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "nom": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estValidee": {
            "type": "boolean"
          },
          "categorieId": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.Models.Entity.StatutAvis": {
        "enum": [
          1,
          2,
          3,
          4
        ],
        "type": "integer",
        "format": "int32"
      },
      "WebApiPfe.Models.Entity.TauxTVA": {
        "required": [
          "dateEffet",
          "estActif",
          "libelle",
          "taux"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "integer",
            "format": "int32"
          },
          "libelle": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "taux": {
            "type": "number",
            "format": "double"
          },
          "description": {
            "type": "string",
            "nullable": true
          },
          "estActif": {
            "type": "boolean"
          },
          "dateEffet": {
            "type": "string",
            "format": "date-time"
          },
          "dateFin": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "categorieId": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "WebApiPfe.Models.Enum.StatutCommandeFournisseur": {
        "enum": [
          0,
          1,
          2,
          3,
          4,
          5,
          6
        ],
        "type": "integer",
        "format": "int32"
      },
      "WebApiPfe.Models.Enum.StatutDemande": {
        "enum": [
          0,
          1,
          2
        ],
        "type": "integer",
        "format": "int32"
      },
      "WebApiPfe.Models.Enum.StatutRemboursement": {
        "enum": [
          0,
          1,
          2,
          3
        ],
        "type": "integer",
        "format": "int32"
      },
      "WebApiPfe.Models.Enum.TypeNotification": {
        "enum": [
          1,
          2,
          3,
          4,
          5,
          6,
          7,
          8,
          9,
          10
        ],
        "type": "integer",
        "format": "int32"
      },
      "WebApiPfe.Models.Enum.TypePromotion": {
        "enum": [
          0,
          1,
          2
        ],
        "type": "integer",
        "format": "int32"
      }
    }
  }
}