﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WebApiPfe.Models.Entity
{

    [Table("Avis")]
    public class Avis
    {
            [Key][DatabaseGenerated(DatabaseGeneratedOption.Identity)] public int Id { get; set; }
            [Required] [Range(1, 5)] public int Note { get; set; }
            [StringLength(1000)]
            public string Commentaire { get; set; } = string.Empty;

            [Required]
            public DateTime DatePublication { get; set; } = DateTime.UtcNow;

            // Champs pour la modération
            public StatutAvis Statut { get; set; } = StatutAvis.Publie;

            // Indique si le commentaire a été supprimé par l'admin
            public bool CommentaireSupprime { get; set; } = false;

            public DateTime? DateModeration { get; set; }

            public int? ModerePar { get; set; }

            [StringLength(500)]
            public string? CommentaireModeration { get; set; }

            [Required]
            public int ProduitId { get; set; }

            [ForeignKey(nameof(ProduitId))]
            public virtual Produit Produit { get; set; } = null!;

            [Required]
            public int ClientId { get; set; }

            [ForeignKey(nameof(ClientId))]
            public virtual Client Client { get; set; } = null!;

            [ForeignKey(nameof(ModerePar))]
            public virtual Utilisateur? Moderateur { get; set; }
     }

    public enum StatutAvis
    {
        Publie = 1,           // Avis publié automatiquement
        CommentaireSupprime = 2, // Commentaire supprimé par admin, note conservée
        Signale = 3           // Avis signalé pour révision
    }
}


