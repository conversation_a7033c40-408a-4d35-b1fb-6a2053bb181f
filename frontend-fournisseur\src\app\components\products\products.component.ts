import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ProduitService } from '../../services/produit.service';
import { CategorieService } from '../../services/categorie.service';
import { SousCategorieService } from '../../services/sous-categorie.service';
import { MarqueService } from '../../services/marque.service';
import { FormeService } from '../../services/forme.service';
import { TauxTVAService } from '../../services/taux-tva.service';
import { AuthService } from '../../services/auth.service';
import { ImageUrlService } from '../../services/image-url.service';

import {
  Produit,
  ProduitCreate,
  ProduitUpdate,
  CategorieDropdown,
  SousCategorieDropdown,
  MarqueDropdown,
  FormeDropdown,
  TauxTVADropdown
} from '../../models';

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.scss']
})
export class ProductsComponent implements OnInit {
  // Signaux pour la gestion d'état
  products = signal<Produit[]>([]);
  isLoading = signal(false);
  error = signal<string | null>(null);
  showForm = signal(false);
  isEditMode = signal(false);
  selectedProduct = signal<Produit | null>(null);
  searchQuery = signal('');

  // Dropdowns
  categoriesDropdown = signal<CategorieDropdown[]>([]);
  sousCategoriesDropdown = signal<SousCategorieDropdown[]>([]);
  marquesDropdown = signal<MarqueDropdown[]>([]);
  formesDropdown = signal<FormeDropdown[]>([]);
  tauxTVADropdown = signal<TauxTVADropdown[]>([]);

  // Gestion des images
  selectedImageFiles: File[] = [];
  imagePreviewUrls: string[] = [];

  // Valeurs du formulaire pour ngModel
  formData = {
    referenceOriginal: '',
    referenceFournisseur: '',
    codeABarre: '',
    nom: '',
    description: '',
    prixAchat: '',
    prixVente: '',
    stock: '',
    pourcentageRemise: '0',
    categorieId: '',
    sousCategorieId: '',
    marqueId: '',
    formeId: '',
    tauxTVAId: ''
  };

  // Calcul des prix
  calculatedPrixTTC: number = 0;
  selectedTauxTVA: number = 0;

  // Pagination
  currentPage = signal(1);
  itemsPerPage = 12;

  // Affichage des détails
  showDetails = signal(false);
  selectedProductDetails = signal<Produit | null>(null);
  loadingDetails = signal(false);

  constructor(
    private produitService: ProduitService,
    private categorieService: CategorieService,
    private sousCategorieService: SousCategorieService,
    private marqueService: MarqueService,
    private formeService: FormeService,
    private tauxTVAService: TauxTVAService,
    private authService: AuthService,
    public imageUrlService: ImageUrlService
  ) {}

  ngOnInit(): void {
    console.log('🚀 Initialisation du composant ProductsComponent');
    this.testBackendConnection();
    this.loadData();
    this.loadDropdowns();
  }

  testBackendConnection(): void {
    console.log('🔗 Test de connexion au backend...');

    // Test simple avec les catégories
    this.categorieService.getDropdown().subscribe({
      next: (response) => {
        console.log('✅ Backend accessible - Catégories:', response);
      },
      error: (error) => {
        console.error('❌ Backend inaccessible:', error);
        console.error('❌ Status:', error.status);
        console.error('❌ Message:', error.message);
        console.error('❌ URL:', error.url);
      }
    });
  }

  // Computed properties
  stats = computed(() => {
    const products = this.products();
    return {
      total: products.length,
      active: products.filter(p => p.stock > 0).length,
      outOfStock: products.filter(p => p.stock === 0).length,
      totalValue: products.reduce((sum, p) => {
        // Utiliser le prix avec promotions si disponible, sinon le prix TTC normal
        const prixFinal = p.prixApresRemises || p.prixVenteTTC;
        return sum + (prixFinal * p.stock);
      }, 0)
    };
  });

  filteredProducts = computed(() => {
    const products = this.products();
    const query = this.searchQuery().toLowerCase();
    
    if (!query) return products;
    
    return products.filter(product =>
      product.nom.toLowerCase().includes(query) ||
      product.description?.toLowerCase().includes(query) ||
      product.referenceOriginal.toLowerCase().includes(query)
    );
  });

  currentPageProducts = computed(() => {
    const filtered = this.filteredProducts();
    const start = (this.currentPage() - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    return filtered.slice(start, end);
  });

  // Méthodes de chargement des données
  loadData(): void {
    this.isLoading.set(true);
    this.error.set(null);

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser?.id) {
      this.error.set('Utilisateur non connecté');
      this.isLoading.set(false);
      return;
    }

    // Charger seulement les produits du fournisseur connecté
    this.produitService.getByFournisseur(currentUser.id).subscribe({
      next: (products) => {
        this.products.set(products);
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des produits:', error);
        this.error.set('Erreur lors du chargement des produits');
        this.isLoading.set(false);
      }
    });
  }

  loadDropdowns(): void {
    console.log('🔄 Début du chargement des dropdowns...');

    // Charger les catégories
    console.log('📋 Chargement des catégories...');
    this.categorieService.getAll().subscribe({
      next: (categories) => {
        console.log('✅ Catégories reçues:', categories);
        // Le backend retourne directement les objets avec les bonnes propriétés
        const categoriesFormatted = categories.map((cat: any) => ({
          id: cat.id,
          nom: cat.nom,
          sousCategoriesCount: cat.sousCategoriesCount || 0
        }));
        console.log('✅ Catégories formatées:', categoriesFormatted);
        this.categoriesDropdown.set(categoriesFormatted);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des catégories:', error);
        this.categoriesDropdown.set([]);
      }
    });

    // Charger les marques
    console.log('📋 Chargement des marques...');
    this.marqueService.getAll().subscribe({
      next: (marques) => {
        console.log('✅ Marques reçues:', marques);
        // Le backend retourne des objets avec 'name' et 'logo'
        const marquesFormatted = marques.map((marque: any) => ({
          id: marque.id,
          name: marque.name,
          logo: marque.logo || ''
        }));
        console.log('✅ Marques formatées:', marquesFormatted);
        this.marquesDropdown.set(marquesFormatted);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des marques:', error);
        this.marquesDropdown.set([]);
      }
    });

    // Charger les formes
    console.log('📋 Chargement des formes...');
    this.formeService.getAll().subscribe({
      next: (formes) => {
        console.log('✅ Formes reçues:', formes);
        // Le backend retourne des objets avec 'nom', 'categorieId' et 'imageUrl'
        const formesFormatted = formes.map((forme: any) => ({
          id: forme.id,
          nom: forme.nom,
          categorieId: forme.categorieId,
          imageUrl: forme.imageUrl || ''
        }));
        console.log('✅ Formes formatées:', formesFormatted);
        this.formesDropdown.set(formesFormatted);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des formes:', error);
        this.formesDropdown.set([]);
      }
    });

    // Charger les taux TVA
    console.log('📋 Chargement des taux TVA...');
    this.tauxTVAService.getAll().subscribe({
      next: (taux) => {
        console.log('✅ Taux TVA reçus:', taux);
        // Le backend retourne des objets avec 'libelle', 'taux' et 'estActif'
        const tauxFormatted = taux.map((tva: any) => ({
          id: tva.id,
          libelle: tva.libelle,
          taux: tva.taux,
          estActif: tva.estActif
        }));
        console.log('✅ Taux TVA formatés:', tauxFormatted);
        this.tauxTVADropdown.set(tauxFormatted);
        console.log('🔍 Signal tauxTVADropdown après set:', this.tauxTVADropdown());
        console.log('📊 Nombre de taux dans le signal:', this.tauxTVADropdown().length);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des taux TVA:', error);
        this.tauxTVADropdown.set([]);
      }
    });

    console.log('🔄 Fin de l\'initialisation du chargement des dropdowns');
  }

  // Méthodes d'interface utilisateur
  onSearch(event: any): void {
    this.searchQuery.set(event.target.value);
    this.currentPage.set(1);
  }

  openAddForm(): void {
    this.isEditMode.set(false);
    this.selectedProduct.set(null);
    this.showForm.set(true);
    this.clearImageSelection();
    this.resetFormData();

    // Test direct de l'API
    console.log('🧪 Test direct de l\'API Taux TVA...');
    this.tauxTVAService.getAll().subscribe({
      next: (taux) => {
        console.log('🧪 Test direct réussi - Taux reçus:', taux);
      },
      error: (error) => {
        console.error('🧪 Test direct échoué:', error);
      }
    });

    // Forcer le rechargement des dropdowns si ils sont vides
    if (this.categoriesDropdown().length === 0 || this.tauxTVADropdown().length === 0) {
      console.log('⚠️ Dropdowns vides, rechargement forcé...');
      this.loadDropdowns();
    }

    // Afficher l'état des dropdowns
    console.log('📊 État des dropdowns à l\'ouverture du formulaire:');
    console.log('- Taux TVA disponibles:', this.tauxTVADropdown().length);
    console.log('- Détail des taux TVA:', this.tauxTVADropdown());
    console.log('- Catégories:', this.categoriesDropdown());
    console.log('- Marques:', this.marquesDropdown());
    console.log('- Formes:', this.formesDropdown());
    console.log('- Taux TVA:', this.tauxTVADropdown());
  }

  resetFormData(): void {
    this.formData = {
      referenceOriginal: '',
      referenceFournisseur: '',
      codeABarre: '',
      nom: '',
      description: '',
      prixAchat: '',
      prixVente: '',
      stock: '',
      pourcentageRemise: '0',
      categorieId: '',
      sousCategorieId: '',
      marqueId: '',
      formeId: '',
      tauxTVAId: ''
    };
    console.log('🔄 Formulaire réinitialisé');
  }

  editProduct(product: Produit): void {
    console.log('🔧 Début de l\'édition du produit:', product);
    console.log('🔧 ID du produit:', product.id);
    console.log('🔧 Nom du produit:', product.nom);

    // Définir le mode édition
    this.isEditMode.set(true);
    console.log('✅ Mode édition activé:', this.isEditMode());

    // Afficher le formulaire
    this.showForm.set(true);
    console.log('✅ Formulaire affiché:', this.showForm());

    // Forcer le rechargement des dropdowns si nécessaire
    if (this.categoriesDropdown().length === 0) {
      console.log('⚠️ Dropdowns vides, rechargement...');
      this.loadDropdowns();
    }

    // Charger les détails complets du produit depuis l'API
    console.log('🔄 Chargement des détails complets du produit depuis l\'API...');
    this.produitService.getById(product.id).subscribe({
      next: (productDetails) => {
        console.log('✅ Détails complets du produit reçus:', productDetails);

        // Sélectionner le produit avec tous les détails
        this.selectedProduct.set(productDetails);
        console.log('✅ Produit complet sélectionné:', this.selectedProduct());

        // Charger les données dans le formulaire
        setTimeout(() => {
          console.log('📝 Chargement des données complètes dans le formulaire...');
          this.loadProductDataIntoForm(productDetails);
        }, 200);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des détails du produit:', error);
        // En cas d'erreur, utiliser les données de base
        this.selectedProduct.set(product);
        setTimeout(() => {
          this.loadProductDataIntoForm(product);
        }, 200);
      }
    });
  }

  loadProductDataIntoForm(product: Produit): void {
    console.log('📝 Début du chargement des données du produit dans le formulaire:', product);
    console.log('📝 Données du produit à charger:');
    console.log('- ID:', product.id);
    console.log('- Nom:', product.nom);
    console.log('- Prix achat HT:', product.prixAchatHT);
    console.log('- Prix vente HT:', product.prixVenteHT);
    console.log('- Stock:', product.stock);
    console.log('- Marque ID:', product.marqueId);
    console.log('- Forme ID:', product.formeId);
    console.log('- Taux TVA ID:', product.tauxTVAId);
    console.log('- Sous-catégorie ID:', product.sousCategorieId);

    // Attendre que le formulaire soit rendu
    setTimeout(() => {
      console.log('📝 Remplissage des champs du formulaire avec formData...');

      // Pré-remplir les champs de base avec formData
      console.log('📝 Remplissage des champs de base...');
      this.formData.referenceOriginal = product.referenceOriginal;
      console.log('- Référence originale:', this.formData.referenceOriginal);

      this.formData.referenceFournisseur = product.referenceFournisseur || '';
      console.log('- Référence fournisseur:', this.formData.referenceFournisseur);

      this.formData.codeABarre = product.codeABarre;
      console.log('- Code à barres:', this.formData.codeABarre);

      this.formData.nom = product.nom;
      console.log('- Nom:', this.formData.nom);

      this.formData.description = product.description || '';
      console.log('- Description:', this.formData.description);

      this.formData.prixAchat = product.prixAchatHT.toString();
      console.log('- Prix achat:', this.formData.prixAchat);

      this.formData.prixVente = product.prixVenteHT.toString();
      console.log('- Prix vente:', this.formData.prixVente);

      this.formData.stock = product.stock.toString();
      console.log('- Stock:', this.formData.stock);

      this.formData.pourcentageRemise = product.pourcentageRemiseTotale?.toString() || '0';
      console.log('- Pourcentage remise:', this.formData.pourcentageRemise);

      // Pré-sélectionner les dropdowns avec formData
      console.log('📝 Remplissage des dropdowns...');
      this.formData.marqueId = product.marqueId.toString();
      console.log('- Marque ID:', this.formData.marqueId);

      this.formData.formeId = product.formeId.toString();
      console.log('- Forme ID:', this.formData.formeId);

      this.formData.tauxTVAId = product.tauxTVAId.toString();
      console.log('- Taux TVA ID:', this.formData.tauxTVAId);

      // Charger la catégorie et sous-catégorie
      console.log('📋 Chargement des catégories et sous-catégories...');
      console.log('- Sous-catégorie du produit:', product.sousCategorie);
      console.log('- ID sous-catégorie:', product.sousCategorieId);

      if (product.sousCategorie?.categorieId || product.sousCategorieId) {
        // Utiliser l'ID de catégorie de la sous-catégorie ou chercher dans les données
        const categorieId = product.sousCategorie?.categorieId;
        if (categorieId) {
          this.formData.categorieId = categorieId.toString();
          console.log('✅ Catégorie sélectionnée:', this.formData.categorieId);

          // Charger les sous-catégories pour cette catégorie
          this.loadSousCategories(categorieId);

          // Attendre que les sous-catégories soient chargées
          setTimeout(() => {
            this.formData.sousCategorieId = product.sousCategorieId.toString();
            console.log('✅ Sous-catégorie sélectionnée:', this.formData.sousCategorieId);
          }, 300);
        } else {
          // Si pas de catégorie dans sousCategorie, essayer de la trouver
          console.log('⚠️ Pas de categorieId dans sousCategorie, recherche...');
          this.formData.sousCategorieId = product.sousCategorieId.toString();
        }
      } else {
        console.log('⚠️ Aucune sous-catégorie trouvée pour ce produit');
      }

      // Mettre à jour le calcul du prix TTC
      this.selectedTauxTVA = this.tauxTVADropdown().find(t => t.id === product.tauxTVAId)?.taux || 0;
      this.calculatePrixTTC();

      console.log('✅ Données du produit chargées dans le formulaire');
      console.log('📊 État final de formData:', this.formData);
    }, 200);
  }



  private loadSousCategories(categorieId: number): void {
    this.sousCategorieService.getByCategorie(categorieId).subscribe({
      next: (sousCategories) => {
        console.log('✅ Sous-catégories chargées pour édition:', sousCategories);
        const sousCategoriesArray = Array.isArray(sousCategories) ? sousCategories : [];
        this.sousCategoriesDropdown.set(sousCategoriesArray);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des sous-catégories pour édition:', error);
        this.sousCategoriesDropdown.set([]);
      }
    });
  }

  closeForm(): void {
    this.showForm.set(false);
    this.isEditMode.set(false);
    this.selectedProduct.set(null);
    this.clearImageSelection();
    this.resetFormData();
  }

  viewProductDetails(product: Produit): void {
    console.log('👁️ Chargement des détails complets du produit:', product.id);

    this.loadingDetails.set(true);

    // Charger les détails complets du produit avec toutes les relations
    this.produitService.getById(product.id).subscribe({
      next: (productDetails) => {
        console.log('✅ Détails complets du produit chargés:', productDetails);
        console.log('📊 Relations chargées:');
        console.log('- Marque:', productDetails.marque);
        console.log('- Forme:', productDetails.forme);
        console.log('- Sous-catégorie:', productDetails.sousCategorie);
        console.log('- Taux TVA:', productDetails.tauxTVA);

        this.selectedProductDetails.set(productDetails);
        this.showDetails.set(true);
        this.loadingDetails.set(false);
      },
      error: (error) => {
        console.error('❌ Erreur lors du chargement des détails du produit:', error);
        // En cas d'erreur, utiliser les données de base
        this.selectedProductDetails.set(product);
        this.showDetails.set(true);
        this.loadingDetails.set(false);
      }
    });
  }

  closeDetails(): void {
    this.showDetails.set(false);
    this.selectedProductDetails.set(null);
  }

  deleteProduct(product: Produit): void {
    if (confirm(`Êtes-vous sûr de vouloir supprimer "${product.nom}" ?`)) {
      this.produitService.delete(product.id).subscribe({
        next: () => {
          this.loadData();
          alert('Produit supprimé avec succès');
        },
        error: () => alert('Erreur lors de la suppression')
      });
    }
  }

  // Méthodes utilitaires
  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(price);
  }

  getFinalPrice(product: Produit): number {
    // Retourner le prix avec promotions si disponible, sinon le prix TTC normal
    return product.prixApresRemises || product.prixVenteTTC;
  }

  hasPromotion(product: Produit): boolean {
    return product.prixApresRemises !== undefined && product.prixApresRemises < product.prixVenteTTC;
  }

  getStatusClass(product: Produit): string {
    if (product.stock === 0) return 'status-out-of-stock';
    if (product.stock <= 5) return 'status-low-stock';
    return 'status-in-stock';
  }

  getStatusText(product: Produit): string {
    if (product.stock === 0) return 'Rupture';
    if (product.stock <= 5) return 'Stock faible';
    return 'En stock';
  }

  // Méthodes pour les images
  clearImageSelection(): void {
    this.selectedImageFiles = [];
    this.imagePreviewUrls = [];
  }

  onImageFilesSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedImageFiles = Array.from(input.files);
      this.generateImagePreviews();
    }
  }

  onSingleImageSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (file) {
      console.log('📷 Nouvelle image sélectionnée:', file.name);

      // Vérifier la limite de 10 images
      if (this.selectedImageFiles.length >= 10) {
        alert('Vous ne pouvez pas ajouter plus de 10 images.');
        return;
      }

      // Ajouter la nouvelle image à la liste
      this.selectedImageFiles.push(file);

      // Générer la prévisualisation pour cette nouvelle image
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          this.imagePreviewUrls.push(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);

      console.log('✅ Image ajoutée. Total:', this.selectedImageFiles.length);

      // Réinitialiser l'input file pour permettre de sélectionner la même image à nouveau
      input.value = '';
    }
  }

  /**
   * Gérer la sélection multiple d'images
   */
  onMultipleImagesSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const files = Array.from(input.files || []) as File[];

    if (files.length === 0) return;

    console.log('📷 Nouvelles images sélectionnées:', files.length);

    // Vérifier la limite totale de 10 images
    const totalImages = this.selectedImageFiles.length + files.length;
    if (totalImages > 10) {
      const imagesRestantes = 10 - this.selectedImageFiles.length;
      alert(`Vous ne pouvez ajouter que ${imagesRestantes} image(s) supplémentaire(s). Limite de 10 images au total.`);
      return;
    }

    // Ajouter toutes les nouvelles images
    files.forEach((file, index) => {
      console.log(`📷 Ajout de l'image ${index + 1}:`, file.name);

      // Ajouter le fichier à la liste
      this.selectedImageFiles.push(file);

      // Générer la prévisualisation
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          this.imagePreviewUrls.push(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    });

    console.log('✅ Images ajoutées. Total:', this.selectedImageFiles.length);

    // Réinitialiser l'input pour permettre de sélectionner d'autres images
    input.value = '';
  }

  generateImagePreviews(): void {
    this.imagePreviewUrls = [];
    this.selectedImageFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          this.imagePreviewUrls.push(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    });
  }

  getImagePreview(file: File): string {
    const index = this.selectedImageFiles.indexOf(file);
    return this.imagePreviewUrls[index] || '';
  }

  removeImageFile(index: number): void {
    this.selectedImageFiles.splice(index, 1);
    this.imagePreviewUrls.splice(index, 1);
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img && !img.src.includes('data:image')) {
      console.log('❌ Erreur de chargement d\'image:', img.src);
      img.src = this.imageUrlService.getPlaceholderUrl();
    }
  }

  getMainProductImage(product: Produit): string {
    if (product.images?.length > 0) {
      // Chercher l'image principale
      const mainImage = product.images.find(img => img.isMain);
      const imageUrl = mainImage?.imageUrl || product.images[0].imageUrl;
      return this.imageUrlService.getProduitImageUrl(imageUrl);
    }

    // Si pas d'images, utiliser le placeholder
    return this.imageUrlService.getPlaceholderUrl();
  }

  // Méthodes pour le calcul des prix
  onPrixChange(): void {
    this.calculatePrixTTC();
  }

  /**
   * Sélectionner automatiquement le premier taux TVA disponible
   */
  private selectionnerPremierTauxTVA(): void {
    const tauxDisponibles = this.tauxTVADropdown();
    console.log('🎯 Sélection automatique du taux TVA...');
    console.log('📋 Taux disponibles:', tauxDisponibles);

    if (tauxDisponibles.length > 0) {
      const premierTaux = tauxDisponibles[0];
      this.formData.tauxTVAId = premierTaux.id.toString(); // Convertir en string
      this.selectedTauxTVA = premierTaux.taux;
      console.log('✅ Taux TVA sélectionné automatiquement:', premierTaux);
      this.calculatePrixTTC();
    } else {
      console.log('⚠️ Aucun taux TVA disponible');
    }
  }

  /**
   * Forcer le rechargement des taux TVA
   */
  forceReloadTauxTVA(): void {
    console.log('🔄 Rechargement forcé des taux TVA...');
    this.tauxTVAService.getAll().subscribe({
      next: (taux) => {
        console.log('✅ Taux TVA rechargés:', taux);
        const tauxFormatted = taux.map((tva: any) => ({
          id: tva.id,
          libelle: tva.libelle,
          taux: tva.taux,
          estActif: tva.estActif
        }));
        this.tauxTVADropdown.set(tauxFormatted);
        console.log('📊 Signal mis à jour:', this.tauxTVADropdown());
      },
      error: (error) => {
        console.error('❌ Erreur rechargement taux TVA:', error);
      }
    });
  }

  onTauxTVAChange(event: any): void {
    const tauxTVAId = +event.target.value;
    const selectedTaux = this.tauxTVADropdown().find(t => t.id === tauxTVAId);
    this.selectedTauxTVA = selectedTaux?.taux || 0;
    this.calculatePrixTTC();
  }

  calculatePrixTTC(): void {
    const prixVenteHTInput = document.querySelector('input[name="prixVente"]') as HTMLInputElement;
    const prixVenteHT = prixVenteHTInput ? +prixVenteHTInput.value : 0;
    
    if (prixVenteHT > 0 && this.selectedTauxTVA > 0) {
      this.calculatedPrixTTC = prixVenteHT * (1 + this.selectedTauxTVA / 100);
    } else {
      this.calculatedPrixTTC = 0;
    }
  }

  onCategorieChange(event: any): void {
    const categorieId = +event.target.value;
    if (categorieId) {
      // Charger les sous-catégories
      this.sousCategorieService.getByCategorie(categorieId).subscribe({
        next: (sousCategories) => {
          const sousCategoriesArray = Array.isArray(sousCategories) ? sousCategories : [];
          this.sousCategoriesDropdown.set(sousCategoriesArray);
        },
        error: (error) => {
          console.error('❌ Erreur sous-catégories:', error);
          this.sousCategoriesDropdown.set([]);
        }
      });

      // Charger les formes filtrées par catégorie
      this.formeService.getByCategorie(categorieId).subscribe({
        next: (formes) => {
          const formesArray = Array.isArray(formes) ? formes : [];
          this.formesDropdown.set(formesArray);
        },
        error: (error) => {
          console.error('❌ Erreur formes:', error);
          this.formesDropdown.set([]);
        }
      });

      // Charger les taux TVA filtrés par catégorie
      this.tauxTVAService.getByCategorie(categorieId).subscribe({
        next: (taux) => {
          console.log('🔍 Taux TVA reçus pour catégorie', categorieId, ':', taux);
          let tauxArray = Array.isArray(taux) ? taux : [];

          // Si aucun taux spécifique à la catégorie, utiliser tous les taux
          if (tauxArray.length === 0) {
            console.log('⚠️ Aucun taux TVA spécifique pour cette catégorie, utilisation de tous les taux');
            this.tauxTVAService.getAll().subscribe({
              next: (tousTaux) => {
                console.log('✅ Tous les taux TVA récupérés:', tousTaux);
                this.tauxTVADropdown.set(tousTaux);
                this.selectionnerPremierTauxTVA();
              },
              error: (error) => console.error('❌ Erreur tous taux TVA:', error)
            });
          } else {
            console.log('✅ Taux TVA spécifiques trouvés:', tauxArray);
            this.tauxTVADropdown.set(tauxArray);
            this.selectionnerPremierTauxTVA();
          }
        },
        error: (error) => {
          console.error('❌ Erreur taux TVA:', error);
          this.tauxTVADropdown.set([]);
        }
      });
    } else {
      // Réinitialiser les dropdowns
      this.sousCategoriesDropdown.set([]);
      this.formesDropdown.set([]);
      this.tauxTVADropdown.set([]);
    }
  }

  refreshDropdowns(): void {
    console.log('🔄 Actualisation manuelle des dropdowns...');

    // Réinitialiser tous les dropdowns
    this.categoriesDropdown.set([]);
    this.sousCategoriesDropdown.set([]);
    this.marquesDropdown.set([]);
    this.formesDropdown.set([]);
    this.tauxTVADropdown.set([]);

    this.loadDropdowns();

    setTimeout(() => {
      console.log('📊 État des dropdowns après rechargement:');
      console.log('- Catégories:', this.categoriesDropdown());
      console.log('- Marques:', this.marquesDropdown());
      console.log('- Formes:', this.formesDropdown());
      console.log('- Taux TVA:', this.tauxTVADropdown());
    }, 2000);
  }

  openQuickCreateModal(type: string): void {
    console.log('➕ Ouverture du modal de création rapide pour:', type);
    alert(`Fonctionnalité de création rapide pour ${type} à implémenter`);
  }

  onSubmit(form: any): void {
    if (!form.valid) {
      alert('Veuillez remplir tous les champs obligatoires');
      return;
    }

    const formData = form.value;
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser?.id) {
      alert('Utilisateur non connecté');
      return;
    }

    if (this.isEditMode() && this.selectedProduct()) {
      // Mode édition
      const update: ProduitUpdate = {
        id: this.selectedProduct()!.id,
        nom: formData.nom,
        description: formData.description || '',
        prixVenteHT: +formData.prixVente || undefined,
        stock: +formData.stock || undefined,
        pourcentageRemise: +formData.pourcentageRemise || undefined,
        imageFiles: this.selectedImageFiles.length > 0 ? this.selectedImageFiles : undefined
      };

      this.produitService.update(this.selectedProduct()!.id, update).subscribe({
        next: () => {
          this.loadData();
          this.closeForm();
          alert('Produit mis à jour avec succès');
        },
        error: (error) => {
          console.error('❌ Erreur lors de la mise à jour:', error);
          alert('Erreur lors de la mise à jour du produit');
        }
      });
    } else {
      // Mode création
      if (!formData.referenceOriginal || !formData.nom || !formData.description ||
          !formData.prixAchat || !formData.prixVente || !formData.sousCategorieId ||
          !formData.marqueId || !formData.formeId || !formData.tauxTVAId) {
        alert('Veuillez remplir tous les champs obligatoires');
        return;
      }

      // Validation des champs obligatoires
      if (!formData.nom || !formData.referenceOriginal) {
        alert('Veuillez remplir tous les champs obligatoires (nom, référence)');
        return;
      }

      if (!formData.sousCategorieId || !formData.marqueId || !formData.formeId || !formData.tauxTVAId) {
        alert('Veuillez sélectionner une sous-catégorie, une marque, une forme et un taux TVA');
        return;
      }

      const prixAchat = +formData.prixAchat;
      const prixVente = +formData.prixVente;
      const stock = +formData.stock;

      if (isNaN(prixAchat) || prixAchat <= 0) {
        alert('Le prix d\'achat doit être un nombre positif');
        return;
      }

      if (isNaN(prixVente) || prixVente <= 0) {
        alert('Le prix de vente doit être un nombre positif');
        return;
      }

      if (isNaN(stock) || stock < 0) {
        alert('Le stock doit être un nombre positif ou zéro');
        return;
      }

      const newProduct: ProduitCreate = {
        nom: formData.nom.trim(),
        description: formData.description?.trim() || '',
        referenceOriginal: formData.referenceOriginal.trim(),
        referenceFournisseur: formData.referenceFournisseur?.trim() || undefined,
        codeABarre: formData.codeABarre?.trim() || `${Date.now()}${Math.floor(Math.random() * 1000)}`,
        prixAchatHT: prixAchat,
        prixVenteHT: prixVente,
        stock: stock,
        fournisseurId: currentUser.id,
        sousCategorieId: +formData.sousCategorieId,
        marqueId: +formData.marqueId,
        formeId: +formData.formeId,
        tauxTVAId: +formData.tauxTVAId,
        pourcentageRemise: formData.pourcentageRemise !== null && formData.pourcentageRemise !== undefined && formData.pourcentageRemise !== '' ? +formData.pourcentageRemise : undefined,
        imageFiles: this.selectedImageFiles.length > 0 ? this.selectedImageFiles : undefined
      };

      console.log('📤 Données à envoyer:', newProduct);
      console.log('🔍 Détail des données:');
      console.log('- Nom:', newProduct.nom);
      console.log('- Description:', newProduct.description);
      console.log('- Prix HT:', newProduct.prixVenteHT);
      console.log('- Prix Achat:', newProduct.prixAchatHT);
      console.log('- Stock:', newProduct.stock);
      console.log('- Sous-catégorie ID:', newProduct.sousCategorieId);
      console.log('- Marque ID:', newProduct.marqueId);
      console.log('- Forme ID:', newProduct.formeId);
      console.log('- Taux TVA ID:', newProduct.tauxTVAId);
      console.log('- Code à barre:', newProduct.codeABarre);
      console.log('- Référence:', newProduct.referenceFournisseur);
      console.log('- Pourcentage remise (form):', formData.pourcentageRemise);
      console.log('- Pourcentage remise (final):', newProduct.pourcentageRemise);
      console.log('- Images:', newProduct.imageFiles?.length || 0);

      this.produitService.create(newProduct).subscribe({
        next: () => {
          this.loadData();
          this.closeForm();
          alert('Produit créé avec succès');
        },
        error: (error) => {
          console.error('❌ Erreur lors de la création:', error);
          alert('Erreur lors de la création du produit');
        }
      });
    }
  }
}
