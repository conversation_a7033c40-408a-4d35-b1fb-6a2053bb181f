﻿using Microsoft.EntityFrameworkCore;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Services.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using Microsoft.AspNetCore.Identity;
using AutoMapper;

namespace WebApiPfe.Services.Implementations
{
    public class AdminService : IAdminService
    {
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly UserManager<Utilisateur> _userManager;

        public AdminService(
            AppDbContext context,
            IMapper mapper,
            UserManager<Utilisateur> userManager)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
        }

        public async Task<IEnumerable<UtilisateurGestionDto>> GetAllUtilisateursAsync()
        {
            var utilisateurs = await _context.Utilisateurs.ToListAsync();
            var result = new List<UtilisateurGestionDto>();

            foreach (var utilisateur in utilisateurs)
            {
                var roles = await _userManager.GetRolesAsync(utilisateur);
                var userRole = roles.FirstOrDefault() ?? "Utilisateur";

                // Compter les commandes pour les clients
                int? nombreCommandes = null;
                if (userRole.Equals("Client", StringComparison.OrdinalIgnoreCase))
                {
                    var client = await _context.Clients.FirstOrDefaultAsync(c => c.Id == utilisateur.Id);
                    if (client != null)
                    {
                        nombreCommandes = await _context.Commandes.CountAsync(cmd => cmd.ClientId == client.Id);
                    }
                }

                // Compter les produits pour les fournisseurs
                int? nombreProduits = null;
                if (userRole.Equals("Fournisseur", StringComparison.OrdinalIgnoreCase))
                {
                    var fournisseur = await _context.Fournisseurs.FirstOrDefaultAsync(f => f.Id == utilisateur.Id);
                    if (fournisseur != null)
                    {
                        nombreProduits = await _context.Produits.CountAsync(p => p.FournisseurId == fournisseur.Id);
                    }
                }

                result.Add(new UtilisateurGestionDto
                {
                    Id = utilisateur.Id,
                    NomComplet = $"{utilisateur.Prenom} {utilisateur.Nom}",
                    Email = utilisateur.Email ?? string.Empty,
                    EstActif = utilisateur.EstActif,
                    Role = userRole,
                    DateInscription = utilisateur.DateInscription,
                    DerniereConnexion = utilisateur.DerniereConnexion,
                    NombreCommandes = nombreCommandes,
                    NombreProduits = nombreProduits
                });
            }

            return result;
        }

        public async Task<UtilisateursPageDto> GetUtilisateursPageAsync(int page = 1, int pageSize = 10, string? search = null, string? role = null, bool? estActif = null)
        {
            Console.WriteLine($"🔍 AdminService: GetUtilisateursPageAsync - Page: {page}, PageSize: {pageSize}, Search: {search}, Role: {role}, EstActif: {estActif}");

            var query = _context.Utilisateurs.AsQueryable();

            // Filtrage par recherche (nom, prénom, email)
            if (!string.IsNullOrEmpty(search))
            {
                var searchLower = search.ToLower();
                query = query.Where(u =>
                    u.Nom.ToLower().Contains(searchLower) ||
                    u.Prenom.ToLower().Contains(searchLower) ||
                    (u.Email != null && u.Email.ToLower().Contains(searchLower))
                );
            }

            // Filtrage par statut actif/inactif
            if (estActif.HasValue)
            {
                query = query.Where(u => u.EstActif == estActif.Value);
            }

            // Compter le total avant pagination
            var totalCount = await query.CountAsync();
            Console.WriteLine($"✅ AdminService: Total utilisateurs trouvés: {totalCount}");

            // Pagination
            var utilisateurs = await query
                .OrderBy(u => u.Nom)
                .ThenBy(u => u.Prenom)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            Console.WriteLine($"✅ AdminService: Utilisateurs récupérés pour la page: {utilisateurs.Count}");

            // Conversion en DTO avec rôles
            var result = new List<UtilisateurGestionDto>();
            foreach (var utilisateur in utilisateurs)
            {
                var roles = await _userManager.GetRolesAsync(utilisateur);
                var userRole = roles.FirstOrDefault() ?? "Utilisateur";

                // Filtrage par rôle (après récupération car les rôles sont dans AspNetUserRoles)
                if (!string.IsNullOrEmpty(role) && !userRole.Equals(role, StringComparison.OrdinalIgnoreCase))
                {
                    continue;
                }

                // Compter les commandes pour les clients
                int? nombreCommandes = null;
                if (userRole.Equals("Client", StringComparison.OrdinalIgnoreCase))
                {
                    var client = await _context.Clients.FirstOrDefaultAsync(c => c.Id == utilisateur.Id);
                    if (client != null)
                    {
                        nombreCommandes = await _context.Commandes.CountAsync(cmd => cmd.ClientId == client.Id);
                    }
                }

                // Compter les produits pour les fournisseurs
                int? nombreProduits = null;
                if (userRole.Equals("Fournisseur", StringComparison.OrdinalIgnoreCase))
                {
                    var fournisseur = await _context.Fournisseurs.FirstOrDefaultAsync(f => f.Id == utilisateur.Id);
                    if (fournisseur != null)
                    {
                        nombreProduits = await _context.Produits.CountAsync(p => p.FournisseurId == fournisseur.Id);
                    }
                }

                result.Add(new UtilisateurGestionDto
                {
                    Id = utilisateur.Id,
                    NomComplet = $"{utilisateur.Prenom} {utilisateur.Nom}",
                    Email = utilisateur.Email ?? string.Empty,
                    EstActif = utilisateur.EstActif,
                    Role = userRole,
                    DateInscription = utilisateur.DateInscription,
                    DerniereConnexion = utilisateur.DerniereConnexion,
                    NombreCommandes = nombreCommandes,
                    NombreProduits = nombreProduits
                });
            }

            Console.WriteLine($"✅ AdminService: Utilisateurs après filtrage par rôle: {result.Count}");

            return new UtilisateursPageDto
            {
                Utilisateurs = result,
                TotalCount = totalCount, // Note: Ce n'est pas parfait car le filtrage par rôle se fait après
                Page = page,
                PageSize = pageSize
            };
        }

        public async Task<bool> ToggleActivationUtilisateurAsync(int id)
        {
            var utilisateur = await _context.Utilisateurs.FindAsync(id);
            if (utilisateur is null) return false;

            utilisateur.EstActif = !utilisateur.EstActif;
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> SupprimerUtilisateurAsync(int id)
        {
            var utilisateur = await _context.Utilisateurs.FindAsync(id);
            if (utilisateur is null) return false;

            _context.Utilisateurs.Remove(utilisateur);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> ValiderCategorieAsync(int categorieId)
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Recherche de la catégorie ID: {categorieId}");
                var categorie = await _context.Categories.FindAsync(categorieId);

                if (categorie is null)
                {
                    Console.WriteLine($"❌ AdminService: Catégorie {categorieId} non trouvée");
                    return false;
                }

                Console.WriteLine($"✅ AdminService: Catégorie trouvée: {categorie.Nom}, EstValidee: {categorie.EstValidee}");

                // Si déjà validée, on considère que c'est un succès
                if (categorie.EstValidee)
                {
                    Console.WriteLine($"✅ AdminService: Catégorie déjà validée");
                    return true;
                }

                categorie.EstValidee = true;
                var saveResult = await _context.SaveChangesAsync() > 0;
                Console.WriteLine($"✅ AdminService: Sauvegarde réussie: {saveResult}");
                return saveResult;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans ValiderCategorieAsync: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> AjouterCategorieAsync(CategorieGestionDto dto)
        {
            if (dto is null) return false;

            var categorie = new Categorie
            {
                Nom = dto.Nom ?? string.Empty,
                Description = dto.Description ?? string.Empty,
                EstValidee = false
            };

            await _context.Categories.AddAsync(categorie);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> ModifierCategorieAsync(int id, CategorieGestionDto dto)
        {
            if (dto is null) return false;

            var categorie = await _context.Categories.FindAsync(id);
            if (categorie is null) return false;

            categorie.Nom = dto.Nom ?? categorie.Nom;
            categorie.Description = dto.Description ?? categorie.Description;
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> AjouterSousCategorieAsync(SousCategorieGestionDto dto)
        {
            if (dto is null) return false;

            var categorieParente = await _context.Categories.FindAsync(dto.CategorieId);
            if (categorieParente is null) return false;

            var sousCategorie = new SousCategorie
            {
                Nom = dto.Nom ?? string.Empty,
                Description = dto.Description ?? string.Empty,
                CategorieId = dto.CategorieId,
                EstValidee = false
            };

            await _context.SousCategories.AddAsync(sousCategorie);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<IEnumerable<CommandeGestionDto>> GetCommandesAsync()
        {
            return await _context.Commandes
                .Where(c => c != null)
                .Select(c => new CommandeGestionDto
                {
                    Id = c.Id,
                    DateCommande = c.DateCreation,
                    Statut = c.Statut.ToString(),
                    MontantTotal = c.MontantTotal
                })
                .ToListAsync();
        }

        public async Task<StatistiqueDto> ObtenirStatistiquesGeneralesAsync()
        {
            return new StatistiqueDto
            {
                NombreUtilisateurs = await _context.Utilisateurs.CountAsync(),
                NombreProduits = await _context.Produits.CountAsync(),
                NombreCommandes = await _context.Commandes.CountAsync(),
                NombreCommandesAnnulees = await _context.Commandes
                    .CountAsync(c => c.Statut == StatutCommande.Annulee),
                NombreVentes = await _context.Commandes
                    .CountAsync(c => c.Statut == StatutCommande.Validee),
                NombreFournisseurs = await _context.Fournisseurs.CountAsync(),
                NombreClients = await _context.Clients.CountAsync()
            };
        }

        public Task<bool> RefuserCategorieAsync(int categorieId)
        {
            throw new NotImplementedException();
        }

        public Task<bool> SupprimerCategorieAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<bool> ValidersousCategorieAsync(int categorieId)
        {
            throw new NotImplementedException();
        }

        public Task<bool> RefusersousCategorieAsync(int categorieId)
        {
            throw new NotImplementedException();
        }

        public Task<bool> ModifierSousCategorieAsync(int id, SousCategorieGestionDto dto)
        {
            throw new NotImplementedException();
        }

        public Task<bool> SupprimerSousCategorieAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<bool> AnnulerCommandeAsync(int commandeId)
        {
            throw new NotImplementedException();
        }

        // ==================== GESTION DES PRODUITS ====================

        public async Task<ProduitGestionPageDto> GetProduitsPageAsync(int page = 1, int pageSize = 10, string? search = null, string? statut = null, bool? stockCritique = null, string? fournisseur = null)
        {
            Console.WriteLine($"🔍 AdminService: GetProduitsPageAsync - Page: {page}, PageSize: {pageSize}, Search: {search}, Statut: {statut}, StockCritique: {stockCritique}, Fournisseur: {fournisseur}");

            var query = _context.Produits
                .Include(p => p.Fournisseur)
                .Include(p => p.SousCategorie)
                    .ThenInclude(sc => sc.Categorie)
                .Include(p => p.Images)
                .Include(p => p.TauxTVA)
                .Include(p => p.PromotionsApplicables)
                .AsQueryable();

            // Filtrage par recherche (nom, référence)
            if (!string.IsNullOrEmpty(search))
            {
                var searchLower = search.ToLower();
                query = query.Where(p =>
                    p.Nom.ToLower().Contains(searchLower) ||
                    p.ReferenceOriginal.ToLower().Contains(searchLower) ||
                    (p.ReferenceFournisseur != null && p.ReferenceFournisseur.ToLower().Contains(searchLower))
                );
            }

            // Filtrage par statut de validation
            if (!string.IsNullOrEmpty(statut))
            {
                switch (statut.ToLower())
                {
                    case "valide":
                        // Pour l'instant, on considère tous les produits comme validés
                        // À ajuster selon la logique métier
                        break;
                    case "attente":
                        // Filtrer les produits en attente de validation
                        break;
                }
            }

            // Filtrage par stock critique
            if (stockCritique.HasValue && stockCritique.Value)
            {
                query = query.Where(p => p.Stock <= 10);
            }

            // Filtrage par fournisseur
            if (!string.IsNullOrEmpty(fournisseur))
            {
                query = query.Where(p => p.Fournisseur.RaisonSociale.ToLower().Contains(fournisseur.ToLower()));
            }

            // Compter le total avant pagination
            var totalCount = await query.CountAsync();
            Console.WriteLine($"✅ AdminService: Total produits trouvés: {totalCount}");

            // Pagination
            var produits = await query
                .OrderByDescending(p => p.DateAjout)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            Console.WriteLine($"✅ AdminService: Produits récupérés pour la page: {produits.Count}");

            // Conversion en DTO avec calcul des prix promotionnels
            var result = new List<ProduitGestionDto>();

            foreach (var p in produits)
            {
                // Calcul des prix avec promotions (simplifié pour l'instant)
                var tauxTVA = p.TauxTVA?.Taux ?? 0.20m;
                var prixBaseTTC = p.PrixVenteHT * (1 + tauxTVA);

                // Pour l'instant, on utilise les prix de base
                // TODO: Implémenter le calcul des promotions quand les entités seront prêtes
                var prixApresOutletTTC = prixBaseTTC;
                var prixFinalTTC = prixBaseTTC;
                var pourcentageRemise = 0m;

                result.Add(new ProduitGestionDto
                {
                    Id = p.Id,
                    Nom = p.Nom,
                    ReferenceOriginal = p.ReferenceOriginal,
                    ReferenceFournisseur = p.ReferenceFournisseur,
                    PrixVenteTTC = Math.Round(prixBaseTTC, 2),
                    PrixApresOutletTTC = Math.Round(prixApresOutletTTC, 2),
                    PrixFinalTTC = Math.Round(prixFinalTTC, 2),
                    PourcentageRemiseTotale = pourcentageRemise,
                    Stock = p.Stock,
                    FournisseurNom = p.Fournisseur?.RaisonSociale ?? "Non défini",
                    CategorieNom = p.SousCategorie?.Categorie?.Nom ?? "Non défini",
                    SousCategorieNom = p.SousCategorie?.Nom ?? "Non défini",
                    EstValide = true, // À ajuster selon la logique métier
                    EstEnAvant = p.EstMisEnAvant,
                    DateCreation = p.DateAjout,
                    NombreVues = 0, // À implémenter si nécessaire
                    NombreVentes = 0, // À calculer depuis les commandes
                    NoteMoyenne = 0, // À calculer depuis les avis
                    NombreAvis = 0, // À calculer depuis les avis
                    Images = p.Images?.OrderBy(img => img.Ordre).Select(img => img.ImageUrl).ToList() ?? new List<string>()
                });
            }

            Console.WriteLine($"✅ AdminService: Produits après conversion DTO: {result.Count}");

            return new ProduitGestionPageDto
            {
                Produits = result,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }

        public async Task<bool> ValiderProduitAsync(int produitId)
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Validation du produit ID: {produitId}");
                var produit = await _context.Produits.FindAsync(produitId);

                if (produit == null)
                {
                    Console.WriteLine($"❌ AdminService: Produit {produitId} non trouvé");
                    return false;
                }

                // Pour l'instant, on considère que tous les produits sont validés
                // À ajuster selon la logique métier (ajouter un champ EstValide au modèle Produit)
                Console.WriteLine($"✅ AdminService: Produit {produitId} validé");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans ValiderProduitAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> RefuserProduitAsync(int produitId, string? raison = null)
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Refus du produit ID: {produitId}, Raison: {raison}");
                var produit = await _context.Produits.FindAsync(produitId);

                if (produit == null)
                {
                    Console.WriteLine($"❌ AdminService: Produit {produitId} non trouvé");
                    return false;
                }

                // À implémenter selon la logique métier
                // Peut-être marquer le produit comme refusé ou le supprimer
                Console.WriteLine($"✅ AdminService: Produit {produitId} refusé");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans RefuserProduitAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> MettreEnAvantProduitAsync(int produitId)
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Mise en avant du produit ID: {produitId}");
                var produit = await _context.Produits.FindAsync(produitId);

                if (produit == null)
                {
                    Console.WriteLine($"❌ AdminService: Produit {produitId} non trouvé");
                    return false;
                }

                produit.EstMisEnAvant = true;
                var result = await _context.SaveChangesAsync() > 0;
                Console.WriteLine($"✅ AdminService: Produit {produitId} mis en avant: {result}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans MettreEnAvantProduitAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> RetirerMiseEnAvantProduitAsync(int produitId)
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Retrait mise en avant du produit ID: {produitId}");
                var produit = await _context.Produits.FindAsync(produitId);

                if (produit == null)
                {
                    Console.WriteLine($"❌ AdminService: Produit {produitId} non trouvé");
                    return false;
                }

                produit.EstMisEnAvant = false;
                var result = await _context.SaveChangesAsync() > 0;
                Console.WriteLine($"✅ AdminService: Mise en avant retirée pour produit {produitId}: {result}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans RetirerMiseEnAvantProduitAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ModererContenuProduitAsync(int produitId, string? nouveauNom = null, string? nouvelleDescription = null)
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Modération du contenu produit ID: {produitId}");
                var produit = await _context.Produits.FindAsync(produitId);

                if (produit == null)
                {
                    Console.WriteLine($"❌ AdminService: Produit {produitId} non trouvé");
                    return false;
                }

                if (!string.IsNullOrEmpty(nouveauNom))
                {
                    produit.Nom = nouveauNom;
                }

                if (!string.IsNullOrEmpty(nouvelleDescription))
                {
                    produit.Description = nouvelleDescription;
                }

                var result = await _context.SaveChangesAsync() > 0;
                Console.WriteLine($"✅ AdminService: Contenu modéré pour produit {produitId}: {result}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans ModererContenuProduitAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SupprimerProduitAsync(int produitId)
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Suppression du produit ID: {produitId}");
                var produit = await _context.Produits.FindAsync(produitId);

                if (produit == null)
                {
                    Console.WriteLine($"❌ AdminService: Produit {produitId} non trouvé");
                    return false;
                }

                _context.Produits.Remove(produit);
                var result = await _context.SaveChangesAsync() > 0;
                Console.WriteLine($"✅ AdminService: Produit {produitId} supprimé: {result}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans SupprimerProduitAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<IEnumerable<ProduitGestionDto>> GetProduitsStockCritiqueAsync(int seuilCritique = 10)
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Récupération des produits en stock critique (seuil: {seuilCritique})");

                var produits = await _context.Produits
                    .Include(p => p.Fournisseur)
                    .Include(p => p.SousCategorie)
                        .ThenInclude(sc => sc.Categorie)
                    .Include(p => p.Images)
                    .Include(p => p.TauxTVA)
                    .Include(p => p.PromotionsApplicables)
                    .Where(p => p.Stock <= seuilCritique)
                    .OrderBy(p => p.Stock)
                    .ToListAsync();

                var result = produits.Select(p => {
                    var tauxTVA = p.TauxTVA?.Taux ?? 0.20m;
                    var prixBaseTTC = p.PrixVenteHT * (1 + tauxTVA);

                    // Calculer les prix avec promotions outlet
                    var remiseOutlet = p.PromotionsApplicables?.FirstOrDefault(promo =>
                        promo.Type == TypePromotion.Outlet && promo.EstValide());

                    decimal prixApresOutletHT = p.PrixVenteHT;
                    decimal pourcentageRemiseOutlet = 0m;

                    if (remiseOutlet != null)
                    {
                        pourcentageRemiseOutlet = remiseOutlet.PourcentageRemise;
                        prixApresOutletHT = remiseOutlet.CalculerPrixRemise(p.PrixVenteHT, p.TauxTVA);
                    }

                    var prixApresOutletTTC = prixApresOutletHT * (1 + tauxTVA);

                    // Calculer les autres promotions
                    var autresPromos = p.PromotionsApplicables?
                        .Where(promo => promo.Type != TypePromotion.Outlet && promo.EstValide())
                        .OrderByDescending(promo => promo.PourcentageRemise)
                        .ToList() ?? new List<Promotion>();

                    decimal prixFinalHT = prixApresOutletHT;
                    decimal pourcentageRemiseTotale = pourcentageRemiseOutlet;

                    foreach (var promo in autresPromos)
                    {
                        var prixAvantPromo = prixFinalHT;
                        prixFinalHT = promo.CalculerPrixRemise(prixFinalHT, p.TauxTVA);
                        pourcentageRemiseTotale += promo.PourcentageRemise * (prixAvantPromo / p.PrixVenteHT);
                    }

                    var prixFinalTTC = prixFinalHT * (1 + tauxTVA);

                    // Calculer le pourcentage de remise totale réel
                    if (p.PrixVenteHT > 0)
                    {
                        pourcentageRemiseTotale = Math.Round(100 * (1 - prixFinalHT / p.PrixVenteHT), 2);
                    }

                    return new ProduitGestionDto
                    {
                        Id = p.Id,
                        Nom = p.Nom,
                        ReferenceOriginal = p.ReferenceOriginal,
                        ReferenceFournisseur = p.ReferenceFournisseur,
                        PrixVenteTTC = Math.Round(prixBaseTTC, 2),
                        PrixApresOutletTTC = Math.Round(prixApresOutletTTC, 2),
                        PrixFinalTTC = Math.Round(prixFinalTTC, 2),
                        PourcentageRemiseTotale = pourcentageRemiseTotale,
                        Stock = p.Stock,
                        FournisseurNom = p.Fournisseur?.RaisonSociale ?? "Non défini",
                        CategorieNom = p.SousCategorie?.Categorie?.Nom ?? "Non défini",
                        SousCategorieNom = p.SousCategorie?.Nom ?? "Non défini",
                        EstValide = true,
                        EstEnAvant = p.EstMisEnAvant,
                        DateCreation = p.DateAjout,
                        NombreVues = 0,
                        NombreVentes = 0,
                        NoteMoyenne = 0,
                        NombreAvis = 0,
                        Images = p.Images?.OrderBy(img => img.Ordre).Select(img => img.ImageUrl).ToList() ?? new List<string>()
                    };
                }).ToList();

                Console.WriteLine($"✅ AdminService: {result.Count} produits en stock critique trouvés");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans GetProduitsStockCritiqueAsync: {ex.Message}");
                return new List<ProduitGestionDto>();
            }
        }

        public async Task<bool> UpdateStockProduitAsync(int produitId, int nouveauStock)
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Mise à jour stock produit ID: {produitId}, Nouveau stock: {nouveauStock}");
                var produit = await _context.Produits.FindAsync(produitId);

                if (produit == null)
                {
                    Console.WriteLine($"❌ AdminService: Produit {produitId} non trouvé");
                    return false;
                }

                produit.Stock = nouveauStock;
                var result = await _context.SaveChangesAsync() > 0;
                Console.WriteLine($"✅ AdminService: Stock mis à jour pour produit {produitId}: {result}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans UpdateStockProduitAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<IEnumerable<ProduitGestionDto>> GetProduitsEnAvantAsync()
        {
            try
            {
                Console.WriteLine($"🔍 AdminService: Récupération des produits mis en avant");

                var produits = await _context.Produits
                    .Include(p => p.Fournisseur)
                    .Include(p => p.SousCategorie)
                        .ThenInclude(sc => sc.Categorie)
                    .Include(p => p.Images)
                    .Include(p => p.TauxTVA)
                    .Include(p => p.PromotionsApplicables)
                    .Where(p => p.EstMisEnAvant)
                    .OrderByDescending(p => p.DateAjout)
                    .ToListAsync();

                var result = produits.Select(p => {
                    var tauxTVA = p.TauxTVA?.Taux ?? 0.20m;
                    var prixBaseTTC = p.PrixVenteHT * (1 + tauxTVA);

                    // Calculer les prix avec promotions outlet
                    var remiseOutlet = p.PromotionsApplicables?.FirstOrDefault(promo =>
                        promo.Type == TypePromotion.Outlet && promo.EstValide());

                    decimal prixApresOutletHT = p.PrixVenteHT;
                    decimal pourcentageRemiseOutlet = 0m;

                    if (remiseOutlet != null)
                    {
                        pourcentageRemiseOutlet = remiseOutlet.PourcentageRemise;
                        prixApresOutletHT = remiseOutlet.CalculerPrixRemise(p.PrixVenteHT, p.TauxTVA);
                    }

                    var prixApresOutletTTC = prixApresOutletHT * (1 + tauxTVA);

                    // Calculer les autres promotions
                    var autresPromos = p.PromotionsApplicables?
                        .Where(promo => promo.Type != TypePromotion.Outlet && promo.EstValide())
                        .OrderByDescending(promo => promo.PourcentageRemise)
                        .ToList() ?? new List<Promotion>();

                    decimal prixFinalHT = prixApresOutletHT;
                    decimal pourcentageRemiseTotale = pourcentageRemiseOutlet;

                    foreach (var promo in autresPromos)
                    {
                        var prixAvantPromo = prixFinalHT;
                        prixFinalHT = promo.CalculerPrixRemise(prixFinalHT, p.TauxTVA);
                        pourcentageRemiseTotale += promo.PourcentageRemise * (prixAvantPromo / p.PrixVenteHT);
                    }

                    var prixFinalTTC = prixFinalHT * (1 + tauxTVA);

                    // Calculer le pourcentage de remise totale réel
                    if (p.PrixVenteHT > 0)
                    {
                        pourcentageRemiseTotale = Math.Round(100 * (1 - prixFinalHT / p.PrixVenteHT), 2);
                    }

                    return new ProduitGestionDto
                    {
                        Id = p.Id,
                        Nom = p.Nom,
                        ReferenceOriginal = p.ReferenceOriginal,
                        ReferenceFournisseur = p.ReferenceFournisseur,
                        PrixVenteTTC = Math.Round(prixBaseTTC, 2),
                        PrixApresOutletTTC = Math.Round(prixApresOutletTTC, 2),
                        PrixFinalTTC = Math.Round(prixFinalTTC, 2),
                        PourcentageRemiseTotale = pourcentageRemiseTotale,
                        Stock = p.Stock,
                        FournisseurNom = p.Fournisseur?.RaisonSociale ?? "Non défini",
                        CategorieNom = p.SousCategorie?.Categorie?.Nom ?? "Non défini",
                        SousCategorieNom = p.SousCategorie?.Nom ?? "Non défini",
                        EstValide = true,
                        EstEnAvant = p.EstMisEnAvant,
                        DateCreation = p.DateAjout,
                        NombreVues = 0,
                        NombreVentes = 0,
                        NoteMoyenne = 0,
                        NombreAvis = 0,
                        Images = p.Images?.OrderBy(img => img.Ordre).Select(img => img.ImageUrl).ToList() ?? new List<string>()
                    };
                }).ToList();

                Console.WriteLine($"✅ AdminService: {result.Count} produits en avant trouvés");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ AdminService: Erreur dans GetProduitsEnAvantAsync: {ex.Message}");
                return new List<ProduitGestionDto>();
            }
        }
    }
}
