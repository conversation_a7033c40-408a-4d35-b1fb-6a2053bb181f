using AutoMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.ComponentModel;
using System.Globalization;
using System.Text;
using System.Text.Json;
using WebApiPfe;
using WebApiPfe.Converters;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;
using WebApiPfe.Services.Helpers;
using WebApiPfe.Services.Hubs;
using WebApiPfe.Services.Implementations;
using WebApiPfe.Services.Interfaces;
using WebApiPfe.Services.Profile;
using WebApiPfe.Services.Token;

var builder = WebApplication.CreateBuilder(args);

// Configuration du port
builder.WebHost.UseUrls("http://localhost:5014");

// Configuration du convertisseur de type pour les décimaux
TypeDescriptor.AddAttributes(typeof(decimal), new TypeConverterAttribute(typeof(DecimalTypeConverter)));
TypeDescriptor.AddAttributes(typeof(decimal?), new TypeConverterAttribute(typeof(DecimalTypeConverter)));

// 1. Configuration de la base de donn�es
builder.Services.AddDbContext<AppDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// 2. Configuration d'AutoMapper
builder.Services.AddAutoMapper(
    typeof(Program),
    typeof(UtilisateurProfile),
    typeof(MappingProfile),
    typeof(CommandeFournisseurProfile)
);

// 3. Configuration d'Identity
builder.Services.AddIdentity<Utilisateur, IdentityRole<int>>(
    options =>
    {
        options.User.RequireUniqueEmail = true;
    })
    .AddEntityFrameworkStores<AppDbContext>()
    .AddDefaultTokenProviders()
    .AddUserManager<UserManager<Utilisateur>>()
    .AddSignInManager<SignInManager<Utilisateur>>();

// 4. Configuration JWT (Version s�curis�e)
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    var jwtSecret = builder.Configuration["JWT:SigninKey"]
        ?? throw new InvalidOperationException("Cl� JWT non configur�e dans appsettings.json");

    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true, 
        ValidIssuer = builder.Configuration["JWT:Issuer"],
        ValidateAudience = true, 
        ValidAudience = builder.Configuration["JWT:Audience"],
        ValidateLifetime = true, 
        ClockSkew = TimeSpan.Zero,  
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSecret))
    };
    options.TokenValidationParameters.ClockSkew = TimeSpan.FromMinutes(1);
});

// 5. Configuration des services
builder.Services.AddScoped(typeof(Lazy<>), typeof(LazyResolver<>));

builder.Services.AddSignalR().AddHubOptions<NotificationHub>(
    options =>
    {
        options.ClientTimeoutInterval = TimeSpan.FromMinutes(2);
    });

builder.Services.AddScoped<PromotionService>();
builder.Services.AddScoped<ICommandeService, CommandeService>();
builder.Services.AddScoped<ICommandeFournisseurService, CommandeFournisseurService>();
builder.Services.AddScoped<ILigneCommandeFournisseurService, LigneCommandeFournisseurService>();
builder.Services.AddScoped<ICategorieService, CategorieService>();
builder.Services.AddScoped<ISousCategorieService, SousCategorieService>();
builder.Services.AddScoped<IProduitService, ProduitService>();
builder.Services.AddScoped<IFormeService, FormeService>();
builder.Services.AddScoped<IMarqueService, MarqueService>();
builder.Services.AddScoped<IPromotionService, PromotionService>();
builder.Services.AddScoped<IAuthService, AuthService>(provider =>
    new AuthService(
        provider.GetRequiredService<AppDbContext>(),
        provider.GetRequiredService<ITokenService>(),
        provider.GetRequiredService<IMapper>(),
        provider.GetRequiredService<IPasswordHasher<Utilisateur>>(),
        provider.GetRequiredService<UserManager<Utilisateur>>(),
        builder.Configuration));

builder.Services.AddScoped<ITauxTVAService, TauxTVAService>();
builder.Services.AddScoped<IClientService, ClientService>();
builder.Services.AddScoped<IFournisseurService, FournisseurService>();
builder.Services.AddScoped<IAdminService, AdminService>();
builder.Services.AddScoped<IPasswordHasher<Utilisateur>, PasswordHasher<Utilisateur>>();
builder.Services.AddScoped<IAdresseService, AdresseService>();
builder.Services.AddScoped<IFavoriService, FavoriService>();
builder.Services.AddScoped<IPaiementService, PaiementService>();
builder.Services.AddScoped<ITokenService, TokenService>();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();
builder.Services.AddScoped<IPanierService, PanierService>();
// builder.Services.AddScoped<IReclamationService, ReclamationService>(); // Temporairement commenté
builder.Services.AddScoped<IDemandeCategorieService, DemandeCategorieService>();
builder.Services.AddScoped<IDemandeSousCategorieService, DemandeSousCategorieService>();
builder.Services.AddScoped<ICommandeFournisseurService, CommandeFournisseurService>();
builder.Services.AddScoped<IAvisService, AvisService>();
builder.Services.AddScoped<INotificationService, NotificationService>();
builder.Services.AddScoped<IDemandeService, DemandeService>();

builder.Services.Configure<FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = 50 * 1024 * 1024;
});
builder.Services.AddAntiforgery(options =>
{
    options.HeaderName = "X-CSRF-TOKEN";
});
//builder.Services.Configure<ApiBehaviorOptions>(options =>
//{
//    options.InvalidModelStateResponseFactory = context =>
//    {
//        var errors = context.ModelState
//            .Where(e => e.Value.Errors.Count > 0)
//            .ToDictionary(
//                e => e.Key,
//                e => e.Value.Errors.Select(er =>
//                    $"{er.ErrorMessage} (Valeur re�ue: {context.ModelState[e.Key]?.RawValue})").ToArray()
//            );

//        var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
//        logger.LogError("VALIDATION FAILURE:\n{@Errors}\nRequest: {@Request}",
//            errors,
//            new
//            {
//                Path = context.HttpContext.Request.Path,
//                Method = context.HttpContext.Request.Method,
//                Headers = context.HttpContext.Request.Headers
//            });

//        return new BadRequestObjectResult(new
//        {
//            Title = "Erreurs de validation d�taill�es",
//            Errors = errors,
//            TraceId = context.HttpContext.TraceIdentifier,
//            Documentation = "/swagger"
//        });
//    };
//});


builder.Services.AddEndpointsApiExplorer();
//builder.Services.AddSwaggerExamplesFromAssemblyOf<Program>();
builder.Services.AddSwaggerGen(opt =>
{
    opt.SwaggerDoc
    (
        "v1", 
        new OpenApiInfo { 
            Title = "MyAPI", 
            Version = "v1" 
        });
    opt.CustomSchemaIds(type =>
        type.FullName?.Replace("+", ".") ?? type.Name);
    opt.OperationFilter<FileUploadOperationFilter>();
    //opt.SchemaFilter<FormFileSchemaFilter>();
    opt.MapType<IFormFile>(() => new OpenApiSchema
    {
        Type = "string",
        Format = "binary",
    });
    opt.MapType<List<IFormFile>>(() => new OpenApiSchema
    {
        Type = "array",
        Items = new OpenApiSchema
        {
            Type = "string",
            Format = "binary"
        }
    });
    //opt.ExampleFilters();
    //opt.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    //{
    //    In = ParameterLocation.Header,
    //    Description = "Please enter token",
    //    Name = "Authorization",
    //    Type = SecuritySchemeType.Http,
    //    BearerFormat = "JWT",
    //    Scheme = "bearer"
    //});

    //opt.AddSecurityRequirement(new OpenApiSecurityRequirement
    //{
    //    {
    //        new OpenApiSecurityScheme
    //        {
    //            Reference = new OpenApiReference
    //            {
    //                Type = ReferenceType.SecurityScheme,
    //                Id = "Bearer"
    //            }
    //        },
    //        Array.Empty<string>()
    //    }
    //});

});

// 7. Configuration CORS 
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAngularDevClient", policy =>
    {
        policy
            .AllowAnyOrigin()
            .AllowAnyHeader()
            .AllowAnyMethod()
            .WithExposedHeaders("*"); 
    });
});

builder.Services.AddControllers()
    .ConfigureApiBehaviorOptions(options =>
    {
        options.SuppressMapClientErrors = true;
        options.SuppressModelStateInvalidFilter = true;
    })
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.WriteIndented = true;
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    });
// Configuration des types MIME pour les images
var contentTypeProvider = new FileExtensionContentTypeProvider();
contentTypeProvider.Mappings[".avif"] = "image/avif";
contentTypeProvider.Mappings[".webp"] = "image/webp";
contentTypeProvider.Mappings[".jpeg"] = "image/jpeg";
contentTypeProvider.Mappings[".jpg"] = "image/jpeg";
contentTypeProvider.Mappings[".png"] = "image/png";
contentTypeProvider.Mappings[".gif"] = "image/gif";

builder.Services.Configure<StaticFileOptions>(options => {
    options.ContentTypeProvider = contentTypeProvider;
    options.ServeUnknownFileTypes = true;
    options.DefaultContentType = "application/octet-stream";
});
var app = builder.Build();

// 8. Middleware pipeline 
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "API Produits v1");
        c.ConfigObject.AdditionalItems["requestSnippetsEnabled"] = true;

        //// Configuration pour les uploads de fichiers
        //c.DefaultModelExpandDepth(2);
        //c.DefaultModelRendering(ModelRendering.Model);
        //c.DisplayRequestDuration();
        //c.DocExpansion(DocExpansion.None);
        //c.EnableDeepLinking();
        //c.EnableFilter();
    });
}

app.UseHttpsRedirection();



//app.Use(async (context, next) =>
//{
//    context.Request.EnableBuffering();
//    var requestBody = await new StreamReader(context.Request.Body).ReadToEndAsync();
//    context.Request.Body.Position = 0;

//    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
//    logger.LogInformation("Requ�te re�ue : {Method} {Path}\nHeaders: {@Headers}\nBody: {Body}",
//        context.Request.Method,
//        context.Request.Path,
//        context.Request.Headers,
//        requestBody);

//    await next();
//});
// Configuration des fichiers statiques
app.UseStaticFiles();

// Configuration pour servir les logos des fournisseurs
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(
        Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "logos")),
    RequestPath = "/uploads/logos",
    ContentTypeProvider = contentTypeProvider,
    ServeUnknownFileTypes = true,
    DefaultContentType = "image/jpeg"
});

// Configuration pour servir les images des produits
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(
        Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "produits")),
    RequestPath = "/uploads/produits",
    ContentTypeProvider = contentTypeProvider,
    ServeUnknownFileTypes = true,
    DefaultContentType = "image/jpeg"
});

// Configuration générale pour tous les uploads
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(
        Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads")),
    RequestPath = "/uploads",
    ContentTypeProvider = contentTypeProvider,
    ServeUnknownFileTypes = true,
    DefaultContentType = "image/jpeg"
});
app.Use(async (context, next) => {
    var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();
    logger.LogInformation($"Requ�te: {context.Request.Method} {context.Request.Path}");
    await next();
});

app.UseRouting();
app.UseCors("AllowAngularDevClient");

// Configuration de la culture pour accepter les points décimaux
var supportedCultures = new[] { "en-US", "fr-FR" };
app.UseRequestLocalization(options => {
    options.SetDefaultCulture("en-US") // Utiliser en-US par défaut pour les points décimaux
           .AddSupportedCultures(supportedCultures)
           .AddSupportedUICultures(supportedCultures);
});

app.UseAuthentication();
app.UseAuthorization();
// 9. Exception Handler 
app.UseExceptionHandler(exceptionHandlerApp =>
{

    exceptionHandlerApp.Run(async context =>
    {
        var exception = context.Features.Get<IExceptionHandlerFeature>()?.Error;
        var logger = context.RequestServices.GetRequiredService<ILogger<Program>>();

        logger.LogError(exception, "Erreur non g�r�e survenue");

        context.Response.ContentType = "application/json";
        var problemDetails = new ProblemDetails
        {
            Title = exception?.GetType().Name ?? "Erreur inattendue",
            Detail = exception?.Message,
            Instance = context.Request.Path,
            Status = exception switch
            {
                ArgumentException => StatusCodes.Status400BadRequest,
                UnauthorizedAccessException => StatusCodes.Status401Unauthorized,
                NotImplementedException => StatusCodes.Status501NotImplemented,
                _ => StatusCodes.Status500InternalServerError
            }
        };

        if (app.Environment.IsDevelopment())
        {
            problemDetails.Extensions.Add("stackTrace", exception?.StackTrace);
            problemDetails.Extensions.Add("innerException", exception?.InnerException?.Message);
        }

        await context.Response.WriteAsJsonAsync(problemDetails);
    });
});

app.Map("/error", () =>
    Results.Problem(
        title: "Endpoint d'erreur intentionnel",
        statusCode: StatusCodes.Status418ImATeapot,
        extensions: new Dictionary<string, object?>
        {
            {"documentation", "https://http.cat/418"}
        }
    )
).ExcludeFromDescription();

//10.  Configuration des hubs et contr�leurs
app.MapHub<NotificationHub>("/notificationHub");
app.MapControllers();

app.UseRequestLocalization(new RequestLocalizationOptions
{
    DefaultRequestCulture = new RequestCulture("en-US"),
    SupportedCultures = new[] { new CultureInfo("en-US") },
    SupportedUICultures = new[] { new CultureInfo("en-US") }
});
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}
// Initialiser les rôles et l'admin par défaut
using (var scope = app.Services.CreateScope())
{
    var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole<int>>>();
    var authService = scope.ServiceProvider.GetRequiredService<IAuthService>();
    var roles = Enum.GetNames(typeof(RoleUtilisateur));

    try
    {
        // Créer les rôles s'ils n'existent pas
        foreach (var role in roles)
        {
            if (!await roleManager.RoleExistsAsync(role))
            {
                await roleManager.CreateAsync(new IdentityRole<int>(role));
            }
        }

        // Initialiser l'admin par défaut
        await authService.InitialiserAdminParDefautAsync();
        Console.WriteLine("✅ Admin par défaut initialisé avec succès");
        Console.WriteLine("📧 Email: <EMAIL>");
        Console.WriteLine("🔑 Mot de passe: Admin123!");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"⚠️ Erreur lors de l'initialisation de l'admin: {ex.Message}");
        Console.WriteLine("⚠️ Vérifiez que SQL Server est démarré et que la base de données existe");
    }
}

app.Run();