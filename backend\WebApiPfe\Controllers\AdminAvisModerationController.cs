using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using WebApiPfe.DTOs.Admin;
using WebApiPfe.Services.Interfaces;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.Controllers
{
    [ApiController]
    [Route("api/admin/avis-moderation")]
    public class AdminAvisModerationController : ControllerBase
    {
        private readonly IAvisService _avisService;
        private readonly ILogger<AdminAvisModerationController> _logger;
        private readonly UserManager<Utilisateur> _userManager;

        public AdminAvisModerationController(
            IAvisService avisService,
            ILogger<AdminAvisModerationController> logger,
            UserManager<Utilisateur> userManager)
        {
            _avisService = avisService;
            _logger = logger;
            _userManager = userManager;
        }

        /// <summary>
        /// Obtenir tous les avis pour modération (Admin frontend)
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<List<AvisModerationDto>>> GetAvisForModeration([FromQuery] AvisFilterDto filter)
        {
            try
            {
                _logger.LogInformation("Récupération des avis pour modération avec filtre: {@Filter}", filter);
                var avis = await _avisService.GetAvisForModerationAsync(filter);
                _logger.LogInformation("Récupération réussie: {Count} avis trouvés", avis.Count);
                return Ok(avis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des avis pour modération");
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir un avis spécifique pour modération
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<AvisModerationDto>> GetAvisModeration(int id)
        {
            try
            {
                _logger.LogInformation("Récupération de l'avis {Id} pour modération", id);
                var avis = await _avisService.GetAvisModerationAsync(id);
                if (avis == null)
                {
                    _logger.LogWarning("Avis {Id} non trouvé", id);
                    return NotFound(new { message = "Avis non trouvé" });
                }

                return Ok(avis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        /// <summary>
        /// Modérer un avis (Admin frontend)
        /// </summary>
        [HttpPut("{id}/moderer")]
        public async Task<ActionResult<AvisModerationDto>> ModererAvis(int id, [FromBody] ModererAvisDto dto)
        {
            try
            {
                _logger.LogInformation("Modération de l'avis {Id} avec statut {Statut}", id, dto.Statut);
                // Pour l'admin frontend, on utilise un ID modérateur par défaut
                var moderateurId = 1; // ID de l'admin système
                var avis = await _avisService.ModererAvisAsync(id, dto, moderateurId);
                _logger.LogInformation("Modération réussie pour l'avis {Id}", id);
                return Ok(avis);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Avis {Id} non trouvé pour modération: {Message}", id, ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la modération de l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        /// <summary>
        /// Supprimer seulement le commentaire d'un avis (Admin frontend)
        /// </summary>
        [HttpPut("{id}/supprimer-commentaire")]
        public async Task<ActionResult<AvisModerationDto>> SupprimerCommentaire(int id, [FromBody] SupprimerCommentaireDto? dto)
        {
            try
            {
                _logger.LogInformation("Suppression du commentaire de l'avis {Id}", id);

                // Récupérer l'admin par défaut
                var admins = await _userManager.GetUsersInRoleAsync("Admin");
                var adminParDefaut = admins.FirstOrDefault();
                if (adminParDefaut == null)
                {
                    _logger.LogError("Aucun administrateur trouvé dans le système");
                    return StatusCode(500, new { message = "Aucun administrateur trouvé dans le système" });
                }

                var raisonSuppression = dto?.RaisonSuppression ?? "Commentaire supprimé par l'administrateur";
                var avis = await _avisService.SupprimerCommentaireAsync(id, adminParDefaut.Id, raisonSuppression);
                _logger.LogInformation("Commentaire supprimé avec succès pour l'avis {Id}", id);
                return Ok(avis);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Avis {Id} non trouvé pour suppression de commentaire: {Message}", id, ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression du commentaire de l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        [HttpPut("{id}/restaurer-commentaire")]
        public async Task<ActionResult<AvisModerationDto>> RestaurerCommentaire(int id)
        {
            try
            {
                _logger.LogInformation("Restauration du commentaire de l'avis {Id}", id);

                // Récupérer l'admin par défaut
                var admins = await _userManager.GetUsersInRoleAsync("Admin");
                var adminParDefaut = admins.FirstOrDefault();
                if (adminParDefaut == null)
                {
                    _logger.LogError("Aucun administrateur trouvé dans le système");
                    return StatusCode(500, new { message = "Aucun administrateur trouvé dans le système" });
                }

                var avis = await _avisService.RestaurerCommentaireAsync(id, adminParDefaut.Id);
                _logger.LogInformation("Commentaire restauré avec succès pour l'avis {Id}", id);
                return Ok(avis);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogWarning("Avis {Id} non trouvé pour restauration de commentaire: {Message}", id, ex.Message);
                return NotFound(new { message = ex.Message });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("Opération invalide pour la restauration du commentaire de l'avis {Id}: {Message}", id, ex.Message);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la restauration du commentaire de l'avis {Id}", id);
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        /// <summary>
        /// Obtenir les statistiques des avis (Admin frontend)
        /// </summary>
        [HttpGet("statistiques")]
        public async Task<ActionResult<AvisStatsDto>> GetAvisStats()
        {
            try
            {
                _logger.LogInformation("Récupération des statistiques des avis");
                var stats = await _avisService.GetAvisStatsAsync();
                _logger.LogInformation("Statistiques récupérées: {TotalAvis} avis total", stats.TotalAvis);
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des statistiques des avis");
                return StatusCode(500, new { message = "Erreur interne du serveur", details = ex.Message });
            }
        }

        /// <summary>
        /// Endpoint de test pour vérifier la connectivité
        /// </summary>
        [HttpGet("test")]
        public ActionResult<object> Test()
        {
            return Ok(new 
            { 
                message = "Admin Avis Moderation Controller fonctionne",
                timestamp = DateTime.UtcNow,
                endpoints = new[]
                {
                    "GET /api/admin/avis-moderation - Liste des avis",
                    "GET /api/admin/avis-moderation/statistiques - Statistiques",
                    "GET /api/admin/avis-moderation/{id} - Avis spécifique",
                    "PUT /api/admin/avis-moderation/{id}/moderer - Modérer un avis",
                    "PUT /api/admin/avis-moderation/{id}/supprimer-commentaire - Supprimer un commentaire",
                    "PUT /api/admin/avis-moderation/{id}/restaurer-commentaire - Restaurer un commentaire"
                }
            });
        }
    }
}
