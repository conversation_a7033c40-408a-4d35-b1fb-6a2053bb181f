﻿namespace WebApiPfe.DTOs.Admin
{
    public class UtilisateurGestionDto
    {
        public int Id { get; set; }
        public string NomComplet { get; set; }
        public string Email { get; set; }
        public bool EstActif { get; set; }
        public string Role { get; set; }
        public DateTime DateInscription { get; set; }
        public DateTime? DerniereConnexion { get; set; }
        public int? NombreCommandes { get; set; } // Pour les clients
        public int? NombreProduits { get; set; } // Pour les fournisseurs
    }

    public class UtilisateursPageDto
    {
        public List<UtilisateurGestionDto> Utilisateurs { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    }

    public class ProduitGestionDto
    {
        public int Id { get; set; }
        public string Nom { get; set; } = string.Empty;
        public string ReferenceOriginal { get; set; } = string.Empty;
        public string? ReferenceFournisseur { get; set; }
        public decimal PrixVenteTTC { get; set; }
        public decimal PrixApresOutletTTC { get; set; }
        public decimal PrixFinalTTC { get; set; }
        public decimal PourcentageRemiseTotale { get; set; }
        public int Stock { get; set; }
        public bool EstStockCritique => Stock <= 10;
        public string FournisseurNom { get; set; } = string.Empty;
        public string CategorieNom { get; set; } = string.Empty;
        public string SousCategorieNom { get; set; } = string.Empty;
        public bool EstValide { get; set; }
        public bool EstEnAvant { get; set; }
        public DateTime DateCreation { get; set; }
        public int NombreVues { get; set; }
        public int NombreVentes { get; set; }
        public double NoteMoyenne { get; set; }
        public int NombreAvis { get; set; }
        public List<string> Images { get; set; } = new();
        public string? ImagePrincipale => Images.FirstOrDefault();
        public string StatutValidation => EstValide ? "Validé" : "En attente";
        public string StatutStock => EstStockCritique ? "Critique" : "Normal";
    }

    public class ProduitGestionPageDto
    {
        public List<ProduitGestionDto> Produits { get; set; } = new();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    }

    public class ProduitModerationDto
    {
        public int ProduitId { get; set; }
        public string? NouveauNom { get; set; }
        public string? NouvelleDescription { get; set; }
        public string? Raison { get; set; }
    }
}
