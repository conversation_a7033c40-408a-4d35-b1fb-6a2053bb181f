import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AdminService } from '../../../services/admin.service';



interface CommandeAvecFournisseur {
  id: number;
  numeroCommande: string;
  clientNom: string;
  clientEmail: string;
  montantTotal: number;
  statut: string;
  dateCommande: string;
  nombreArticles: number;
  commandesFournisseur: CommandeFournisseurDetail[];
}

interface CommandeFournisseurDetail {
  id: number;
  reference: string;
  fournisseurId: number;
  fournisseurNom: string;
  fournisseurEmail: string;
  montantTotal: number;
  statut: string;
  dateCreation: string;
  dateLivraison?: string;
  numeroBonLivraison?: string;
  fraisLivraison: number;
  lignes: LigneCommandeFournisseurDetail[];
}

interface LigneCommandeFournisseurDetail {
  id: number;
  produitId: number;
  produitNom: string;
  referenceProduit: string;
  quantite: number;
  prixUnitaire: number;
  montantLigne: number;
}

@Component({
  selector: 'app-order-management',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './order-management.component.html',
  styleUrls: ['./order-management.component.css']
})
export class OrderManagementComponent implements OnInit {
  // Angular 19: Signals
  commandesAvecFournisseur = signal<CommandeAvecFournisseur[]>([]);
  isLoading = signal(false);
  error = signal('');
  searchTerm = signal('');
  selectedStatus = signal('');
  dateDebut = signal('');
  dateFin = signal('');
  currentPage = signal(1);
  pageSize = signal(10);
  totalItems = signal(0);
  expandedOrderId = signal<number | null>(null);



  // Computed signals pour les commandes filtrées
  filteredCommandesAvecFournisseur = computed(() => {
    const orders = this.commandesAvecFournisseur();
    const search = this.searchTerm().toLowerCase();
    const status = this.selectedStatus();
    const dateDebut = this.dateDebut();
    const dateFin = this.dateFin();

    return orders.filter(order => {
      const matchesSearch = !search || 
        order.numeroCommande.toLowerCase().includes(search) ||
        order.clientNom.toLowerCase().includes(search) ||
        order.clientEmail.toLowerCase().includes(search);

      const matchesStatus = !status || order.statut === status;

      let matchesDate = true;
      if (dateDebut || dateFin) {
        const orderDate = new Date(order.dateCommande);
        if (dateDebut) {
          const startDate = new Date(dateDebut);
          matchesDate = matchesDate && orderDate >= startDate;
        }
        if (dateFin) {
          const endDate = new Date(dateFin);
          endDate.setHours(23, 59, 59, 999);
          matchesDate = matchesDate && orderDate <= endDate;
        }
      }

      return matchesSearch && matchesStatus && matchesDate;
    });
  });

  statuts = ['EnAttente', 'Confirmee', 'EnPreparation', 'Expediee', 'Livree', 'Annulee'];

  constructor(private adminService: AdminService) {}

  ngOnInit(): void {
    this.loadCommandes();
  }

  loadCommandes(): void {
    this.isLoading.set(true);
    this.error.set('');

    // Toujours charger les commandes avec fournisseur (vue détaillée par défaut)
    this.adminService.getCommandesAvecFournisseur().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.commandesAvecFournisseur.set(response.data);
          this.totalItems.set(response.total || response.data.length);
        } else {
          this.commandesAvecFournisseur.set([]);
          this.totalItems.set(0);
        }
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des commandes avec fournisseur:', error);
        this.error.set('Erreur lors du chargement des commandes');
        this.isLoading.set(false);
      }
    });
  }

  // Méthodes simplifiées - plus besoin de basculer entre les vues
  toggleDetailedView(): void {
    // Ne fait plus rien car on reste toujours en vue détaillée
  }

  // Method called by template - alias for toggleDetailedView
  toggleView(): void {
    // Ne fait plus rien car on reste toujours en vue détaillée
  }

  onSearch(): void {
    // Le signal se met à jour automatiquement
  }

  onFilterChange(): void {
    // Le signal se met à jour automatiquement
  }

  onDateFilterChange(): void {
    // Le signal se met à jour automatiquement
  }

  clearFilters(): void {
    this.searchTerm.set('');
    this.selectedStatus.set('');
    this.dateDebut.set('');
    this.dateFin.set('');
  }

  refresh(): void {
    this.currentPage.set(1);
    this.loadCommandes();
  }

  viewOrderDetails(commande: CommandeAvecFournisseur): void {
    // Basculer l'expansion/contraction des détails de la commande
    const currentExpanded = this.expandedOrderId();
    this.expandedOrderId.set(currentExpanded === commande.id ? null : commande.id);
  }

  isOrderExpanded(orderId: number): boolean {
    return this.expandedOrderId() === orderId;
  }

  getTotalCommandes(): number {
    return this.filteredCommandesAvecFournisseur().length;
  }

  getTotalAmount(): string {
    const total = this.filteredCommandesAvecFournisseur().reduce((sum, c) => sum + c.montantTotal, 0);
    return this.formatPrice(total);
  }

  exportOrders(): void {
    alert('Fonctionnalité d\'export en cours de développement');
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('fr-TN', {
      style: 'currency',
      currency: 'TND'
    }).format(price);
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getStatusText(statut: string): string {
    switch (statut) {
      case 'EnAttente': return 'En attente';
      case 'Confirmee': return 'Confirmée';
      case 'EnPreparation': return 'En préparation';
      case 'Expediee': return 'Expédiée';
      case 'Livree': return 'Livrée';
      case 'Annulee': return 'Annulée';
      default: return statut;
    }
  }

  getStatusClass(statut: string): string {
    switch (statut) {
      case 'EnAttente': return 'status-pending';
      case 'Confirmee': return 'status-confirmed';
      case 'EnPreparation': return 'status-preparing';
      case 'Expediee': return 'status-shipped';
      case 'Livree': return 'status-delivered';
      case 'Annulee': return 'status-cancelled';
      default: return 'status-unknown';
    }
  }

  getCommandeFournisseurStatusText(statut: string): string {
    switch (statut) {
      case 'EnAttente': return 'En attente';
      case 'Confirmee': return 'Confirmée';
      case 'EnPreparation': return 'En préparation';
      case 'Expediee': return 'Expédiée';
      case 'Livree': return 'Livrée';
      case 'Annulee': return 'Annulée';
      default: return statut;
    }
  }

  getCommandeFournisseurStatusClass(statut: string): string {
    switch (statut) {
      case 'EnAttente': return 'status-pending';
      case 'Confirmee': return 'status-confirmed';
      case 'EnPreparation': return 'status-preparing';
      case 'Expediee': return 'status-shipped';
      case 'Livree': return 'status-delivered';
      case 'Annulee': return 'status-cancelled';
      default: return 'status-unknown';
    }
  }

  canCancelOrder(statut: string): boolean {
    return ['EnAttente', 'Confirmee'].includes(statut);
  }

  annulerCommande(commande: CommandeAvecFournisseur): void {
    console.log('Annuler commande ID:', commande.id);
  }

  // Pagination methods
  totalPages = computed(() => {
    return Math.ceil(this.totalItems() / this.pageSize());
  });

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      this.loadCommandes();
    }
  }

  // Math object for template
  Math = Math;
}
