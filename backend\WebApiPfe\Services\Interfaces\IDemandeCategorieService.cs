using WebApiPfe.DTOs.Admin;
using WebApiPfe.Models.Enum;
using WebApiPfe.Models.Entity;

namespace WebApiPfe.Services.Interfaces
{
    public interface IDemandeCategorieService
    {
        Task<IEnumerable<DemandeCategorieDto>> GetAllDemandesAsync();
        Task<IEnumerable<DemandeCategorieDto>> GetDemandesByFournisseurAsync(int fournisseurId);
        Task<IEnumerable<DemandeCategorieDto>> GetDemandesByStatutAsync(StatutDemande statut);
        Task<DemandeCategorieDto?> GetDemandeByIdAsync(int id);
        Task<DemandeCategorieDto> CreateDemandeAsync(CreateDemandeCategorieDto createDto, int fournisseurId);
        Task<DemandeCategorieDto> TraiterDemandeAsync(int id, TraiterDemandeCategorieDto traiterDto, int adminId);
        Task<bool> DeleteDemandeAsync(int id);
        Task<IEnumerable<Fournisseur>> GetAllFournisseursAsync();
    }
}
