﻿using WebApiPfe.DTOs.CreateDTOs;
using WebApiPfe.DTOs.ReadDTOs;
using WebApiPfe.Models.Entity;
using WebApiPfe.Models.Enum;

namespace WebApiPfe.Services.Profiles
{
    public class AuthProfile : AutoMapper.Profile
    {
        public AuthProfile()
        {
            CreateMap<ClientCreateDto, Client>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.RoleDiscriminator, opt => opt.MapFrom(_ => RoleUtilisateur.Client));

            CreateMap<FournisseurCreateDto, Fournisseur>()
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.RoleDiscriminator, opt => opt.MapFrom(_ => RoleUtilisateur.Fournisseur));

            // Mapping pour la lecture
            CreateMap<Fournisseur, FournisseurDto>()
                .IncludeBase<Utilisateur, UtilisateurReadDto>();
        }
    }
}
