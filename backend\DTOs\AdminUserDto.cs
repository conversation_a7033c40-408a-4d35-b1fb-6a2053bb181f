using System.ComponentModel.DataAnnotations;

namespace backend.DTOs
{
    /// <summary>
    /// DTO pour les utilisateurs dans l'interface admin
    /// </summary>
    public class AdminUserDto
    {
        public string Id { get; set; } = string.Empty;
        public string Nom { get; set; } = string.Empty;
        public string Prenom { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty; // "client" ou "fournisseur"
        public string Statut { get; set; } = string.Empty; // "actif", "inactif", "suspendu"
        public DateTime DateInscription { get; set; }
        public DateTime DerniereConnexion { get; set; }
        public int? Commandes { get; set; } // Pour les clients
        public int? Produits { get; set; } // Pour les fournisseurs
    }

    /// <summary>
    /// DTO pour les statistiques des utilisateurs
    /// </summary>
    public class AdminUserStatsDto
    {
        public int TotalUsers { get; set; }
        public int ClientCount { get; set; }
        public int SupplierCount { get; set; }
        public int SuspendedCount { get; set; }
    }

    /// <summary>
    /// DTO pour la mise à jour du statut d'un utilisateur
    /// </summary>
    public class UpdateUserStatusDto
    {
        [Required]
        [RegularExpression("^(actif|inactif|suspendu)$", ErrorMessage = "Le statut doit être 'actif', 'inactif' ou 'suspendu'")]
        public string Statut { get; set; } = string.Empty;
    }
}
