export interface Commande {
  id: number;
  numeroCommande: string;
  dateCommande: Date;
  statut: StatutCommande;
  montantHT: number;
  montantTTC: number;
  montantTVA: number;
  fraisLivraison: number;
  dateExpedition?: Date;
  dateLivraison?: Date;
  adresseLivraisonId: number;
  clientId: number;
  
  // Navigation properties
  client?: any;
  adresseLivraison?: any;
  detailsCommande?: DetailCommande[];
  livraison?: Livraison;
  paiement?: any;
}

export interface DetailCommande {
  id: number;
  commandeId: number;
  produitId: number;
  quantite: number;
  prixUnitaire: number;
  montantHT: number;
  montantTTC: number;
  tauxTVA: number;
  
  // Navigation properties
  produit?: any;
}

export interface CommandeFournisseur {
  id: number;
  reference: string;
  commandeClientId: number;
  fournisseurId: number;
  nomFournisseur: string;
  matriculeFiscale: string;
  dateCreation: Date;
  dateLivraison?: Date;
  fraisLivraison: number;
  statut: string;
  numeroBonLivraison?: string;
  lignesCommande: LigneCommandeFournisseurDto[];
  montantTotal: number;

  // Propriétés calculées pour l'affichage
  dateCommande?: Date; // Alias pour dateCreation
  clientNom?: string;
  clientEmail?: string;
  clientTelephone?: string;
  adresseLivraison?: any;
}

export interface LigneCommandeFournisseur {
  id: number;
  commandeFournisseurId: number;
  produitId: number;
  quantite: number;
  prixUnitaire: number;
  montantTotal: number;

  // Navigation properties
  produit?: any;
}

export interface LigneCommandeFournisseurDto {
  id: number;
  commandeId: number;
  produitId: number;
  nomProduit: string;
  referenceProduit: string;
  quantite: number;
  prixUnitaire: number;
  totalLigne: number;
  imagePrincipale?: string;

  // Alias pour compatibilité
  produitNom?: string;
  produitReference?: string;
  produitDescription?: string;
}

export interface Livraison {
  id: number;
  numeroSuivi: string;
  statut: StatutLivraison;
  dateExpedition?: Date;
  dateLivraisonPrevue?: Date;
  dateLivraisonReelle?: Date;
  transporteur?: string;
  commentaires?: string;
  commandeId: number;
  fournisseurId: number;
  
  // Navigation properties
  commande?: Commande;
  fournisseur?: any;
}

export enum StatutCommande {
  EnAttente = 'EnAttente',
  Confirmee = 'Confirmee',
  EnPreparation = 'EnPreparation',
  Expediee = 'Expediee',
  Livree = 'Livree',
  Annulee = 'Annulee'
}

export enum StatutCommandeFournisseur {
  Nouvelle = 'Nouvelle',
  Acceptee = 'Acceptee',
  EnPreparation = 'EnPreparation',
  Prete = 'Prete',
  Expediee = 'Expediee',
  Livree = 'Livree',
  Refusee = 'Refusee'
}

export enum StatutLivraison {
  EnAttente = 'EnAttente',
  EnCours = 'EnCours',
  Livree = 'Livree',
  Echec = 'Echec',
  Retournee = 'Retournee'
}
